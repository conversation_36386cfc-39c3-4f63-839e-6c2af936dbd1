package wdc.disk.ecs.apps.SFTPSend.profile;

import java.util.StringTokenizer;
import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.ProfileException;

/*
 * Field         | Primary Use                     | Critical for          | Used in Modules
--------------|--------------------------------|----------------------|----------------
columnName    | Database column mapping        | Data mapping         | DB operations
dbColumnType  | Database type definition       | DB schema            | DB operations
dbColumnLength| Column size specification      | Data validation      | DB operations
systemType    | System type mapping           | Type conversion      | Data processing
binaryLength  | Binary data size              | Binary processing    | Binary msg handling
columnNo      | Column ordering               | Data structure       | Comparison/Sorting
allowNulls    | Null validation               | Data validation      | Data validation
startIndex    | Fixed-width positioning       | Data parsing         | Data extraction
valueIndex    | Value mapping                 | Data mapping         | Data processing
 */
public class PDSMsgColProfileRecord extends ibm.disk.profiles.ProfileObject {
	private static final long serialVersionUID = 5062373946593921732L;
	private String columnName;
	private String dbColumnType;
	private int dbColumnLength;
	private String systemType;
	private int binaryLength;
	private int columnNo;
	private boolean allowNulls;
	private int startIndex;
	private int valueIndex;
		
	
	/**
	 * DBMsgIDRecord constructor comment.
	 * @param values java.lang.String
	 * @exception ibm.disk.profiles.ProfileException The exception description.
	 */
	public PDSMsgColProfileRecord(String values) throws ibm.disk.profiles.ProfileException {
		super(values);
		StringTokenizer st = new StringTokenizer(values.trim(), "|");
		int MIN_TOKENS = 9;
		if (st.countTokens() < MIN_TOKENS) {
			throw (new ProfileException("Invalid number of values: Found " + st.countTokens() + ", must be " + MIN_TOKENS));
		}
		String fieldName = null, fieldValue = null;
		try {
			fieldName = "column name";
			fieldValue = "";
			setColumnName(st.nextToken().trim());
			
			fieldName = "DB column type";
			setDbColumnType(st.nextToken().trim());
			
			fieldName = "DB column length";
			fieldValue = "must an integer value.";
			setDbColumnLength(new Integer(st.nextToken().trim()).intValue());
			
			fieldName = "System Type";
			fieldValue = "";
			setSystemType(st.nextToken().trim());
			
			fieldName = "Binary length";
			fieldValue = "must an integer value.";
			setBinaryLength(new Integer(st.nextToken().trim()).intValue());
			
			fieldName = "column No.";
			fieldValue = "must an integer value.";
			setColumnNo(new Integer(st.nextToken().trim()).intValue());
			
			fieldName = "allow nulls";
			fieldValue = "must an boolean value (T,F,Y or N).";
			fieldValue = st.nextToken().trim();
			if(fieldValue == null || "".equals(fieldValue.trim())) {
				setAllowNulls(false);
			}else {
				setAllowNulls(StringMethods.getBoolean(fieldValue));				
			}

			fieldName = "start index";
			fieldValue = "must an integer value.";
			setStartIndex(new Integer(st.nextToken().trim()).intValue());
			
			fieldName = "value index";
			fieldValue = "must an integer value.";
			setValueIndex(new Integer(st.nextToken().trim()).intValue());
			
		} catch (Exception e) {
			throw (new ProfileException("Invalid field: " + fieldName + "=" + fieldValue));
		}
	}
	
	/**
	 * This method must be implemented so that it compares the values passed
	 * for the type of comparison.
	 * Creation date: (10/16/2003 2:04:46 PM)
	 * @return boolean true if the record matches the list of objects to compare,
	 * false is they do not match.
	 * @param compareType int This is used if there are more than one type of
	 * comparisons used for the profile. 
	 * @param value java.util.Vector List of Objects to compare to this record
	 * for a match. 
	 */
	public boolean compare(int compareType, java.util.Vector vector) throws ibm.disk.profiles.ProfileException {
		String elem = null;
		try { 
			elem = ((String)vector.get(0)).trim();
		} catch (Exception e) {
			throw (new ProfileException("Invalid column value found in search Vector for "+ this.getClass().getSimpleName() +".compare()"));
		}
		
		
		if(compareType == 0 && StringMethods.wildStrCompare(Integer.valueOf(getColumnNo()).toString(), elem)) {
			return true;
		}
		
		if (compareType == 1 && StringMethods.wildStrCompare(getColumnName(),elem)) { 
				return true;
		}		
		
		return false;
	}

	public String getColumnName() {
		return columnName;
	}

	public void setColumnName(String columnName) {
		this.columnName = columnName;
	}

	public String getDbColumnType() {
		return dbColumnType;
	}

	public void setDbColumnType(String dbColumnType) {
		this.dbColumnType = dbColumnType;
	}

	public int getDbColumnLength() {
		return dbColumnLength;
	}

	public void setDbColumnLength(int dbColumnLength) {
		this.dbColumnLength = dbColumnLength;
	}

	public String getSystemType() {
		return systemType;
	}

	public void setSystemType(String systemType) {
		this.systemType = systemType;
	}

	public int getBinaryLength() {
		return binaryLength;
	}

	public void setBinaryLength(int binaryLength) {
		this.binaryLength = binaryLength;
	}

	public int getColumnNo() {
		return columnNo;
	}

	public void setColumnNo(int columnNo) {
		this.columnNo = columnNo;
	}

	public boolean isAllowNulls() {
		return allowNulls;
	}

	public void setAllowNulls(boolean allowNulls) {
		this.allowNulls = allowNulls;
	}

	public int getStartIndex() {
		return startIndex;
	}

	public void setStartIndex(int startIndex) {
		this.startIndex = startIndex;
	}

	public int getValueIndex() {
		return valueIndex;
	}

	public void setValueIndex(int valueIndex) {
		this.valueIndex = valueIndex;
	}


}

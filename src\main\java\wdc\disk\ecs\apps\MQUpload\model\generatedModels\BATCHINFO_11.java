package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for BATCHINFO_11
 */
public class BATCHINFO_11  {
    private Short spindle;
    private Integer batchid;
    private java.time.LocalDateTime batchtime;
    private String expid;
    private String lot;
    private String variety;
    private String product;
    private String prodtype;
    private String resource;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public BATCHINFO_11() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getBatchid() {
        return batchid;
    }

    public void setBatchid(Integer batchid) {
        this.batchid = batchid;
    }

    public java.time.LocalDateTime getBatchtime() {
        return batchtime;
    }

    public void setBatchtime(java.time.LocalDateTime batchtime) {
        this.batchtime = batchtime;
    }

    public String getExpid() {
        return expid;
    }

    public void setExpid(String expid) {
        this.expid = expid;
    }

    public String getLot() {
        return lot;
    }

    public void setLot(String lot) {
        this.lot = lot;
    }

    public String getVariety() {
        return variety;
    }

    public void setVariety(String variety) {
        this.variety = variety;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getProdtype() {
        return prodtype;
    }

    public void setProdtype(String prodtype) {
        this.prodtype = prodtype;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



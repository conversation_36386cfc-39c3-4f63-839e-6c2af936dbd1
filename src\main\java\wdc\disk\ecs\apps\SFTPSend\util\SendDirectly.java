package wdc.disk.ecs.apps.SFTPSend.util;

import com.jcraft.jsch.SftpException;

import ibm.disk.utility.LogStream;

public class SendDirectly implements SendMethod {

	@Override
	public void process(SendPdsToServerService service, String serverDirName, String msgId, int fileIndex, String srcFile,LogStream log)
			throws SftpException {
		System.out.println("ServerDirName:" + serverDirName);
		service.getChannel().put(srcFile, serverDirName);
	}


}

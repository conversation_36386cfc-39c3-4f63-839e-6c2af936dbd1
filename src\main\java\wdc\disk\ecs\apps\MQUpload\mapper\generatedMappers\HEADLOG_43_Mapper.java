package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.HEADLOG_43;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for HEADLOG_43
 */
public interface HEADLOG_43_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.HEADLOG AS TARGET",
        "USING (VALUES (",
        "    #{channel},",
        "    #{spindle},",
        "    #{side},",
        "    #{testnum},",
        "    #{insttime},",
        "    #{typenum},",
        "    #{headid},",
        "    #{reason},",
        "    #{dtinsert},",
        "    #{product},",
        "    #{correlid},",
        "    #{armIdent}",
        ")) AS SOURCE (",
        "    CHANNEL,",
        "    SPINDLE,",
        "    SIDE,",
        "    TESTNUM,",
        "    INSTTIME,",
        "    TYPENUM,",
        "    HEADID,",
        "    REASON,",
        "    DTINSERT,",
        "    PRODUCT,",
        "    CORRELID,",
        "    ARM_IDENT",
        ")",
        "ON TARGET.CHANNEL = SOURCE.CHANNEL AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.SIDE = SOURCE.SIDE AND TARGET.TESTNUM = SOURCE.TESTNUM AND TARGET.HEADID = SOURCE.HEADID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.INSTTIME = SOURCE.INSTTIME,",
        "    TARGET.TYPENUM = SOURCE.TYPENUM,",
        "    TARGET.REASON = SOURCE.REASON,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.PRODUCT = SOURCE.PRODUCT,",
        "    TARGET.CORRELID = SOURCE.CORRELID,",
        "    TARGET.ARM_IDENT = SOURCE.ARM_IDENT",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    CHANNEL,",
        "    SPINDLE,",
        "    SIDE,",
        "    TESTNUM,",
        "    INSTTIME,",
        "    TYPENUM,",
        "    HEADID,",
        "    REASON,",
        "    DTINSERT,",
        "    PRODUCT,",
        "    CORRELID,",
        "    ARM_IDENT",
        "  )",
        "  VALUES (",
        "    SOURCE.CHANNEL,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.SIDE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.INSTTIME,",
        "    SOURCE.TYPENUM,",
        "    SOURCE.HEADID,",
        "    SOURCE.REASON,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.PRODUCT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.ARM_IDENT",
        "  )"
    })
    void insertOrUpdate(HEADLOG_43 record);
}











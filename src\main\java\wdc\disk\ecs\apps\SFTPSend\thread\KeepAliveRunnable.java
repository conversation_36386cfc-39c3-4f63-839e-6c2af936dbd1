package wdc.disk.ecs.apps.SFTPSend.thread;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.Session;

import ibm.disk.utility.LogStream;

public class KeepAliveRunnable implements Runnable{

	private Session session;
	private Channel channel;
	private int seconds;
	
	public KeepAliveRunnable() {
		super();
	}

	public KeepAliveRunnable(Session session) {
		super();
		this.session = session;
	}
	
	public KeepAliveRunnable(Session session, Channel channel) {
		super();
		this.session = session;
		this.channel = channel;
	}
	
	public KeepAliveRunnable(Session session , int seconds) {
		super();
		this.session = session;
		this.seconds = seconds;
	}

	@Override
	public void run() {
		try {
			while (true) {
				if(this.seconds > 0) {
					Thread.sleep(this.seconds * 1000);					
				}
				
				if(session != null) {
					System.out.println("Send check alive message");
					session.sendKeepAliveMsg();					
				}
				
				if(session == null || !session.isConnected()) {
					System.out.println("Exit check alive message");
					break;
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			System.out.println("Session is detected down.");
			closeSFTP();
			
		}
	}

		
	public void closeSFTP() {
		if (channel != null) {
			try {
				if(channel.isConnected()) {
					channel.disconnect();
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			channel = null;
		}
		if (session != null) {
			try {
				if(session.isConnected()) {
					session.disconnect();
					System.out.println("Keep alive session close.");
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			session = null;
		}
	}
}

package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.SCNRINFO_40;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for SCNRINFO_40
 */
public interface SCNRINFO_40_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.SCNRINFO AS TARGET",
        "USING (VALUES (",
        "    #{scnrname},",
        "    #{boardnum},",
        "    #{axis},",
        "    #{calibvalue},",
        "    #{maxposerr},",
        "    #{idlimit},",
        "    #{odlimit},",
        "    #{scnrtype},",
        "    #{hgaloader},",
        "    #{serialnum},",
        "    #{parms},",
        "    #{spindle},",
        "    #{calibtime},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SCNRNA<PERSON>,",
        "    BOARDNU<PERSON>,",
        "    AXIS,",
        "    CALIBVALUE,",
        "    MAXPOSERR,",
        "    IDLIM<PERSON>,",
        "    ODLIM<PERSON>,",
        "    SCNRTYPE,",
        "    HGALOADER,",
        "    SERIALNUM,",
        "    PARMS,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SCNRNAME = SOURCE.SCNRNAME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CALIBTIME = SOURCE.CALIBTIME",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.BOARDNUM = SOURCE.BOARDNUM,",
        "    TARGET.AXIS = SOURCE.AXIS,",
        "    TARGET.CALIBVALUE = SOURCE.CALIBVALUE,",
        "    TARGET.MAXPOSERR = SOURCE.MAXPOSERR,",
        "    TARGET.IDLIMIT = SOURCE.IDLIMIT,",
        "    TARGET.ODLIMIT = SOURCE.ODLIMIT,",
        "    TARGET.SCNRTYPE = SOURCE.SCNRTYPE,",
        "    TARGET.HGALOADER = SOURCE.HGALOADER,",
        "    TARGET.SERIALNUM = SOURCE.SERIALNUM,",
        "    TARGET.PARMS = SOURCE.PARMS,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SCNRNAME,",
        "    BOARDNUM,",
        "    AXIS,",
        "    CALIBVALUE,",
        "    MAXPOSERR,",
        "    IDLIMIT,",
        "    ODLIMIT,",
        "    SCNRTYPE,",
        "    HGALOADER,",
        "    SERIALNUM,",
        "    PARMS,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SCNRNAME,",
        "    SOURCE.BOARDNUM,",
        "    SOURCE.AXIS,",
        "    SOURCE.CALIBVALUE,",
        "    SOURCE.MAXPOSERR,",
        "    SOURCE.IDLIMIT,",
        "    SOURCE.ODLIMIT,",
        "    SOURCE.SCNRTYPE,",
        "    SOURCE.HGALOADER,",
        "    SOURCE.SERIALNUM,",
        "    SOURCE.PARMS,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CALIBTIME,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(SCNRINFO_40 record);
}











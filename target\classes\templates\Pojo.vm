package ${package};



/**
 * Generated POJO for ${className}
 */
public class ${className}  {
    #foreach($column in $columns)
    private ${column.javaType} ${column.getCamelCaseName()};
    #end

    // Default constructor
    public ${className}() {
    }

    #foreach($column in $columns)
    public ${column.javaType} get${column.getCamelCaseName().substring(0,1).toUpperCase()}${column.getCamelCaseName().substring(1)}() {
        return ${column.getCamelCaseName()};
    }

    public void set${column.getCamelCaseName().substring(0,1).toUpperCase()}${column.getCamelCaseName().substring(1)}(${column.javaType} ${column.getCamelCaseName()}) {
        this.${column.getCamelCaseName()} = ${column.getCamelCaseName()};
    }

    #end
}



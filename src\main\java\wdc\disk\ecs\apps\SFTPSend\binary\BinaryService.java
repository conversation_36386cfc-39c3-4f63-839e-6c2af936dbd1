package wdc.disk.ecs.apps.SFTPSend.binary;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Vector;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;
import wdc.disk.ecs.apps.SFTPSend.profile.PDSMsgColProfileRecord;

public class BinaryService {
	public LogStream log;
	public DiskProfile diskProfile;
	
	public BinaryService() {
		super();
	}
	
	public BinaryService(LogStream log) {
		super();
		this.log = log;
	}

	public BinaryService(LogStream log, DiskProfile diskProfile) {
		super();
		this.log = log;
		this.diskProfile = diskProfile;
	}

	public int columnCount(DiskProfile dp) throws ProfileException {
		return dp.getAllProfileRecords().size();
	}
	
	public String parseMsg(String msg, String correlid, DiskProfile dp, String splitter) throws ProfileException {
		StringBuffer sb = new StringBuffer();
		@SuppressWarnings("unchecked")
		Vector<PDSMsgColProfileRecord> allRec = dp.getAllProfileRecords();
		for(int i=0; i<allRec.size(); i++) {
			String value = "";
			//Get record base on the column No.
			PDSMsgColProfileRecord rec = (PDSMsgColProfileRecord)dp.getRecord(Integer.valueOf(i).toString());
			if("DTINSERT".equals(rec.getColumnName().toUpperCase())) {
				value = "";
			}else if ("CORRELID".equals(rec.getColumnName().toUpperCase())) {
				value = correlid;
			}else {
				int startIndex = rec.getStartIndex();
				int endIndex = rec.getStartIndex() + rec.getBinaryLength() * 2;
				try {
//					System.out.println("Column:"+ rec.getColumnName() +".Start index:" + startIndex + ".End:" + endIndex);
					String splitData = msg.substring(startIndex, endIndex);
//					System.out.println("data: " + splitData);
					value = parse(rec.getDbColumnType(), splitData);
				} catch (StringIndexOutOfBoundsException e) {
					log.write(LogStream.WARNING_MESSAGE, e.getMessage());
					log.write(LogStream.WARNING_MESSAGE, "Column:"+ rec.getColumnName() +".Start index:" + startIndex + ".End:" + endIndex);
					return sb.toString();
				}
			}
//			System.out.println(rec.getColumnName() + " : " + value);
			sb.append(splitter);
			sb.append(value);
		}
		return sb.toString();
	}

	public String dataInsertTime() {
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
		Date date = new Date();
		return simpleDateFormat.format(date);
	}
	
	
	public String parse(String dbColumnType, String data) {
		if(dbColumnType == null || "".equals(dbColumnType.trim())){
			return "";
		}
		
		if("SMALLINT".equals(dbColumnType.trim().toUpperCase())) {
			return Integer.valueOf(ConvertorUtility.ConvertHexBytesToShort(data, true)).toString();
		}
		if("INTEGER".equals(dbColumnType.trim().toUpperCase())) {
			return Integer.valueOf(ConvertorUtility.ConvertHexBytesToInteger(data, true)).toString();
		}
		if("DOUBLE".equals(dbColumnType.trim().toUpperCase())) {
			Double double1 = Double.valueOf(ConvertorUtility.ConvertHexBytesToDouble(data, true));
			return double1.toString();
		}
		if("TIMESTAMP".equals(dbColumnType.trim().toUpperCase())) {
			Date date = ConvertorUtility.ConvertHexBytesToDateTime(data, true);
			SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
			return simpleDateFormat.format(date);
		}
		
		if("CHAR".equals(dbColumnType.trim().toUpperCase()) || "VARCHAR".equals(dbColumnType.trim().toUpperCase())) {
			String strResultString = data;
			int iStart= 0;
			int iEnd= 0;
			boolean isTruncated = false;
			for(int i=0; i<= data.length() - 2; i=i+2) {
				String tmpStr = data.substring(i, i+2);
				if(tmpStr.toUpperCase().compareTo("1F") < 0 
						||  tmpStr.toUpperCase().compareTo("7F") > 0 ){		
					if(isTruncated) {
						iEnd = i;
						strResultString = data.substring(iStart, iEnd);
						break;
					}
				}else {					
					if(!isTruncated) {
						iStart=i;
					}
					isTruncated = true;
				}

			}
			
			if(!isTruncated) {
				strResultString = "";
			}
			String str = ConvertorUtility.ConvertHexBytesToString(strResultString);
			return str;
		}
		
		return "dbtype:" + dbColumnType + " data:" + data;
	}
	
	

	public String generatePdsStringForIndividualMsg(String rawStr) throws ProfileException {
		String rawMsgId = rawStr.substring(0, 8);
		int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawMsgId, true);
		DBMsgIDRecord rec = findProfileNameFromMsgId(Integer.valueOf(msgId).toString());
		DiskProfile dProfile = getProfileRecord(Integer.valueOf(msgId).toString(), rec);
		
		String rawCorrelId = rawStr.substring(8, 16);
		int correlId = ConvertorUtility.ConvertHexBytesToInteger(rawCorrelId, true);
		
		//Skip header 
		String rawMsg = rawStr.substring(16);
		int columnCount = columnCount(dProfile);
		
		StringBuffer sb = new StringBuffer();
		sb.append(dataInsertTime());
		sb.append(" ");
		sb.append(msgId);
		sb.append(" ");
		sb.append(correlId);
		sb.append(" ");
		sb.append(columnCount);
		sb.append(" ");
		sb.append("*");
		String parseMsg = parseMsg(rawMsg, Integer.valueOf(correlId).toString(),dProfile, rec.getSplitter());
		sb.append(parseMsg);
		
		return sb.toString();
	}
	
	public String getMsgIdForIndividualMsg(String rawStr) {
		String rawMsgId = rawStr.substring(0, 8);
		int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawMsgId, true);
		return Integer.valueOf(msgId).toString();
	}
	
	public boolean isMultiMessage(String rawStr) {
		String rawMsg = rawStr.substring(0, 8);
		return "e6030000".equals(rawMsg) ? true : false ;
	}
	
	public DBMsgIDRecord findProfileNameFromMsgId(String msgId) throws ProfileException {
//		DiskProfile diskProfile = new DiskProfile(
////				this.getClass().getResource("/pdsMsgId.pro").getPath(),
//				"profiles" + File.separator + "pdsMsgId.pro",
//				"wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord", DiskProfile.KEEP_ONE);
		
		@SuppressWarnings("rawtypes")
		Vector vec = new Vector();
		vec.add(DBMsgIDRecord.COMPARE_BY_TYPE);
		vec.add(msgId);
		DBMsgIDRecord rec = (DBMsgIDRecord)diskProfile.getRecord(1,vec);
	
		return rec;
	}
	
	public ArrayList<SingleMessage> splitPackedMsgToArrayList(String rawData){
		//Skip Msg Id and Correl Id
		rawData = rawData.substring(BinaryMsgFieldLength.MSG_ID + BinaryMsgFieldLength.CORREL_ID);
		
		ArrayList<SingleMessage> packedMsgList = new ArrayList<SingleMessage>();
		boolean isInLoop = true;
		while(isInLoop) {
			SingleMessage singleMessage = new SingleMessage();
			int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.MSG_ID, PackedMsgIndex.MSG_ID + BinaryMsgFieldLength.MSG_ID), true);
			int correlid = ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.CORRELID , PackedMsgIndex.CORRELID + BinaryMsgFieldLength.CORREL_ID), true);
			int msgLength = 2 * ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.MSG_LEN, PackedMsgIndex.MSG_LEN + BinaryMsgFieldLength.MSG_LENGTH), true);
			String rawMsgContent = rawData.substring(PackedMsgIndex.MSG_CONTENT, PackedMsgIndex.MSG_CONTENT + msgLength);
			singleMessage.setMsgId(Integer.valueOf(msgId).toString());
			singleMessage.setCorrelid(Integer.valueOf(correlid).toString());
			singleMessage.setMsgLength(Integer.valueOf(msgLength).toString());
			singleMessage.setMsgContent(rawMsgContent);
			rawData = rawData.substring(PackedMsgIndex.MSG_CONTENT + msgLength );
			
			packedMsgList.add(singleMessage);
			
//			System.out.println("single msg:" + singleMessage);
//			System.out.println("Remain raw:" + rawData);
		
			if("".equals(rawData.trim())) {
				isInLoop = false;
			}
		}
		
		return packedMsgList;
	}
	
	public String generatePdsStringForSingleMsg(SingleMessage singleMessage) throws ProfileException {
		String msgId = singleMessage.getMsgId();
		DBMsgIDRecord rec = findProfileNameFromMsgId(msgId);
		DiskProfile dProfile = getProfileRecord(msgId,rec);
		
		String correlId = singleMessage.getCorrelid(); 
		String rawMsg = singleMessage.getMsgContent();
		int columnCount = columnCount(dProfile);
		
		StringBuffer sb = new StringBuffer();
		sb.append(dataInsertTime());
		sb.append(" ");
		sb.append(msgId);
		sb.append(" ");
		sb.append(correlId);
		sb.append(" ");
		sb.append(columnCount);
		sb.append(" ");
		sb.append("*");
		String parsedStr = parseMsg(rawMsg, new Integer(correlId).toString(), dProfile, rec.getSplitter());
		sb.append(parsedStr);
		
		return sb.toString();
	}

	private DiskProfile getProfileRecord(String msgId, DBMsgIDRecord rec) throws ProfileException {
//		String path = getClass().getResource("/" + msgId + "_" + profileName + ".pro").getPath();
//		path = "C:\\Users\\<USER>\\Desktop\\SftpTest\\profiles\\" +  msgId + "_" + profileName + ".pro";
		String path = "profiles/" + msgId + "_" + rec.getTableName() + ".pro";
		DiskProfile diskProfile = new DiskProfile(path, "wdc.disk.ecs.apps.SFTPSend.profile.PDSMsgColProfileRecord", DiskProfile.KEEP_ONE);
		return diskProfile;
	}
	

}

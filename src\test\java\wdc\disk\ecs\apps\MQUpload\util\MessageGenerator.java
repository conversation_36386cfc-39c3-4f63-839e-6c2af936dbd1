package wdc.disk.ecs.apps.MQUpload.util;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.ArrayList;
import java.util.List;

public class MessageGenerator{
    private static final SimpleDateFormat DATE_FORMAT = 
        new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
    
    private final ConnectionFactory factory;
    private final ScheduledExecutorService scheduler;
    private Connection connection;
    // Keep track of channels for cleanup
    private final List<Channel> activeChannels;
    
    public MessageGenerator() throws IOException, TimeoutException {
        this.factory = new ConnectionFactory();
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.activeChannels = new ArrayList<>();
        configureConnection();
        connection = factory.newConnection();
    }
    
    private void configureConnection() {
        // factory.setHost("*************");
        // factory.setPort(5672);
        // factory.setUsername("guest");
        // factory.setPassword("guest");
        // factory.setVirtualHost("/");

        factory.setHost("csf-mp-talend03.ad.shared");
        factory.setPort(5672);
        factory.setUsername("mqupload");
        factory.setPassword("upload66");
        factory.setVirtualHost("/");
    }
    
    
    private int lotCounter = 1;
    // Example data: 2025-04-16-11.18.14.000597 FTPSUM SERH 21 */TEST_LOT_1893/SERH  /YAC-1101L /8/-4.0/-4.0/2.0/2.0/92.8/15152.0/165.2/0.00/0.00/0.00/0.00/0.06/7.36/1.30/20531Y1090502R0501/DW40717Y1/20531Y1090502R0502
    // Example field: timestamp tabcode product columnCounts */lot/datafield
    private void generateAndSendMessage(Channel channel, String tabCode,  String routingKey, String message) throws IOException {
           
            
            String timestamp = DATE_FORMAT.format(new Date());
            String lot = String.format("TEST_LOT_%d", lotCounter++);
            String product = "SERH"; // Example product, can be made configurable
            int columnCounts = 21; // Example column count, can be made configurable
            
            String fullMessage = String.format("%s %s %s %d */%s/%s",
                timestamp, tabCode, product, columnCounts, lot, message);
            channel.basicPublish(
                "ecs.direct",    // exchange
                routingKey,      // routing key
                null,            // properties
                fullMessage.getBytes()
            );

            System.out.println("Sent message: " + fullMessage);
    
    }

    // Example usage
    public void start(String tabcode, String routingKey, String message) {
        try {
            Channel dedicatedChannel = connection.createChannel();
            // Add to our tracking list
            activeChannels.add(dedicatedChannel);
            
            // Declare exchange
            dedicatedChannel.exchangeDeclare("ecs.direct", "direct", true);
            
            scheduler.scheduleAtFixedRate(
                () -> {
                    try {
                        generateAndSendMessage(dedicatedChannel, tabcode, routingKey, message);
                    } catch (IOException e) {
                        System.err.println("Error generating message: " + e.getMessage());
                    }
                }, 
                0, 1, TimeUnit.MILLISECONDS
            );
            
            System.out.println("Message generator started for " + tabcode);
        } catch (Exception e) {
            System.err.println("Error starting generator: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    public void stop() {
        try {
            // First shutdown the scheduler to stop generating messages
            scheduler.shutdown();
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
            
            // Then close all channels
            for (Channel channel : activeChannels) {
                try {
                    if (channel != null && channel.isOpen()) {
                        channel.close();
                        System.out.println("Closed channel successfully");
                    }
                } catch (Exception e) {
                    System.err.println("Error closing channel: " + e.getMessage());
                }
            }
            activeChannels.clear();
            
            // Finally close the connection
            if (connection != null && connection.isOpen()) {
                connection.close();
                System.out.println("Closed connection successfully");
            }
            
            System.out.println("Message generator stopped completely");
        } catch (Exception e) {
            System.err.println("Error stopping generator: " + e.getMessage());
            e.printStackTrace();
        }
    }


    
    
    public static void main(String[] args) throws IOException, TimeoutException {
        MessageGenerator generator = new MessageGenerator();
        /*
         *     {"tabCode": "LID", "mqMessageId": "309", "msgType": "ASCII", "tableName": "LID"},
               {"tabCode": "FTPRAW", "mqMessageId": "222", "msgType": "ASCII", "tableName": "FTP_RAW"},
               {"tabCode": "FTPSUM", "mqMessageId": "223", "msgType": "ASCII", "tableName": "FTP_SUM"}
         */
        generator.start("LID", "309", "SERH  /YAC-1101L /8/-4.0/-4.0/2.0/2.0/92.8/15152.0/165.2/0.00/0.00/0.00/0.00/0.06/7.36/1.30/20531Y1090502R0501/DW40717Y1/20531Y1090502R0502\r\n");
        
        generator.start("FTPRAW", "222", "SERH  /YAC-1001L /1/0.0/0.0/150.0/150.0/64.1/3268.0/155.00\r\n");
        generator.start("FTPSUM", "223", "SERH  /YAC-1101L /8/-4.0/-4.0/2.0/2.0/92.8/15152.0/165.2/0.00/0.00/0.00/0.00/0.06/7.36/1.30/20531Y1090502R0501/DW40717Y1/20531Y1090502R0502\r\n");     
        
        // Run for 5 minutes then stop
        try {
            Thread.sleep(TimeUnit.MINUTES.toMillis(5));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        } finally {
            generator.stop();
        }
    }
}

package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.MONRSLT_19;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for MONRSLT_19
 */
public interface MONRSLT_19_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.MONRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{product},",
        "    #{diskcode},",
        "    #{disksequence},",
        "    #{side},",
        "    #{testnum},",
        "    #{magrule},",
        "    #{magheadid},",
        "    #{batchtime},",
        "    #{dataindex},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    PRODUCT,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    SIDE,",
        "    TESTNUM,",
        "    MAGRU<PERSON>,",
        "    MAGHEADID,",
        "    BATCHTIME,",
        "    DATAINDEX,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.PRODUCT = SOURCE.PRODUCT AND TARGET.DISKCODE = SOURCE.DISKCODE AND TARGET.DISKSEQUENCE = SOURCE.DISKSEQUENCE AND TARGET.SIDE = SOURCE.SIDE AND TARGET.TESTNUM = SOURCE.TESTNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.MAGRULE = SOURCE.MAGRULE,",
        "    TARGET.MAGHEADID = SOURCE.MAGHEADID,",
        "    TARGET.BATCHTIME = SOURCE.BATCHTIME,",
        "    TARGET.DATAINDEX = SOURCE.DATAINDEX,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    PRODUCT,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    SIDE,",
        "    TESTNUM,",
        "    MAGRULE,",
        "    MAGHEADID,",
        "    BATCHTIME,",
        "    DATAINDEX,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.PRODUCT,",
        "    SOURCE.DISKCODE,",
        "    SOURCE.DISKSEQUENCE,",
        "    SOURCE.SIDE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.MAGRULE,",
        "    SOURCE.MAGHEADID,",
        "    SOURCE.BATCHTIME,",
        "    SOURCE.DATAINDEX,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(MONRSLT_19 record);
}











* Table: DB2SYS.HEADLOG(BINARY format)
*| ColumnName       | DBColType | DBColLen | SysType       | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
| CHANNEL          | Char      | 8        | System.String | 8         | 0     | Y     | 0        | 0        | Y      |
| SPINDLE          | SmallInt  | 2        | System.Int16  | 2         | 1     | Y     | 20       | 1        | Y      |
| SIDE             | Char      | 1        | System.String | 1         | 2     | Y     | 24       | 2        | Y      |
| TESTNUM          | Integer   | 4        | System.Int32  | 4         | 3     | Y     | 32       | 3        | Y      |
| INSTTIME         | Timestamp | 4        | System.DateTime| 4        | 4     |       | 64       | 4        |        |
| TYPENUM          | SmallInt  | 2        | System.Int16  | 2         | 5     |       | 72       | 5        |        |
| HEADID           | Char      | 20       | System.String | 11        | 6     | Y     | 40       | 6        | Y      |
| REASON           | Char      | 40       | System.String | 11        | 7     |       | 80       | 7        |        |
| DTINSERT         | Timestamp | 4        | System.DateTime| 4        | 8     | Y     | 0        | 8        |        |
| PRODUCT          | Integer   | 4        | System.Int32  | 2         | 9     |       | 76       | 9        |        |
| CORRELID         | Integer   | 4        | System.Int32  | 4         | 10    |       | 0        | 10       |        |
| ARM_IDENT        | VarChar   | 8        | System.String | 8         | 11    |       | 102      | 11       |        |

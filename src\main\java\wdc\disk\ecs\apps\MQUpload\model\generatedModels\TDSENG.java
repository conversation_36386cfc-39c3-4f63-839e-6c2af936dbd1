package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSENG
 */
public class TDSENG  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private Integer spindle;
    private String rawdataFile;
    private String engKey1;
    private String engVal1;
    private String engKey2;
    private String engVal2;
    private String engKey3;
    private String engVal3;
    private String engKey4;
    private String engVal4;
    private String engKey5;
    private String engVal5;

    // Default constructor
    public TDSENG() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getRawdataFile() {
        return rawdataFile;
    }

    public void setRawdataFile(String rawdataFile) {
        this.rawdataFile = rawdataFile;
    }

    public String getEngKey1() {
        return engKey1;
    }

    public void setEngKey1(String engKey1) {
        this.engKey1 = engKey1;
    }

    public String getEngVal1() {
        return engVal1;
    }

    public void setEngVal1(String engVal1) {
        this.engVal1 = engVal1;
    }

    public String getEngKey2() {
        return engKey2;
    }

    public void setEngKey2(String engKey2) {
        this.engKey2 = engKey2;
    }

    public String getEngVal2() {
        return engVal2;
    }

    public void setEngVal2(String engVal2) {
        this.engVal2 = engVal2;
    }

    public String getEngKey3() {
        return engKey3;
    }

    public void setEngKey3(String engKey3) {
        this.engKey3 = engKey3;
    }

    public String getEngVal3() {
        return engVal3;
    }

    public void setEngVal3(String engVal3) {
        this.engVal3 = engVal3;
    }

    public String getEngKey4() {
        return engKey4;
    }

    public void setEngKey4(String engKey4) {
        this.engKey4 = engKey4;
    }

    public String getEngVal4() {
        return engVal4;
    }

    public void setEngVal4(String engVal4) {
        this.engVal4 = engVal4;
    }

    public String getEngKey5() {
        return engKey5;
    }

    public void setEngKey5(String engKey5) {
        this.engKey5 = engKey5;
    }

    public String getEngVal5() {
        return engVal5;
    }

    public void setEngVal5(String engVal5) {
        this.engVal5 = engVal5;
    }

}



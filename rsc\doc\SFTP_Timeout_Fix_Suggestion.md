# SFTP Timeout Issue: Root Cause Analysis and Fix Suggestion

## Root Cause Analysis

The SFTP timeout issue occurs in the `MonitorSftpSendRunnable.java` class due to a logical flaw in the `isUnprocessPdsQtyExceeded` method and how it interacts with the `needToCreateNewThread` method.

### The Problem

When examining the logs showing `isTimeOut:true` but `needToCreateNewThread:false`, the issue lies in this logic:

```java
public boolean needToCreateNewThread(boolean isTimeout, boolean isUnprocessPdsQtyExceeded, boolean isSendingTimeout) {
    return (isTimeout && isUnprocessPdsQtyExceeded) || isSendingTimeout; 
}
```

The `isUnprocessPdsQtyExceeded` is calculated by the `getFileQtyOfFolder` method, but there's no `isUnprocessPdsQtyExceeded` method visible in the current code. However, based on the configuration and similar patterns in `MonitorTalendStatusRunnable.java`, the logic likely compares file count against `unprocessLimit` (default = 1).

### The Critical Flaw

When there is exactly 1 file to process:
- `isTimeout` = `true` (timeout occurred)
- `isUnprocessPdsQtyExceeded` = `false` (because 1 <= 1, not exceeding limit)
- `isSendingTimeout` = `false` (no sending timeout)
- Result: `(true && false) || false` = `false`

This prevents thread restart even when a timeout has occurred with pending files.

## Suggested Fix

### Option 1: Modify the Logic (Recommended)

The `needToCreateNewThread` method should restart the thread when there's a timeout AND there are files to process, not just when the limit is exceeded.

```java
public boolean needToCreateNewThread(boolean isTimeout, boolean hasUnprocessedFiles, boolean isSendingTimeout) {
    return (isTimeout && hasUnprocessedFiles) || isSendingTimeout; 
}
```

And modify the file count check to return true when there are any files:

```java
public boolean hasUnprocessedFiles(int fileCount) {
    return fileCount > 0;
}
```

### Option 2: Adjust the Limit Check

If keeping the current structure, change the comparison to be inclusive:

```java
public boolean isUnprocessPdsQtyExceeded(int unprocessLimit, int dirQty) {
    return dirQty >= unprocessLimit; // Changed from dirQty > unprocessLimit
}
```

## Implementation Steps

1. **Locate the missing `isUnprocessPdsQtyExceeded` method** in `MonitorSftpSendRunnable.java`
2. **Implement Option 1** (recommended) or **Option 2**
3. **Update the method call** in the main monitoring loop
4. **Test thoroughly** with 1 file scenarios

## Configuration Note

The `FILE_COUNT_LIMIT` parameter in the profile defaults to 1, which is appropriate for the fix. No configuration changes needed.

## Testing Scenarios

1. **Single file timeout**: Place 1 file, wait for timeout, verify thread restarts
2. **Multiple file timeout**: Place multiple files, wait for timeout, verify thread restarts  
3. **No timeout**: Verify normal operation continues without false restarts
4. **Sending timeout**: Verify `isSendingTimeout` logic still works independently

This fix will resolve the issue where the SFTP process hangs when there's exactly one file to process and a timeout occurs.
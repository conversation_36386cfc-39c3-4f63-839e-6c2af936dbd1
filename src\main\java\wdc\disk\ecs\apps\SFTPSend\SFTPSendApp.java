package wdc.disk.ecs.apps.SFTPSend;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.FilenameFilter;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.RandomAccessFile;
import java.net.URL;
import java.net.URLConnection;
import java.net.UnknownHostException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Properties;
import java.util.StringTokenizer;
import java.util.Vector;
import java.util.function.Consumer;
import java.util.stream.Collectors;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

import ibm.disk.cfgreader.ConfigReader;
import ibm.disk.cfgreader.ParameterRecord;
import ibm.disk.cfgreader.ReadEcsIniException;
import ibm.disk.extensions.ColoredString;
import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.ConfigurationProperties;
import wdc.disk.ecs.apps.MQUpload.service.ConfigurationLoader;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryService;
import wdc.disk.ecs.apps.SFTPSend.binary.SingleMessage;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;
import wdc.disk.ecs.apps.SFTPSend.properties.SFTPServerProperties;
import wdc.disk.ecs.apps.SFTPSend.thread.KeepAliveRunnable;
import wdc.disk.ecs.apps.SFTPSend.thread.MonitorSftpSendRunnable;
import wdc.disk.ecs.apps.SFTPSend.thread.RemoveFileThread;
import wdc.disk.ecs.apps.SFTPSend.util.CompareTwoFileSequence;
import wdc.disk.ecs.apps.SFTPSend.util.CreateShareFolderMode;
import wdc.disk.ecs.apps.SFTPSend.util.FileEntity;
import wdc.disk.ecs.apps.SFTPSend.util.FileProcessingUtil;
import wdc.disk.ecs.apps.SFTPSend.util.FileSortUtil;
import wdc.disk.ecs.apps.SFTPSend.util.FolderLastCreatedUtil;
import wdc.disk.ecs.apps.SFTPSend.util.RecordMsgIdService;
import wdc.disk.ecs.apps.SFTPSend.util.SendPdsToServerService;

/**
 *********************************************************************
 *                                                                   *
 *  SFTPSendEcsApp	                                                 *
 *                                                                   *
 *********************************************************************
 * Sample Profile in ecs.ini:
 * MODEL {
 *    MODELKEY=SFTPSend,
 *    CLASS=wdc.disk.ecs.apps.SFTPSend.SFTPSendEcsApp,
 *    PARAMETERS=SFTPSend DEBUG,    // block name with all the parameters to start the application
 * }
 * 
 * SFTPSend {
 *	   PDS_DIR=/opt/ecs/storage/mq/sent, // File directory where PDS files are saved
 *	   SFTP_SERVER_DIR=/D:/ECS_FTP/InputFiles/POC/DECO/, // The file directory in SFTP server where PDS files will be sent
 *	   PRIMARY_SFTP_SERVER=csf-mp-talend02.ad.shared:22:sftpadm:sftp@123456,
 *	   BACKUP_SFTP_SERVER=csf-mp-talend01.ad.shared:22:sftpadm:sftp@123456,
 *	   DB_MSG_ID_PROFILE=/opt/ecs/profiles/dbmsgid.pro,
 *	   BINARY_FORMAT, //optional, default is N. N--PDS file is ascii-encoded data, Y--PDS file is binary data
 *	   MAX_READ_FILES, // optional, default is 1000. Limit the number of files JVM read one time to avoid OOM
 *	   IDLE_SEND_INTERVAL, // optional, default is 60, units-seconds. Interval time after last send in idle mode
 *	   WAITING_FOR_WRITE_ENDED, // optional, default is 10, units-seconds. The time of waiting for other ECS application writes PDS file ended
 *	   CHANGE_TO_IDLE, // optional, default is 120, units-seconds. No data to send for specified time, the app will change to idle mode
 *	   MAX_LINE_LENGTH, // optional, default is 4096. If the length of data line is more than specified length, not send to SFTP, but write to problem file
 *	   RETENTION_HOURS=144, //optional, default is 144, units-hours. The time of retaining the sent data files, default is 7 days
 * }    
 * @Date 2021-08-02 16:42:55
 * <AUTHOR>
 */
public class SFTPSendApp implements Runnable, FilenameFilter {
	private String mainProfile;
	private long mainProfileLastModifyDate;
	private String blockName;
	private String errorTitle = "SFTPSend Error";
	
	private int loglevel = LogStream.INFORMATION_MESSAGE;
	private ibm.disk.utility.LogStream log;
	private ibm.disk.utility.LogStream logRemove;
	private ibm.disk.utility.LogStream logRemoveForBackup;
	
	// File directory where PDS files are saved
	private java.lang.String pdsDirName;
	private java.lang.String sendingDirName;
	private java.lang.String sentDirName;
	private java.lang.String problemDirName;
	private java.lang.String backupSrcPdsDirName;
	private java.lang.String sftpDir;
	
	private java.io.File pdsDir;
	private java.io.File sendingDir;
	private java.io.File sentDir;
	private java.io.File problemDir;
	private java.io.File backupSrcPdsDir;
	
	
	private java.lang.String dbMsgIDProfileName;
	private ibm.disk.profiles.DiskProfile dbMsgIDProfile;
	
	// default retention of sent data is 7 days
	private int hoursOfRetention = 144;
	private RemoveFileThread removeFileThread;
	private RemoveFileThread removeBackupSrcFileThread;
	private Thread sftpSendThread;
	
	// false--ECS is running, true--ECS is exiting
	private boolean exiting = false;
	
	protected transient java.beans.PropertyChangeSupport propertyChange;
	private ibm.disk.extensions.ColoredString fieldMessage = new ibm.disk.extensions.ColoredString();
	
	// false--PDS file is ascii-encoded data, true--PDS file is binary data
	private boolean binaryFormat = false;
	private boolean packSameMsgIdData = false;
	private boolean isRecordParsedPdsFileDetail = false;
	
	private long lastSendTime;
	// No data to send for specified time, the app will change to idle mode
	private int changeToIdle = 120000;
	// Interval time after last send in idle mode, default is 1 min
	private int idleSendInterval = 60000;
	
	// The time of waiting for other ECS application writes PDS file ended, default is 10 sec
	private int waitingForWriteEnded = 10000;
	
	// If the length of data line is more than specified length, not send to SFTP, but write to problem file
	private int maxLineLength = 4096;
	
	// Used in the method--accept() of this class, to count the number of files in directory
	private long fileCount = 0;
	
	// Limit the number of files JVM read one time to avoid OOM
	private int maxReadFiles = 1000;
	
	// The file directory in SFTP server where PDS files will be sent
	private java.lang.String sftpServerDirName;
	
	private SFTPServerProperties primarySFTPServer;
	private SFTPServerProperties backupSFTPServer;
	private SFTPServerProperties currentSFTPServer;
	
	private Session session = null;
	private ChannelSftp channel = null;
	private String sftpConnected = "N";

	// Used in new file name to avoid conflict with file from other ECS host in SFTP server 
	private java.lang.String machineName;
	private RecordMsgIdService recordMsgIdService;
	private String sendByMsgIdModeString;
	private SendPdsToServerService sendPdsToServerService;
	private String archiveByHour;
	private int keepAliveMsgSeconds;
	private boolean needDownloadProfile = false;
	private String downloadProfilePath;
	private CreateShareFolderMode createShareFolderMode = CreateShareFolderMode.uploading;
	private Date recordInstant;
	private int downloadIntervalMinutes;    
	
	
	/**
	 * SFTPSendEcsApp constructor comment.
	 * @param ecs2000 ibm.disk.ecs.ecs2000.Ecs2000
	 * @param name java.lang.String
	 */
	public SFTPSendApp() {
		super();
	}
	
	
	public void initialize(String profile, String blockName) throws SFTPSendException {
        mainProfile = profile;
        
		ConfigReader cr = null;
		try {
			cr = new ConfigReader(profile);	
		} catch (ReadEcsIniException e1) {
			String msg = "Could not read profile " + profile + e1.toString();
			writeToLog(LogStream.ERROR_MESSAGE,msg);
			throw new SFTPSendException(msg);
		} catch (NullPointerException e2) {
			String msg = "No configuration file specified";
			writeToLog(LogStream.ERROR_MESSAGE,msg);
			throw new SFTPSendException(msg);
		}
		
		String[] hostname;
		try {
			hostname = java.net.InetAddress.getLocalHost().getHostName().toUpperCase().split("\\.");
			machineName = hostname[0];
		} catch (UnknownHostException e) {
			logAndShowError(e);
			return;
		}
		
		try {
			java.rmi.registry.LocateRegistry.createRegistry(1099);
			LogStream.startRMI();
		} catch (Throwable e) {
		}
				
		if (!readProfile(profile , blockName)) {
			return;
		}
		
		try {
			removeFileThread = new RemoveFileThread(sentDirName, hoursOfRetention, logRemove);
		} catch (IOException e) {
			logAndShowError(e);
			return;
		}
		
		try {
			if(backupSrcPdsDirName != null && !backupSrcPdsDirName.trim().isEmpty()) {
				removeBackupSrcFileThread = new RemoveFileThread(backupSrcPdsDirName, hoursOfRetention, logRemoveForBackup);				
			}
		} catch (IOException e) {
			logAndShowError(e);
			return;
		}
		
		sftpSendThread = new Thread(this, blockName);
		sftpSendThread.start();
		
	}

	
	/**
	 * Sample Profile in ecs.ini:
	 * SFTPSend {
	 *	   PDS_DIR=/opt/ecs/storage/mq/sent, // File directory where PDS files are saved
	 *	   SFTP_SERVER_DIR=/D:/ECS_FTP/InputFiles/POC/DECO/, // The file directory in SFTP server where PDS files will be sent
	 *	   PRIMARY_SFTP_SERVER=csf-mp-talend02.ad.shared:22:sftpadm:sftp@123456,
	 *	   BACKUP_SFTP_SERVER=csf-mp-talend01.ad.shared:22:sftpadm:sftp@123456,
	 *	   DB_MSG_ID_PROFILE=/opt/ecs/profiles/dbmsgid.pro,
	 *	   BINARY_FORMAT, //optional, default is N. N--PDS file is ascii-encoded data, Y--PDS file is binary data
	 *	   MAX_READ_FILES, // optional, default is 1000. Limit the number of files JVM read one time to avoid OOM
	 *	   IDLE_SEND_INTERVAL, // optional, default is 60, units-seconds. Interval time after last send in idle mode
	 *	   WAITING_FOR_WRITE_ENDED, // optional, default is 10, units-seconds. The time of waiting for other ECS application writes PDS file ended
	 *	   CHANGE_TO_IDLE, // optional, default is 120, units-seconds. No data to send for specified time, the app will change to idle mode
	 *	   MAX_LINE_LENGTH, // optional, default is 4096. If the length of data line is more than specified length, not send to SFTP, but write to problem file
	 *	   RETENTION_HOURS=144, //optional, default is 144, units-hours. The time of retaining the sent data files, default is 7 days
	 * }
	 * Author: Enhua
	 * Date: 2021-08-11 10:02:46
	 * @param profile
	 * @param blockname
	 * @return
	 */
	public boolean readProfile(String profile, String blockname) {
		this.setBlockName(blockname);
	
		
		ConfigReader cr = null;
		try {
			cr = new ConfigReader(profile);
		} catch (Exception e) {
			logAndShowError(e);
			return false;
		}
		
		// Get the general configurations for SFTPSend
		cr.startGroup(blockname);
		Vector mqAttrib = null;
		if (cr.hasMoreGroups()) {
			mqAttrib = cr.nextGroup();
			for (int i = 0; i < mqAttrib.size();i++) {
				ParameterRecord rec = (ParameterRecord)mqAttrib.get(i);
				if (rec.getKey().equals ("PDS_DIR")) {
					String temp = rec.getValue().trim();
					if (!temp.endsWith(File.separator)) {
						temp += File.separator;
					}
					pdsDirName = temp;
				} else if (rec.getKey().equals("SFTP_SERVER_DIR")) {
					sftpServerDirName = rec.getValue().trim();
				} else if (rec.getKey().equals("PRIMARY_SFTP_SERVER")) {
					try {
						primarySFTPServer = new SFTPServerProperties(rec.getValue().trim());
						setCurrentSFTPServer(primarySFTPServer);
					} catch (ProfileException e) {
						logAndShowError(e);
						return false;
					}
				} else if (rec.getKey().equals("BACKUP_SFTP_SERVER")) {
					try {
						backupSFTPServer = new SFTPServerProperties(rec.getValue().trim());
					} catch (ProfileException e) {
						logAndShowError(e);
						return false;
					}
				} else if (rec.getKey().equals ("DB_MSG_ID_PROFILE")) {
					dbMsgIDProfileName = rec.getValue().trim();		
				} else if (rec.getKey().equals ("BINARY_FORMAT")) {
					binaryFormat = StringMethods.getBoolean(rec.getValue().trim());
				} else if (rec.getKey().equals ("MAX_READ_FILES")) {	
					try {
						maxReadFiles  = Integer.parseInt(rec.getValue());
					} catch (Exception e) {
						logAndShowError(e);
						return false;
					}					
				} else if (rec.getKey().equals ("MAX_LINE_LENGTH")) {	
					try {
						maxLineLength  = Integer.parseInt(rec.getValue());
					} catch (Exception e) {
						logAndShowError(e);
						return false;
					}					
				} else if (rec.getKey().equals ("IDLE_SEND_INTERVAL")) {
					try {
						idleSendInterval = Integer.parseInt(rec.getValue()) * 1000;
					} catch (Exception e) {
						logAndShowError(e);
						return false;
					}
				} else if (rec.getKey().equals ("WAITING_FOR_WRITE_ENDED")) {
					try {
						waitingForWriteEnded = Integer.parseInt(rec.getValue()) * 1000;
					} catch (Exception e) {
						logAndShowError(e);
						return false;
					}
				} else if (rec.getKey().equals ("CHANGE_TO_IDLE")) {
					try {
						changeToIdle = Integer.parseInt(rec.getValue()) * 1000;
					} catch (Exception e) {
						logAndShowError(e);
						return false;
					}
				}else if (rec.getKey().equals ("RETENTION_HOURS")) {
					try {
						hoursOfRetention = Integer.parseInt(rec.getValue());
					} catch (Exception e) {
						logAndShowError(e);
						return false;
					}
				}else if (rec.getKey().equals ("LOG_LEVEL")) {
						try {
							loglevel = Integer.parseInt(rec.getValue());
						} catch (Exception e) {}   // defaults to log level information
				}else if (rec.getKey().equals ("LOG_FILE")) {
					String logFile = rec.getValue();
					if (logFile != null) {
						log = new LogStream(logFile);
						logRemove = new LogStream(logFile + "Remove");
						logRemoveForBackup = new LogStream(logFile + "RemoveForBackup");
						writeToLog(LogStream.HILITE_INFO_MESSAGE,blockname + " started");
					}
				}else if(rec.getKey().equals("BACK_UP_SOURCE_PDS_DIR")) {
					String temp = rec.getValue().trim();
					if (!temp.endsWith(File.separator)) {
						temp += File.separator;
					}
					backupSrcPdsDirName = temp;
				}else if(rec.getKey().equals("PACK_SAME_MSG_ID_DATA")) {
					String temp = rec.getValue().trim();
					if(temp == null || temp.isEmpty()) {
						packSameMsgIdData = false;
					}else {
						packSameMsgIdData = StringMethods.getBoolean(temp);						
					}
				}else if(rec.getKey().equals("RECORD_MSG_ID_PATH")) {
					String filePath = rec.getValue().trim();
					if(filePath!=null && !"".equals(filePath)) {
						try {
							recordMsgIdService = new RecordMsgIdService(machineName ,filePath);
						} catch (IOException e) {
							e.printStackTrace();
						}
					}
				}else if(rec.getKey().equals("RECORD_MSG_DETAIL")) {
					String temp = rec.getValue().trim();
					if(temp == null || temp.isEmpty()) {
						isRecordParsedPdsFileDetail = false;
					}else {
						isRecordParsedPdsFileDetail = StringMethods.getBoolean(temp);						
					}
				}else if(rec.getKey().equals("SENT_BY_MSG_ID_MODE")) {
					sendByMsgIdModeString = rec.getValue().trim();
				}else if(rec.getKey().equals("SFTP_DIR")) {
					String temp = rec.getValue().trim();
					if(temp != null && !"".equals(temp)) {
						if (!temp.endsWith(File.separator)) {
							temp += File.separator;
						}
						sftpDir = temp;						
					}
				}else if(rec.getKey().equals("ARCHIVE_BY_HOUR")) {
					String temp = rec.getValue().trim();
					if(temp != null && !temp.isEmpty()) {
						archiveByHour = temp;						
					}
				}else if(rec.getKey().equals("KEEP_ALIVE_MSG_SECONDS")) {
					String temp = rec.getValue().trim();
					if(temp != null && !temp.isEmpty()) {
						keepAliveMsgSeconds = Integer.parseInt(temp);						
					}else {
						keepAliveMsgSeconds = -1;
					}
				}else if(rec.getKey().equals("NEED_DOWNLOAD_PROFILE")) {
					String temp = rec.getValue().trim();
					if(temp != null && !temp.isEmpty()) {
						needDownloadProfile = StringMethods.getBoolean(temp);
					}
				}else if(rec.getKey().equals("DOWNLOAD_PROFILE_PATH")) {
					String temp = rec.getValue().trim();
					if(temp != null && !temp.isEmpty()) {
						downloadProfilePath = temp;
					}
				}else if(rec.getKey().equals("CREATE_SHARE_FOLDER_MODE")) {
					String temp = rec.getValue().trim();
					if(temp != null && !temp.isEmpty()) {
						int mode = Integer.parseInt(temp);
						if(mode == 0) {
							createShareFolderMode = CreateShareFolderMode.initialize;
						}else if(mode == 1) {
							createShareFolderMode = CreateShareFolderMode.uploading;
						}
					}else {
						createShareFolderMode = CreateShareFolderMode.uploading;
					}
				}else if(rec.getKey().equals("DOWNLOAD_INTERVAL_MINUTES")) {
					String temp = rec.getValue().trim();
					if(temp != null && !temp.isEmpty()) {
						downloadIntervalMinutes = Integer.parseInt(temp);
					}else {
						downloadIntervalMinutes = 10;
					}
				}
				
			}
		} else {
			logError("Unable to find block name <" + blockname + "> in " + profile);
			return false;
		}
		
		File mainProfile = new File(profile);
		if(mainProfile.exists()) {
			setMainProfileLastModifyDate(mainProfile.lastModified());
		}
		
		log.setLogLevel(loglevel);
		logRemove.setLogLevel(loglevel);
		System.out.println("Log_level is " + loglevel);
		
		// File directory where PDS files are saved
		if ((pdsDirName == null) || (pdsDirName.length() == 0)) {
			logError("Unable to get field 'PDS_DIR' from the <" + blockname + "> block in " + profile);
			return false;
		} else {
			pdsDir = new File(pdsDirName);
			setPdsDirName(pdsDir.getPath() + File.separator);
			
			// Create the file directory--/sending
			if(sftpDir != null) {
				sendingDirName = sftpDir + "sending" + File.separator;				
			}else {
				sendingDirName = getPdsDirName() + "sending" + File.separator;
			}
			try {
				sendingDir = new File(sendingDirName);
				log.write("Create sending dir:" + sendingDirName);
				sendingDir.mkdirs();
			} catch (Exception e) {
				logError(e);
				logError("Unable to create subdirectory: " + sendingDirName);
				return false;
			}
			
			// Create the file directory--/sent
			if(sftpDir != null) {
				sentDirName = sftpDir + "sent" + File.separator;
			}else {
				sentDirName =  getPdsDirName() + "sent" + File.separator;				
			}
			try {
				sentDir = new File(sentDirName);
				if(!sentDir.exists()) {
					log.write("Create sent dir:" + sentDirName);
					boolean mkdirsResult = sentDir.mkdirs();
					if(!mkdirsResult) {
						log.write(LogStream.ERROR_MESSAGE, "Create dir:" + sentDirName + " failed.");
					}					
				}else {
					log.write(LogStream.INFORMATION_MESSAGE, "Sent dir already exist." + sentDir.getAbsolutePath());
				}
			} catch (Exception e) {
				logError(e);
				logError("Unable to create subdirectory: " + sentDirName);
				return false;
			}
			
			// Create the file directory--/problem
			if(sftpDir != null) {
				problemDirName = sftpDir + "problem" + File.separator;								
			}else {
				problemDirName =  getPdsDirName() + "problem" + File.separator;								
			}

			try {	
				problemDir = new File(problemDirName);
				if(!problemDir.exists()) {
					log.write("Create dir:" + problemDirName);
					boolean mkdirsResult = problemDir.mkdirs();
					if(!mkdirsResult) {
						log.write(LogStream.ERROR_MESSAGE, "Create dir:" + problemDirName + " failed.");
					}					
				}else {
					log.write(LogStream.INFORMATION_MESSAGE, "Problem dir already exist." + problemDir.getAbsolutePath());
				}
			} catch (Exception e) {
				logError(e);
				logError("Unable to create subdirectory: " + problemDirName);
				return false;
			}
			
			// Create the file directory--/problem
			try {	
				if(backupSrcPdsDirName != null) {
					backupSrcPdsDir = new File(backupSrcPdsDirName);
					backupSrcPdsDir.mkdirs();					
				}
			} catch (Exception e) {
				logError(e);
				logError("Unable to create subdirectory: " + backupSrcPdsDirName);
				return false;
			}
			
		}
		
		// File directory of SFTP server where PDS files are sent
		if ((sftpServerDirName == null) || (sftpServerDirName.length() == 0)) {
			logError("Unable to get field 'SFTP_SERVER_DIR' from the <" + blockname + "> block in " + profile);
			return false;
		}
		
		// Primary server of SFTP, must be configured
		if (primarySFTPServer == null) {
			logError("Unable to get field 'PRIMARY_SFTP_SERVER' from the <" + blockname + "> block in " + profile);
			return false;
		}
		
		if ((dbMsgIDProfileName == null) || (dbMsgIDProfileName.length() == 0)) {
			logError("Unable to get field 'DB_MSG_ID_PROFILE' from the <" + blockname + "> block in " + profile);
			return false;
		} else {
			try {
				dbMsgIDProfile = new DiskProfile(dbMsgIDProfileName, "wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord", DiskProfile.KEEP_ONE, log);
			} catch (ProfileException e) {
				logError(e);
				return false;
			}
		}

		return(true);
	}
	
	/**
	 * Create PDS record
	 * Author: Enhua
	 * Date: 2021-08-10 16:21:43
	 * @param cls
	 * @return
	 * @throws IOException
	 */
	public String getCompileTimeStamp(Class cls) throws IOException {
	   ClassLoader loader = cls.getClassLoader();
	   String filename = cls.getName().replace('.', '/') + ".class";
	   // Get the corresponding class file as a Resource.
	   URL resource=(loader!=null) ?
	                loader.getResource(filename) :
	                ClassLoader.getSystemResource(filename);
	   URLConnection connection = resource.openConnection();
	   // Note, we are using Connection.getLastModified not File.lastModifed.
	   // This will then work both or members of jars or standalone class files.
	   long time = connection.getLastModified();
	   String pdsRecord=null;
	   String hostname = java.net.InetAddress.getLocalHost().getHostName();
	   String temp = resource.getFile();
	   if(temp.indexOf('!') != -1){
		   temp = temp.substring(0,temp.indexOf('!'));
	   }
	   pdsRecord = StringMethods.getISOTimeStamp() + " ECSCLASSES PROD 4 *|" + hostname + "|" + cls.getName() + "|" + temp + "|" + ((time != 0L)?StringMethods.getISOTimeStamp(time) : "n") + "\n";
	   
	   return pdsRecord;
	}
	
	/**
	 * Log error message
	 * Author: Enhua
	 * Date: 2021-07-02 08:53:30
	 * @param errMsg
	 */
	public void logError(String errMsg) {
		log.write(LogStream.ERROR_MESSAGE, errMsg);
	}
	
	/**
	 * log Throwable
	 * Author: Enhua
	 * Date: 2021-07-02 14:03:00
	 * @param t
	 */
	public void logError(Throwable t) {
		log.writeExceptionStack(t);
	}
	
	/**
	 * Show error message
	 * Author: Enhua
	 * Date: 2021-07-01 14:48:52
	 * @param errMsg
	 */
	public void showError(String errMsg) {
		javax.swing.JOptionPane.showMessageDialog(null, errMsg, errorTitle, javax.swing.JOptionPane.ERROR_MESSAGE);
	}
	
	/**
	 * Log and show error message
	 * Author: Enhua
	 * Date: 2021-07-01 14:48:52
	 * @param errMsg
	 */
	public void logAndShowError(String errMsg) {
		log.write(LogStream.ERROR_MESSAGE, errMsg);
		javax.swing.JOptionPane.showMessageDialog(null, errMsg, errorTitle, javax.swing.JOptionPane.ERROR_MESSAGE);
	}
	
	/**
	 * Log and show Throwable
	 * Author: Enhua
	 * Date: 2021-07-01 13:55:14
	 * @param t
	 */
	public void logAndShowError(Throwable t) {
		log.writeExceptionStack(t);
		javax.swing.JOptionPane.showMessageDialog(null, t.getMessage(), errorTitle, javax.swing.JOptionPane.ERROR_MESSAGE);
	}
	
	/**
	 * log and view message
	 * Author: Enhua
	 * Date: 2021-08-04 14:03:47
	 * @param level
	 * @param msg
	 */
	public void logAndView(int level, String msg) {
		log.write(level, msg);
		if (level <= log.getLogLevel()) {
			setMessage(new ColoredString(StringMethods.getCurrentTimeStamp() + " " + msg, LogStream.MessageColors[level]));	
		}
	}
	
	/**
	 * log and view debug message
	 * Author: Enhua
	 * Date: 2021-08-12 16:46:05
	 * @param debugMsg
	 */
	public void logAndViewDebug(String debugMsg) {
		System.out.println(debugMsg);
		log.write(4, debugMsg);
		if (4 <= log.getLogLevel()) {
			setMessage(new ColoredString(StringMethods.getCurrentTimeStamp() + " " + debugMsg, LogStream.MessageColors[4]));
		}
	}
	
	/**
	 * view debug message
	 * Author: Enhua
	 * Date: 2021-08-13 10:24:07
	 * @param debugMsg
	 */
	public void viewDebug(String debugMsg) {
		if (4 <= log.getLogLevel()) {
			setMessage(new ColoredString(StringMethods.getCurrentTimeStamp() + " " + debugMsg, LogStream.MessageColors[4]));
		}
	}
	
	/**
	 * log and view error message
	 * Author: Enhua
	 * Date: 2021-08-12 16:46:05
	 * @param errMsg
	 */
	public void logAndViewError(String errMsg) {
		log.write(0, errMsg);
		if (0 <= log.getLogLevel()) {
			setMessage(new ColoredString(StringMethods.getCurrentTimeStamp() + " " + errMsg, LogStream.MessageColors[0]));	
		}
	}
	
	/**
	 * Log and view Throwable
	 * Author: Enhua
	 * Date: 2021-08-04 13:50:29
	 * @param t
	 */
	public void logAndViewError(Throwable t) {
		log.writeExceptionStack(t);
		setMessage(new ColoredString(StringMethods.getCurrentTimeStamp() + " " + t.getMessage(), LogStream.MessageColors[0]));	
	}
	
	/**
	 * log message
	 * Author: Enhua
	 * Date: 2021-07-19 14:48:40
	 * @param msg
	 */
	public void log(String msg) {
		log.write(LogStream.INFORMATION_MESSAGE, msg);
	}
	
	/**
	 * log debug message
	 * Author: Enhua
	 * Date: 2021-07-08 07:57:03
	 * @param debugMsg
	 */
	public void logDebug(String debugMsg) {
		log.write(LogStream.DEBUG_MESSAGE, debugMsg);
	}
	
	/**
	 * Tests if a specified file should be included in a file list.
	 * @param   dir    the directory in which the file was found.
	 * @param   name   the name of the file.
	 * @return  <code>true</code> if and only if the name should be
	 * included in the file list; <code>false</code> otherwise.
	 */
	public boolean accept(java.io.File dir, String name) {
		File file = new File(dir, name);
		if(file.isFile()) {
			fileCount++;
			if (fileCount < maxReadFiles) {
				return true;
			} else {
				return false;
			}			
		}else {
			return false;
		}
	}
	
	/**
	 * The addPropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void addPropertyChangeListener(java.beans.PropertyChangeListener listener) {
		getPropertyChange().addPropertyChangeListener(listener);
	}
	
	/**
	 * The addPropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void addPropertyChangeListener(java.lang.String propertyName, java.beans.PropertyChangeListener listener) {
		getPropertyChange().addPropertyChangeListener(propertyName, listener);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.beans.PropertyChangeEvent evt) {
		getPropertyChange().firePropertyChange(evt);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.lang.String propertyName, int oldValue, int newValue) {
		getPropertyChange().firePropertyChange(propertyName, oldValue, newValue);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.lang.String propertyName, java.lang.Object oldValue, java.lang.Object newValue) {
		getPropertyChange().firePropertyChange(propertyName, oldValue, newValue);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.lang.String propertyName, boolean oldValue, boolean newValue) {
		getPropertyChange().firePropertyChange(propertyName, oldValue, newValue);
	}
	
	/**
	 * Returns a sorted list of file in the directory specified, sorted by last modified time.
	 * Oldest files first.
	 * Creation date: (10/5/2004 9:40:58 AM)
	 * @return java.io.File[] array of files sorted by last modified, ascending.
	 * @param dir java.io.File
	 */
	public File[] getSortedFiles(File dir) {
		// get the files in the directory
		File [] filelist = null;
		try {
			filelist = dir.listFiles(this);
//		log.write(LogStream.INFORMATION_MESSAGE, "getSortedFiles.Parse PDS file." + dir.getName() +".Length:" + filelist.length );
			// reset the count of files
			fileCount = 0;

			File temp1, temp2;
			// sort the files based on last modified
			for (int i = 0; i < filelist.length -1; i++) {
				for (int j = 0; j < filelist.length -1-i; j++) {
					if (filelist[j].lastModified() > filelist[j+1].lastModified()) {
						temp1 = filelist[j];
						filelist[j] = filelist[j+1];
						filelist[j+1] = temp1;
					}
					if (filelist[j].lastModified() == filelist[j+1].lastModified()) {
						boolean before = CompareTwoFileSequence.compareTwoFileIfSameLastModify(filelist[j].getName(), filelist[j+1].getName());
						//if file1 after file2, then switch position.
						if(!before) {
							temp1 = filelist[j];
							filelist[j] = filelist[j+1];
							filelist[j+1] = temp1;
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			log.writeExceptionStack(e);
		}
		return filelist;
	}
	
	public File[] getSortFilesForBigVolumn(File dir) {
		File[] files = null;
		try {
			FileSortUtil fileSortUtil = new FileSortUtil(maxReadFiles, log);			
			ArrayList<FileEntity> arrayFileList = new ArrayList<FileEntity>();
			List<FileEntity> filesList = fileSortUtil.getFiles(arrayFileList, dir);
			List<FileEntity> sortedFiles = fileSortUtil.sortFiles(filesList);
			FileEntity[] array = sortedFiles.toArray(new FileEntity[0]);
			files = convertToFileArray(array);
		} catch (Exception e) {
			e.printStackTrace();
			log.writeExceptionStack(e);
			return new File[0];
		}
		return files;
	}

	public File[] getSortFilesForBigVolumn2(File dir) {
		try {
			return Files.walk(dir.toPath())
					.filter(path -> {
						File file = path.toFile();
						return file.isFile() && ++fileCount < maxReadFiles;
					})
					.map(Path::toFile)
					.sorted(Comparator.comparingLong(File::lastModified)
							.thenComparing((f1, f2) -> {
								if (f1.lastModified() == f2.lastModified()) {
									return CompareTwoFileSequence.compareTwoFileIfSameLastModify(
											f1.getName(), f2.getName()) ? -1 : 1;
								}
								return 0;
							}))
					.toArray(File[]::new);
		} catch (Exception e) {
			log.writeExceptionStack(e);
			return new File[0];
		} finally {
			fileCount = 0; 
		}
	}

	public File[] convertToFileArray(FileEntity[] fEntities) {
		File[] files = null;
		if(fEntities != null) {
			files = new File[fEntities.length];
			for(int i=0; i<fEntities.length; i++) {
				files[i] = fEntities[i].getFile();
			}
		}
		return files;
	}
	
	/**
	 * Gets the message property (ibm.disk.extensions.ColoredString) value.
	 * @return The message property value.
	 * @see #setMessage
	 */
	public ibm.disk.extensions.ColoredString getMessage() {
		return fieldMessage;
	}
	
	/**
	 * Accessor for the propertyChange field.
	 */
	protected java.beans.PropertyChangeSupport getPropertyChange() {
		if (propertyChange == null) {
			propertyChange = new java.beans.PropertyChangeSupport(this);
		};
		return propertyChange;
	}
	
	/**
	 * The hasListeners method was generated to support the propertyChange field.
	 */
	public synchronized boolean hasListeners(java.lang.String propertyName) {
		return getPropertyChange().hasListeners(propertyName);
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (10/23/2003 6:22:45 PM)
	 * @return boolean
	 */
	public boolean isExiting() {
		return exiting;
	}
	
	/**
	 * The removePropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void removePropertyChangeListener(java.beans.PropertyChangeListener listener) {
		getPropertyChange().removePropertyChangeListener(listener);
	}
	
	/**
	 * The removePropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void removePropertyChangeListener(java.lang.String propertyName, java.beans.PropertyChangeListener listener) {
		getPropertyChange().removePropertyChangeListener(propertyName, listener);
	}
	
	/**
	 * When an object implementing interface <code>Runnable</code> is used 
	 * to create a thread, starting the thread causes the object's 
	 * <code>run</code> method to be called in that separately executing 
	 * thread. 
	 * <p>
	 * The general contract of the method <code>run</code> is that it may 
	 * take any action whatsoever.
	 *
	 * @see     java.lang.Thread#run()
	 */
	public void run() {
		log.write(LogStream.HILITE_INFO_MESSAGE, "****  Application Restarted.Lihao Updated Time: 2023-10/20  ****");
		
		// Waiting for view started
		try {
			Thread.sleep(1000);
		} catch (InterruptedException e) {
			log.write(LogStream.WARNING_MESSAGE,"Interruptd.Thread return." + e.getMessage());
			return;
		}
		
		if (binaryFormat) {
			logAndViewDebug("Processing Binary PDS Files.");
		} else {
			logAndViewDebug("Processing Ascii-encoded PDS Files.");
		}
			
		if (hoursOfRetention == 0) {
			logAndViewDebug("RETENTION_HOURS=0, PDS files will be deleted after uploaded to SFTP");
		}
		
		
		logAndView(LogStream.INFORMATION_MESSAGE, "Test whether SFTP is accessible when starting the SFTPSend Application");
		try {
			// Test whether SFTP is OK
			openSFTP();
			//Initialize msg folder in share folder.
			sendPdsToServerService = new SendPdsToServerService(dbMsgIDProfile,sendByMsgIdModeString, log, session, channel);
			sendPdsToServerService.setCreateShareFolderMode(createShareFolderMode);
			
			//In share folder,will create empty message folder and message_current folder. 
			if(createShareFolderMode == CreateShareFolderMode.initialize) {
				ArrayList<String> msgList = sendPdsToServerService.getMsgList();
				sendPdsToServerService.initializeMsgFolderInServer(msgList, sftpServerDirName);				
			}
		} catch (Exception e1) {
			e1.printStackTrace();
			log.writeExceptionStack(e1);
		}finally {
			closeSFTP();
		}
		
		
		lastSendTime = new Date().getTime();
		while (!exiting) {
			// Make sure we don't need to exit.
			if (exiting || Thread.currentThread().isInterrupted()) {
				String msg = "Got interrupt signal.Break from running SFTP Send Thread.";
				System.out.println(msg);
				log.write(msg);
				break;
			}
			
			if (binaryFormat) {
				parseAndMoveBinaryFiles(pdsDir, sendingDir);
			} else {
				parseAndMoveAsciiFiles(pdsDir, sendingDir);
			}
			
			if (exiting || Thread.currentThread().isInterrupted()) {
				String msg = "Got interrupt signal.Break from running SFTP Send Thread.";
				log.write(msg);
				break;
			}
			
			
			uploadAndMoveFiles(sendingDir, sentDir);
			
			if (exiting || Thread.currentThread().isInterrupted()) {
				String msg = "Got interrupt signal.Break from running SFTP Send Thread.";
				log.write(msg);
				break;
			}
			
			// If no data to send for specified time, change to idle mode
			if (lastSendTime + changeToIdle < new Date().getTime()) {
				logAndViewDebug("For no data to send for more than " + (changeToIdle / 1000) + " sec, change to or keep idle mode"); 
				closeSFTP();
				try {
					Thread.sleep(idleSendInterval);
				} catch (InterruptedException e) {
					String msg = "Got interrupt signal.Break from running SFTP Send Thread.";
					log.write(msg);
					break;
				}
			} else {
//				logAndViewDebug("SFTP Send Active mode processed end. Sleep 1s."); 
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					log.write(LogStream.WARNING_MESSAGE,"Interruptd." + e.getMessage());
					break;
				}
			}
		}
		
		closeSFTP();
		logError("*****  Application " + blockName + " Terminated.  *****");
	}
	

	// Try primary server firstly every time. If failed, try backup server.
	public void openSFTP() {
		log.write(LogStream.INFORMATION_MESSAGE, "Step into open SFTP.");
		if (session == null) {
			boolean connected = false;
			while (!connected) {
				JSch jsch = new JSch();
				
				// try primary SFTP server
				try {
					session = jsch.getSession(primarySFTPServer.getUsername(), primarySFTPServer.getHostname(), primarySFTPServer.getPort());
					session.setPassword(primarySFTPServer.getPassword());
//					session.setConfig("StrictHostKeyChecking", "no");
					Properties prop = new Properties();
					prop.setProperty("StrictHostKeyChecking", "no");
					session.setConfig(prop);
//					session.setServerAliveCountMax(2);
					session.setServerAliveInterval(100 * 1000);
					session.connect();
					
					if(this.keepAliveMsgSeconds > 0) {
						KeepAliveRunnable runnable = new KeepAliveRunnable(session, this.keepAliveMsgSeconds);
						Thread thread = new Thread(runnable);
						thread.start();						
					}
					
					channel = (ChannelSftp) session.openChannel("sftp");
					channel.connect();
				
					setCurrentSFTPServer(primarySFTPServer);
					setSftpConnected("Y");
					logAndView(LogStream.HILITE_INFO_MESSAGE, "Connected to SFTP: " + primarySFTPServer);
					return;
				} catch (Exception e) {
					logAndViewError(e);
					closeSFTP();
				}
				
				// Only when primary SFTP server cannot be connected, try backup SFTP server
				try {
					if (backupSFTPServer != null) {
						session = jsch.getSession(backupSFTPServer.getUsername(), backupSFTPServer.getHostname(), backupSFTPServer.getPort());
						session.setPassword(backupSFTPServer.getPassword());
//						session.setConfig("StrictHostKeyChecking", "no");
						Properties prop = new Properties();
						prop.setProperty("StrictHostKeyChecking", "no");
						session.setConfig(prop);
						session.connect();
						
						channel = (ChannelSftp) session.openChannel("sftp");
						channel.connect();
						setCurrentSFTPServer(backupSFTPServer);
						setSftpConnected("Y");
						logAndView(LogStream.HILITE_INFO_MESSAGE, "Connected to SFTP: " + backupSFTPServer);
						return;
					}
				} catch (Exception e) {
					logAndViewError(e);
					closeSFTP();
				}
		
				try {
					Thread.sleep(10000);
				} catch (InterruptedException e) {
					log.write(LogStream.WARNING_MESSAGE,"Interruptd.Return from OpenSession." + e.getMessage());
					return;
				}
			}
		}

		log.write(LogStream.WARNING_MESSAGE, "Step out of open SFTP.");
	}
	
	// Close the connection to SFTP
	public void closeSFTP() {
		if (channel != null) {
			try {
				if(channel.isConnected()) {
					channel.disconnect();
					setSftpConnected("N");
					log.write(LogStream.INFORMATION_MESSAGE, "Channel disconnected from SFTP: " + currentSFTPServer);
				}else {
					log.write(LogStream.INFORMATION_MESSAGE, "Channel is already disconnected.");
				}
			} catch (Exception e) {
				logAndViewError(e);
			}
			channel = null;
		}else {
			log.write(LogStream.INFORMATION_MESSAGE, "Channel is null.Already disconnected.");
		}
		if (session != null) {
			try {
				if(session.isConnected()) {
					session.disconnect();		
					log.write(LogStream.INFORMATION_MESSAGE, "Session disconnected from SFTP: " + currentSFTPServer);
				}else {
					log.write(LogStream.INFORMATION_MESSAGE, "Session already disconnected from SFTP" + currentSFTPServer);
				}
			} catch (Exception e) {
				logAndViewError(e);
			}
			session = null;
		}else {
			log.write(LogStream.INFORMATION_MESSAGE, "Session null.Already disconnected from SFTP" + currentSFTPServer);
		}
	}
	
	/**
	 * For ascii-encoded files,
	 * move from PDS_DIR to SFTP_DIR/sending
	 * Author: Enhua
	 * Date: 2021-08-09 13:30:27
	 * @param fromDir
	 * @param toDir
	 */
	public void parseAndMoveAsciiFiles(File fromDir, File toDir) {
		// Only if less than 10 files in /sent, will do the move
		String[] toList = toDir.list();
		if (toList.length >= 10) {
			logAndViewDebug("10 or more files in /sending :" + toDir.getAbsolutePath() +", not process PDS files in pdsDir");
			return;
		}
		
		RandomAccessFile in = null;
		String data;
		
//		File[] fromFileList = getSortedFiles(fromDir);
		File[] fromFileList = getSortFilesForBigVolumn(fromDir);
		if(fromFileList == null) {
			logError("Sorted list return empty.");
			return;
		}
		
		if (fromFileList.length == 0) {
			viewDebug("No PDS files found in pdsDir: " + pdsDirName);
			return;
		}
		
		
		logAndViewDebug(fromFileList.length + " PDS files found in pdsDir: "+  pdsDirName);
		
		logAndViewDebug("Wait " + waitingForWriteEnded/1000 + " sec to make sure other ECS applications write PDS files ended");
		
		// Waiting for other ECS applications write PDS files ended
		try {
			Thread.sleep(waitingForWriteEnded);
		} catch (InterruptedException e) {
			String msgString = "Interruptd.Return from parseAndMoveAsciiFiles." + e.getMessage();
			System.out.println(msgString);
			log.write(LogStream.WARNING_MESSAGE,msgString);
			return;
		};
		
		logAndViewDebug("Start to parse PDS files");
		for (int i = 0; i < fromFileList.length; i++) {
			if (fromFileList[i].isDirectory()) {
				continue;
			}
			
			logAndViewDebug("Parse PDS file: " + fromFileList[i]);
			try {
				in = new RandomAccessFile(fromFileList[i], "r");
			} catch (FileNotFoundException e) {
				logAndViewError(e);
				continue;
			}
			
			try {
				logDebug("Trying to backup to src path. " + backupSrcPdsDirName);
				if(backupSrcPdsDirName != null && !backupSrcPdsDirName.trim().isEmpty()) {
					if(!backupSrcPdsDir.exists()) {
						backupSrcPdsDir.mkdir();
					}					
					backupSourcePdsFile(fromFileList[i]);
				}
			} catch (IOException e1) {
				logError(e1);
			}
			
			try {
				if (in.length() < 9) {
					logAndViewError("[File: " + fromFileList[i] + "] is empty or too small, trying to move to /problem");
					File problemFile = new File(problemDirName + "EmptyOrTooSmall_" + fromFileList[i].getName());
					fromFileList[i].renameTo(problemFile);
					logAndViewError("Moved to " + problemFile + " successfully");
					continue;
				} else {
					// the line of data in original file, will be used for the new file name
					File currentFile = fromFileList[i];
					if(!packSameMsgIdData) {
						parseMsgIdAndWriteToFile(in, currentFile, i);						
					}else {
						parseMsgIdAndPackToFile(in, currentFile, i);
					}
				}
			} catch (IOException e) {
				logAndViewError(e);
				continue;
			} finally {
				if (in != null) {
					try {
						in.close();
					} catch (IOException e) {
						logAndViewError(e);
					}
					in = null;
				}
			}
			
			if (!fromFileList[i].delete()) {
				logAndViewError("Failed to delete file=" + fromFileList[i]);
			}
		}
	}
	
	
	private void processAsciiFile(File file) throws IOException {
	    if (file.length() < 9) {
	        handleProblemFile(file, "EmptyOrTooSmall_");
	        return;
	    }

	    try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
	        if (backupSrcPdsDirName != null && !backupSrcPdsDirName.isEmpty()) {
	            backupSourcePdsFile(file);
	        }

	        if (packSameMsgIdData) {
	            processPackedFile(reader, file);
	        } else {
	            processUnpackedFile(reader, file);
	        }
	    } finally {
	        if (!file.delete()) {
	            logAndViewError("Failed to delete file=" + file);
	        }
	    }
	}
	
	
	private void handleProblemFile(File file, String prefix) {
	    try {
	        File problemFile = new File(problemDirName + prefix + file.getName());
	        if (file.renameTo(problemFile)) {
	            logAndViewError("Moved to " + problemFile + " successfully");
	        } else {
	            logAndViewError("Failed to move file to problem directory: " + file.getName());
	        }
	    } catch (Exception e) {
	        logAndViewError("Error handling problem file: " + file.getName() + " - " + e.getMessage());
	    }
	}
	
	private void processPackedFile(BufferedReader reader, File file) throws IOException {
	    Map<String, StringBuffer> msgMap = new HashMap<>();
	    String line;
	    int lineNum = 0;
	    
	    while ((line = reader.readLine()) != null) {
	        lineNum++;
	        if (line.length() > maxLineLength) {
	            handleProblemFile(file, "DataLineTooLong_");
	            continue;
	        }

	        String dbMsgID = getDBMsgID(line);
	        if (dbMsgID == null) {
	            String problemFileName = problemDirName + "DataLineLessField_" + file.getName() + "_line" + lineNum;
	            writeToFile(problemFileName, line);
	            continue;
	        }

	        StringBuffer buffer = msgMap.computeIfAbsent(dbMsgID, k -> new StringBuffer());
	        buffer.append(line).append("\n");
	        logAndView(LogStream.DEBUG_MESSAGE, "Processed line " + lineNum + " for msgID: " + dbMsgID);
	    }

	    for (Map.Entry<String, StringBuffer> entry : msgMap.entrySet()) {
	        String msgId = entry.getKey();
	        String sendingFileName = sendingDirName + msgId + "_" + machineName + "_" + file.getName();
	        writeToFile(sendingFileName, entry.getValue().toString());
	        
	        if (recordMsgIdService != null) {
	            recordMsgIdService.recordMsgId(msgId);
	            if (isRecordParsedPdsFileDetail) {
	                recordMsgIdService.recordMsgDetail(msgId, sendingFileName, sendingDirName);
	            }
	        }
	    }
	}

	private void processUnpackedFile(BufferedReader reader, File file) throws IOException {
	    String line;
	    int lineNum = 0;
	    
	    while ((line = reader.readLine()) != null) {
	        lineNum++;
	        logAndViewDebug("[PDS Data: " + file.getName() + "_line" + lineNum + "] " + line);

	        if (line.length() > maxLineLength) {
	            String problemFileName = problemDirName + "DataLineTooLong_" + file.getName() + "_line" + lineNum;
	            writeToFile(problemFileName, line);
	            continue;
	        }

	        String dbMsgID = getDBMsgID(line);
	        if (dbMsgID == null || dbMsgID.isEmpty()) {
	            String prefix = dbMsgID == null ? "DataLineLessField_" : "NoProfile_";
	            String problemFileName = problemDirName + prefix + file.getName() + "_line" + lineNum;
	            writeToFile(problemFileName, line);
	            continue;
	        }

	        String sendingFileName = sendingDirName + dbMsgID + "_" + machineName + "_" + 
	                               file.getName() + "_line" + lineNum;
	        writeToFile(sendingFileName, line);
	        
	        if (recordMsgIdService != null) {
	            recordMsgIdService.recordMsgId(dbMsgID);
	            if (isRecordParsedPdsFileDetail) {
	                recordMsgIdService.recordMsgDetail(dbMsgID, sendingFileName, sendingDirName);
	            }
	        }
	    }
	}
	

	/**
		1. Read data in line;
		2.Get line data
		3.Get message Id
		4.  a. Can't parse data line, write to problem
	    	b. Error of getting data line, write to problem
	    	c. Get msg id ,write to sent, record msg id 
	 * @param in
	 * @param currentFile
	 * @throws IOException
	 */
	private void parseMsgIdAndWriteToFile(RandomAccessFile in, File currentFile, int seq) throws IOException {
		String data;
		int line = 0;
		do {
			line++;
			data = in.readLine();
			if (data != null) {
				logAndViewDebug("[PDS Data: " + currentFile + "_line" + line + "] " + data);
				if (data.length() > maxLineLength) {
					logAndViewError("The length of data line is more than " + maxLineLength + ", trying to write the data to a new file in /problem");
					String problemFileName = problemDirName + "DataLineTooLong_" + currentFile.getName() + "_line" + line;
					writeToFile(problemFileName, data);
					logAndViewError("Write to [New File: " + problemFileName + "]");
					continue;
				}
				
				String dbMsgID = getDBMsgID(data);
				if (dbMsgID == null) {
					logAndViewError("The field of data line is less than " + 3 + ", trying to write the data to a new file in /problem");
					String problemFileName = problemDirName + "DataLineLessField_" + currentFile.getName() + "_line" + line;
					writeToFile(problemFileName, data);
					logAndViewError("Write to [New File: " + problemFileName + "]");
				} else if (dbMsgID.equals("")) {
					logAndViewError("[Profile: " + dbMsgIDProfileName + "] error" + ", trying to write the data to a new file in /problem");
					String problemFileName = problemDirName + "NoProfile_" + currentFile.getName() + "_line" + line;
					writeToFile(problemFileName, data);
					logAndViewError("Write to [New File: " + problemFileName + "]");
				} else {
					String sendingFileName = sendingDirName + dbMsgID + "_" + machineName + "_" + currentFile.getName() + "_line" + line + "Seq" + seq;
					writeToFile(sendingFileName, data);
					logAndView(LogStream.INFORMATION_MESSAGE, "[Write to new file: " + sendingFileName + "] " + data);
					
					if(recordMsgIdService != null) {
						recordMsgIdService.recordMsgId(dbMsgID);
						if(isRecordParsedPdsFileDetail) {
							recordMsgIdService.recordMsgDetail(dbMsgID, sendingFileName, sendingDirName);							
						}
					}
				}
				
			}
		} while (data != null);
	}
	
	private void parseMsgIdAndPackToFile(RandomAccessFile in, File currentFile, int seq) throws IOException {
		//For the same msg id message, will be pack into one pds file
		Map<String, StringBuffer> map = new HashMap<String, StringBuffer>();
		StringBuffer stringBuffer = null;
		String data;
		int line = 0;
		do {
			line++;
			data = in.readLine();
			if (data != null) {
				logAndViewDebug("[PDS Data: " + currentFile + "_line" + line + "] " + data);
				if (data.length() > maxLineLength) {
					logAndViewError("The length of data line is more than " + maxLineLength + ", trying to write the data to a new file in /problem");
					String problemFileName = problemDirName + "DataLineTooLong_" + currentFile.getName() + "_line" + line;
					writeToFile(problemFileName, data);
					logAndViewError("Write to [New File: " + problemFileName + "]");
					continue;
				}

				String dbMsgID = getDBMsgID(data);
				if (dbMsgID == null) {
					logAndViewError("The field of data line is less than " + 3 + ", trying to write the data to a new file in /problem");
					String problemFileName = problemDirName + "DataLineLessField_" + currentFile.getName() + "_line" + line;
					writeToFile(problemFileName, data);
					logAndViewError("Write to [New File: " + problemFileName + "]");
				} else if (dbMsgID.equals("")) {
					logAndViewError("[Profile: " + dbMsgIDProfileName + "] error" + ", trying to write the data to a new file in /problem");
					String problemFileName = problemDirName + "NoProfile_" + currentFile.getName() + "_line" + line;
					writeToFile(problemFileName, data);
					logAndViewError("Write to [New File: " + problemFileName + "]");
				} else {
					if(!map.containsKey(dbMsgID)) {
						stringBuffer = new StringBuffer();
					}else {
						stringBuffer = map.get(dbMsgID);
					}
					stringBuffer.append(data);
					stringBuffer.append("\n");
					map.put(dbMsgID, stringBuffer);
					logAndView(LogStream.INFORMATION_MESSAGE, "Data is parsed.Line:" + line +  ".Data:" + data + ".");
				}
			}
		} while (data != null);
		
		for(Entry<String, StringBuffer> elem : map.entrySet()) {
			String msgId = elem.getKey();
			String sendingFileName = sendingDirName + msgId + "_" + machineName + "_" + currentFile.getName() + "Seq" + seq;
			writeToFile(sendingFileName, elem.getValue().toString());
			logAndView(LogStream.INFORMATION_MESSAGE, "[Write to new file: " + sendingFileName + "] ");
			logAndView(LogStream.DEBUG_MESSAGE, elem.getValue().toString().trim());
			if(recordMsgIdService != null) {
				recordMsgIdService.recordMsgId(msgId);
				if(isRecordParsedPdsFileDetail) {
					recordMsgIdService.recordMsgDetail(msgId, sendingFileName, sendingDirName);					
				}
				log.write("Msg ID was recorded:" + msgId);
			}
		}
		
		log.write(LogStream.INFORMATION_MESSAGE, "All data parsed and wrote to files.");
	}
	
	
	/**
	 * Get DBMsgID of ascii-encoded data.
	 * Example data: 2021-07-31-15.01.19.000000 LID PROD 9 * /B883C526236/PJ02715299/PCDJ  /DOUBLE        /BY48Z     /LID4001   /0.4/1.1/1.3
	 * DB_MSG_ID_PROFILE=/opt/ecs/profiles/DBMsgID.pro
	 * Author: Enhua
	 * Date: 2021-08-09 15:07:36
	 * @param data
	 * @return
	 */
	public String getDBMsgID(String data) {
		String dbMsgID = "";
		if (data != null) {
			StringTokenizer st = new StringTokenizer(data, " ");
			
			if (st.countTokens() < 3) {
				return null;
			}
			
			// ignore the first token, should be iso timestamp
			st.nextToken();
			
			String pdsType = st.nextToken();
//			String product = st.nextToken();
			
			Vector keys = new Vector();
			keys.add(DBMsgIDRecord.COMPARE_BY_TYPE);
			keys.add(pdsType);
//			keys.add(product);
			try {
				DBMsgIDRecord dbMsgIDRec = (DBMsgIDRecord) dbMsgIDProfile.getRecord(0, keys);
				if (dbMsgIDRec == null) {
					logAndViewError("[PDS Type=" + pdsType + "] is not configured in " + dbMsgIDProfileName);
				} else {
					dbMsgID = dbMsgIDRec.getDBMsgID();
					logAndViewDebug("[Get from profile: " + dbMsgIDProfileName + "] DB Message ID=" + dbMsgIDRec.getDBMsgID() + ", Table Name=" + dbMsgIDRec.getTableName());
				}
			} catch (ProfileException e) {
				logAndViewError(e);
			}
		}
		return dbMsgID;
	}
	
	/**
	 * Write ascii-encoded data to file
	 * Author: Enhua
	 * Date: 2021-08-10 11:40:58
	 * @param fileName
	 * @param data
	 */
	public void writeToFile(String fileName, String data) {
		FileOutputStream outputStream = null;
		try {
			outputStream = new FileOutputStream(fileName);
			outputStream.write(data.getBytes());
		} catch (IOException e) {
			logAndViewError(e);
		} finally {
			if (outputStream != null) {
				try {
					outputStream.close();
				} catch (IOException e) {
					logAndViewError(e);
				}
			}
		}
	}
	
	
	/**
	 * For binary files,
	 * move from DATA_FILE_DIRECTORY to DATA_FILE_DIRECTORY/sending
	 * Author: Enhua
	 * Date: 2021-08-09 13:30:57
	 * @param fromDir
	 * @param toDir
	 */
	public void parseAndMoveBinaryFiles(File fromDir, File toDir) {
		// Only if less than 10 files in /sent, will do the move
		String[] toList = toDir.list();
		if (toList.length >= 10) {
			logAndViewDebug("10 or more files in /senting" + toDir.getAbsolutePath() + ", not process PDS files in pdsDir");
			return;
		}
		
		
//		File[] fromFileList = getSortedFiles(fromDir);
		File[] fromFileList = getSortFilesForBigVolumn(fromDir);
		if (fromFileList.length <= 0) {
			viewDebug("No PDS files found in pdsDir: " + pdsDirName);
			return;
		}
		
		logAndViewDebug((fromFileList.length) + " PDS files found in pdsDir: "+  pdsDirName);
		
		logAndViewDebug("Wait " + waitingForWriteEnded/1000 + " sec to make sure other ECS applications write PDS files ended");
		
		// Waiting for other ECS applications write PDS files ended
		try {
			Thread.sleep(waitingForWriteEnded);
		} catch (InterruptedException e) {
			log.write(LogStream.WARNING_MESSAGE,"Interruptd.Return from parseAndMoveBinaryFiles." + e.getMessage());
			return;
		}
		
		logAndViewDebug("Start to parse PDS files");
		for (int i = 0; i < fromFileList.length; i++) {
			if (fromFileList[i].isDirectory()) {
				continue;
			}
			logAndViewDebug("Parse PDS file: " + fromFileList[i]);
			BinaryService binaryService = new BinaryService(log, dbMsgIDProfile);

			
			File file = null;
			String fileName = null;
			FileInputStream fis = null;
			try {
				file = new File(fromFileList[i].getAbsolutePath());
				if(!file.exists()) {
					throw new FileNotFoundException("File " + file.getName() + " doesn't exist.Please check.");
				}
				fileName = file.getName();
				char charAt0 = fileName.charAt(0);
				if(Character.isLetter(charAt0)) {
					System.out.println("fileName:" + fileName + " char at 0:" + charAt0);
					continue;
				}
				
				try {
					logDebug("Trying to backup to src path. " + backupSrcPdsDirName);
					if(backupSrcPdsDirName != null && !backupSrcPdsDirName.trim().isEmpty()) {
						if(!backupSrcPdsDir.exists()) {
							backupSrcPdsDir.mkdir();
						}					
						backupSourcePdsFile(fromFileList[i]);
					}
				} catch (IOException e1) {
					logError(e1);
				}
				
				fis = new FileInputStream(file);
				int tmp;
				StringBuffer sBuffer = new StringBuffer();
				while((tmp = fis.read()) != -1) {
					sBuffer.append(StringMethods.pad(Integer.toHexString(tmp), 2, '0', true));
				}
				try {
					if(fis != null) {
						fis.close();						
						fis = null;
					}
				} catch (IOException e) {
					logError("IOException at first close.Error msg:" + e.getMessage());
					logError(e);
				} 
					
				boolean isMultiMsg = binaryService.isMultiMessage(sBuffer.toString());
				if(!isMultiMsg) {
					//Single line message
					String asciiMsg = binaryService.generatePdsStringForIndividualMsg(sBuffer.toString());
					String msgId = binaryService.getMsgIdForIndividualMsg(sBuffer.toString()) ;
					String sendingFileName = sendingDirName + msgId + "_" + machineName + "_" + fromFileList[i].getName() + "Seq" + i ;
					writeToFile(sendingFileName, asciiMsg);
					if(recordMsgIdService != null) {
						recordMsgIdService.recordMsgId(msgId);						
						if(isRecordParsedPdsFileDetail) {
							recordMsgIdService.recordMsgDetail(msgId, sendingFileName, sendingDirName);							
						}
					}
					logAndView(LogStream.INFORMATION_MESSAGE, "[Write to new file: " + sendingFileName + "] " + asciiMsg);
				}else {
					ArrayList<SingleMessage> msgList = binaryService.splitPackedMsgToArrayList(sBuffer.toString());
					if(!packSameMsgIdData) {
						for(int index=0; index<msgList.size(); index++) {
							String msgId = msgList.get(index).getMsgId();
							String sendingFileName = sendingDirName + msgId + "_" + machineName + "_" + fromFileList[i].getName() + "_" + (index + 1) + "Seq" + i;
							String singleMsg = binaryService.generatePdsStringForSingleMsg(msgList.get(index));
							writeToFile(sendingFileName, singleMsg);
							if(recordMsgIdService != null) {
								recordMsgIdService.recordMsgId(msgId);
								if(isRecordParsedPdsFileDetail) {
									recordMsgIdService.recordMsgDetail(msgId, sendingFileName, sendingDirName);									
								}
							}
							logAndView(LogStream.INFORMATION_MESSAGE, "[Write to new file: " + sendingFileName + "] " + singleMsg);
						}						
					}else {
						//For the same msg id message, will be pack into one pds file
						Map<String, StringBuffer> map = new HashMap<String, StringBuffer>();
						StringBuffer stringBuffer = null;
						for(int j=0; j<msgList.size(); j++) {
							String singleMsg = binaryService.generatePdsStringForSingleMsg(msgList.get(j));
							String msgId = msgList.get(j).getMsgId();
							if(!map.containsKey(msgId)) {
								stringBuffer = new StringBuffer();
							}else {
								stringBuffer = map.get(msgId);
							}
							stringBuffer.append(singleMsg);
							stringBuffer.append("\n");
							
							map.put(msgId, stringBuffer);
						}
						
						for(Entry<String, StringBuffer> elem : map.entrySet()) {
							String msgId = elem.getKey();
							String sendingFileName = sendingDirName + msgId + "_" + machineName + "_" + fromFileList[i].getName() + "Seq" + i;
							writeToFile(sendingFileName, elem.getValue().toString());
							if(recordMsgIdService != null) {
								recordMsgIdService.recordMsgId(msgId);						
								recordMsgIdService.recordMsgDetail(msgId, sendingFileName, sendingDirName);
							}
							logAndView(LogStream.INFORMATION_MESSAGE, "[Write to new file: " + sendingFileName + "] " + elem.getValue().toString());
						}
					}
				}
				
				//Process completed.Delete used file
				file.delete();
				
		
			} catch (Exception e) {
				e.printStackTrace();
				
				logError("Problem file name:" + fileName + ".Error msg:" + e.getMessage());
				logError(e);
				File problemFile = new File(problemDirName + fileName);
				boolean isGood = file.renameTo(problemFile);
				if(!isGood) {
					logError("Move problem file failed.File name:" + problemFile);
				}else {
					log.write(LogStream.INFORMATION_MESSAGE, "Problem file moved to " + problemFile.getAbsolutePath());
				}
			}finally {
				try {
					if(fis != null) {
						fis.close();						
					}
				} catch (IOException e) {
					logError("IOException msg:" + e.getMessage());
					logError(e);
				}				
			}
		}
	}


	private void backupSourcePdsFile(File file) throws IOException {
		String filename = file.getName();
		String destFileName = getArchiveDirString(archiveByHour, backupSrcPdsDirName, filename);
		System.out.println("Dest file name: " + destFileName);
		File dest = new File(destFileName);
		if(!dest.exists()) {
//			Files.copy(file.toPath(), dest.toPath());					
			copy(file.getAbsolutePath(), dest.getAbsolutePath());
		}
		
		logDebug("File " + filename + " is copied to " + dest);
	}
	
	private String getArchiveDirString(String archiveByHour,String destDirStr, String fileName) {
//		System.out.println("Archive by hour" + archiveByHour);
		if(archiveByHour != null && !archiveByHour.isEmpty()) {
			String folderName = "empty";
			
			//Read property file
			FolderLastCreatedUtil folderLastCreatedUtil = new FolderLastCreatedUtil(destDirStr + "folderCreate.properties");
			try {
				File propFile = folderLastCreatedUtil.getPropFile();
				if(!propFile.exists()) {
					folderName = folderLastCreatedUtil.createFolderName(); 
					folderLastCreatedUtil.writeCurrentTimeToPropertieyFile();
				}else {
					boolean exceedTime = folderLastCreatedUtil.compare(Integer.parseInt(archiveByHour));
					if(exceedTime) {
						folderLastCreatedUtil.writeCurrentTimeToPropertieyFile();
						folderName = folderLastCreatedUtil.createFolderName();
					}else {
						long latestTime = folderLastCreatedUtil.getLatestFolderTimeFromPorp();
						folderName = folderLastCreatedUtil.getFolderNameFromTime(latestTime);
					}					
				}
			} catch (Exception e) {
				log.writeExceptionStack(e);
			}
			
			//Need to make directory
			String dest = destDirStr + folderName + File.separator;
			System.out.println("dest:" + dest);
			File file = new File(dest);
			if(!file.exists()) {
				file.mkdir();
			}
			
			String destFileStr = dest  + fileName;
			System.out.println("destFileStr:" + destFileStr);
			return destFileStr;
		}else {
			return destDirStr + fileName;
		}
	}
	
	public void copy(String source, String dest) {
	    InputStream in = null;
	    OutputStream out = null;
	    try {
	        in = new FileInputStream(new File(source));
	        out = new FileOutputStream(new File(dest));

	        byte[] buffer = new byte[1024];
	        int len;

	        while ((len = in.read(buffer)) > 0) {
	            out.write(buffer, 0, len);
	        }
	    } catch (Exception e) {
	    	log.write(LogStream.ERROR_MESSAGE, e.getMessage());
	    } finally {
	    	if(in != null) {
	    		try {
					in.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
	    	}
	    	if(out != null) {
	    		try {
					out.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
	    	}
	    }
	    
	}   
	
	/**
	 * Send files from /sending to SFTP server.
	 * if successful, move files from /sending to /sent
	 * Author: Hao 
	 * * Date: 2021-08-09 13:31:47
	 * @param fromDir
	 * @param toDir
	 * @param serverDirName
	 * @throws SftpException 
	 */
	public void uploadAndMoveFiles(File fromDir, File toDir)  {
		logAndViewDebug("Entering upload and move files.");
		File[] listFiles = getSortedFilesByProcessSeq(fromDir);
		if (listFiles.length == 0) {
			return;
		}
		logAndViewDebug(listFiles.length + " PDS files found in sendingDir: " +  sendingDirName);
		logAndViewDebug("Start to upload PDS files in " + sendingDirName + " to SFTP");
		
		openSFTP();
		sendPdsToServerService.setSession(session);
		sendPdsToServerService.setChannel(channel);
		
		//For test process, it's convenient for changing profile without rebooting
		if(needDownloadProfile && isTimeToFetchFromServer(recordInstant, downloadIntervalMinutes)) {
			log.write("Trying to download path:" + downloadProfilePath);
			sendPdsToServerService.downloadProfileFromServer(downloadProfilePath, getMainProfile());				
			//After refreshing the profile ,then update time.
			recordInstant = new Date();
			log.write("Downloaded.Path:" + downloadProfilePath);
		}
		//Compare last modify, if different, then reload profile and refresh send to server service
		reloadMainProfileIfChanged();
		
		File sendingfile = null;
		File sentfile = null;
		String sendingFileName = null;
		String sentFileName = null;
		String fileName = null;
		for (int i = 0; i < listFiles.length; i++) {
			logAndViewDebug("Upload PDS " + i + " file:" + listFiles[i] + " .");
			
			if(Thread.currentThread().isInterrupted()) {
				log.write(LogStream.WARNING_MESSAGE, "Uploading but receive interrupted signal.Exit");
				return;
			}
			
//			try {
//				sendPdsToServerService = new SendPdsToServerService(dbMsgIDProfile, sendByMsgIdModeString, log, session, channel);
//				sendPdsToServerService.setCreateShareFolderMode(createShareFolderMode);
//			} catch (ProfileException e) {
//				logError(e);
//			} 
			
			sendingfile = listFiles[i];
			fileName = sendingfile.getName();

			sendingFileName = sendingDirName + fileName;
			String fileMsgId = sendPdsToServerService.getFileMsgId(fileName);
			//Switch mode if profile specific
			try {
				Integer.parseInt(fileMsgId); //if not a parsable  file msg id , will throw error
				settingChangeBaseOnMsgProfileForSendService(fileMsgId);
			} catch (Exception e1) {
				log.write("Can't parse msgId of File:" + sendingFileName + ".Send to problem.");
				String problemFileName = problemDirName + fileName;
				File problemFile = new File(problemFileName);
				boolean renameTo = sendingfile.renameTo(problemFile);
				if(renameTo) {
					log.write("Send to problem:" + problemFileName);					
				}else {
					log.write("Failed to sent to problem." + problemFileName);
				}
				continue;
			}
			log.write("Current mode:" + sendPdsToServerService.getMode());

			//Upload,currently most of msg are using mode of SplitSendByMsgId 2
			try {
				sendPdsToServerService.process(sftpServerDirName, fileMsgId, i, sendingFileName);
			} catch (SftpException e) {
				log.writeExceptionStack(e);
				closeSFTP();
				return;
			}

			if (hoursOfRetention == 0) {
				if (!sendingfile.delete()) {
					logAndView(LogStream.ERROR_MESSAGE, "Failed to delete file: " + sendingFileName);
				} else {
					logAndViewDebug("[File: " + sendingFileName + "] was deleted");
				}
			} else {
				sendingfile.setLastModified(new Date().getTime());
				sentFileName = getArchiveDirString(archiveByHour, sentDirName, sendingfile.getName());
				sentfile = new File(sentFileName);
				lastSendTime = new Date().getTime();
				
				boolean renameTo = sendingfile.renameTo(sentfile);
				if (renameTo) {
					logAndViewDebug("[File: " + sendingFileName + "] was moved to " + sentfile);
				} else {
					logAndViewError("Failed to move [File: " + sendingFileName + "] to dest: " + sentfile + ", attemping to delete it");
					if (!sendingfile.delete()) {
						logAndViewError("Failed to delete file: " + sendingFileName);
					} else {
						logAndViewDebug("[File: " + sendingFileName + "] was deleted");
					}
				}
			}
		}
		log.write(LogStream.INFORMATION_MESSAGE, "Upload file end.");
		closeSFTP();
	}


	private void reloadMainProfileIfChanged() {
		File mainProFile = new File(getMainProfile());
		if(mainProFile.exists()) {
			long potentialNewMainProfileLastModify = mainProFile.lastModified();
			if(getMainProfileLastModifyDate() != potentialNewMainProfileLastModify) {
				//Close old session
				closeSFTP();
				readProfile(getMainProfile(), "SFTPSend");				
				log.write("Profile configurations have been reloaded .");
				try {
					//Open new session
					openSFTP();
					//Initialize msg folder in share folder.
					sendPdsToServerService = new SendPdsToServerService(dbMsgIDProfile,sendByMsgIdModeString, log, session, channel);
					sendPdsToServerService.setCreateShareFolderMode(createShareFolderMode);
					sendPdsToServerService.setSession(session);
					sendPdsToServerService.setChannel(channel);
					
					//In share folder,will create empty message folder and message_current folder. 
					if(createShareFolderMode == CreateShareFolderMode.initialize) {
						ArrayList<String> msgList = sendPdsToServerService.getMsgList();
						sendPdsToServerService.initializeMsgFolderInServer(msgList, sftpServerDirName);				
					}
					log.write("Send pds to server service has been reloaded .");
				} catch (Exception e1) {
					e1.printStackTrace();
					log.writeExceptionStack(e1);
					closeSFTP();
				}
			}
		}
		
	}
	
	//If this is first time(null), then return true;
	//If older than x minutes ,then return true;
	private boolean isTimeToFetchFromServer(Date recordInstant, int minutes) {
		
		if(recordInstant == null) {
			return true;
		}
		
		//now > record + 2
		//		Instant now = Instant.now();
		Date now = new Date();
		return now.getTime() > (recordInstant.getTime() + minutes * 60 * 1000);
	}


	public File[] getSortedFilesByProcessSeq(File dir) {
		File [] filelist = dir.listFiles(this);
//		log.write(LogStream.INFORMATION_MESSAGE, "getSortedFiles.Parse PDS file." + dir.getName() );
		// reset the count of files
		fileCount = 0;
		
		File temp1;
		// sort the files based on last modified
		for (int i = 0; i < filelist.length -1; i++) {
			for (int j = 0; j < filelist.length -1-i; j++) {
				String file1Name = filelist[j].getName();
				String file2Name = filelist[j+1].getName();
				int indexOfFile1 = file1Name.indexOf("Seq");
				int indexOfFile2 = file2Name.indexOf("Seq");
				int f1Seq = 0;
				if(indexOfFile1 != -1) {
					String f1SeqNumber = file1Name.substring(indexOfFile1 + 3);
					f1Seq = Integer.parseInt(f1SeqNumber);
				}
				int f2Seq = 0;
				if(indexOfFile2 != -1) {
					String f2SeqNumber = file2Name.substring(indexOfFile2 + 3);
					f2Seq = Integer.parseInt(f2SeqNumber);
				}
				
				if(f1Seq > f2Seq) {
					temp1 = filelist[j];
					filelist[j] = filelist[j+1];
					filelist[j+1] = temp1;
				}
			}
		}
		return filelist;
		
	}

	
	//If msg profile has specific settings,  will be add here
	//Currently:
	//1. switch split mode
	private void settingChangeBaseOnMsgProfileForSendService(String fileMsgId) {
		//Switch split mode
		DBMsgIDRecord rec;
		try {
			if(this.binaryFormat) {
				Vector vec = new Vector();
				vec.add(DBMsgIDRecord.COMPARE_BY_TYPE);
				vec.add(fileMsgId);
				rec = (DBMsgIDRecord)dbMsgIDProfile.getRecord(1,vec);
			}else {
				Vector keys = new Vector();
				keys.add(DBMsgIDRecord.COMPARE_BY_MSGID);
				keys.add(fileMsgId);
				rec	= (DBMsgIDRecord) dbMsgIDProfile.getRecord(1, keys);
			}
			
			
//			DBMsgIDRecord rec = findProfileNameFromMsgId(fileMsgId);
			String splitMode = rec.getSplitMode();
//			System.out.println("set splitMode:" + splitMode);
			if(splitMode != null && !splitMode.isEmpty()) {
				sendPdsToServerService.switchMode(splitMode);
			}else {
				//if no specify mode setup, then will use default one.
				sendPdsToServerService.switchMode(sendByMsgIdModeString);
			}
			
			String folder = rec.getFolder();
			if(folder != null && !folder.isEmpty()){
				sendPdsToServerService.setFolderNameString(folder);				
			}
		} catch (ProfileException e1) {
			LogStream.writeExceptionStack(log.getFilePath(), e1);
		}
		
		
	}
	
	public DBMsgIDRecord findProfileNameFromMsgId(String msgId) throws ProfileException {
		@SuppressWarnings("rawtypes")
		Vector vec = new Vector();
		vec.add(DBMsgIDRecord.COMPARE_BY_TYPE);
		vec.add(msgId);
		DBMsgIDRecord rec = (DBMsgIDRecord)dbMsgIDProfile.getRecord(1,vec);
		return rec;
	}

	/**
	 * Must implement this, use it to close resources and
	 * clean up before the ecs2000 is terminated.
	 * Creation date: (11/10/2003 8:47:08 AM)
	 */
	public void exitApplication() {
		closeSFTP();
		setExiting(true);
		
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (10/23/2003 6:22:45 PM)
	 * @param newExiting boolean
	 */
	public void setExiting(boolean newExiting) {
		sftpSendThread.interrupt();
		exiting = newExiting;
		removeFileThread.setExiting(newExiting);
		removeBackupSrcFileThread.setExiting(newExiting);
		String exitMsg = "Set SFTP Send App to exit.";
		log.write(LogStream.WARNING_MESSAGE, exitMsg);
		System.out.println(exitMsg);
	}
	
	/**
	 * Sets the message property (ibm.disk.extensions.ColoredString) value.
	 * @param message The new value for the property.
	 * @see #getMessage
	 */
	public void setMessage(ibm.disk.extensions.ColoredString message) {
		ibm.disk.extensions.ColoredString oldValue = fieldMessage;
		fieldMessage = message;
		firePropertyChange("message", oldValue, message);
	}
	
	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return String
	 */
	public String getBlockName() {
		return blockName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param blockName String
	 */
	public void setBlockName(String blockName) {
		this.blockName = blockName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return String
	 */
	public String getErrorTitle() {
		return errorTitle;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param errorTitle String
	 */
	public void setErrorTitle(String errorTitle) {
		this.errorTitle = errorTitle;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return ibm.disk.utility.LogStream
	 */
	public ibm.disk.utility.LogStream getLog() {
		return log;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param log ibm.disk.utility.LogStream
	 */
	public void setLog(ibm.disk.utility.LogStream log) {
		this.log = log;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return java.lang.String
	 */
	public java.lang.String getSendingDirName() {
		return sendingDirName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param sendingDirName java.lang.String
	 */
	public void setSendingDirName(java.lang.String sendingDirName) {
		this.sendingDirName = sendingDirName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return java.lang.String
	 */
	public java.lang.String getSentDirName() {
		return sentDirName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param sentDirName java.lang.String
	 */
	public void setSentDirName(java.lang.String sentDirName) {
		this.sentDirName = sentDirName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return java.lang.String
	 */
	public java.lang.String getProblemDirName() {
		return problemDirName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param problemDirName java.lang.String
	 */
	public void setProblemDirName(java.lang.String problemDirName) {
		this.problemDirName = problemDirName;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return java.io.File
	 */
	public java.io.File getSendingDir() {
		return sendingDir;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param sendingDir java.io.File
	 */
	public void setSendingDir(java.io.File sendingDir) {
		this.sendingDir = sendingDir;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return java.io.File
	 */
	public java.io.File getSentDir() {
		return sentDir;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param sentDir java.io.File
	 */
	public void setSentDir(java.io.File sentDir) {
		this.sentDir = sentDir;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return java.io.File
	 */
	public java.io.File getProblemDir() {
		return problemDir;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param problemDir java.io.File
	 */
	public void setProblemDir(java.io.File problemDir) {
		this.problemDir = problemDir;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return int
	 */
	public int getHoursOfRetention() {
		return hoursOfRetention;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param hoursOfRetention int
	 */
	public void setHoursOfRetention(int hoursOfRetention) {
		this.hoursOfRetention = hoursOfRetention;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return ibm.disk.extensions.ColoredString
	 */
	public ibm.disk.extensions.ColoredString getFieldMessage() {
		return fieldMessage;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param fieldMessage ibm.disk.extensions.ColoredString
	 */
	public void setFieldMessage(ibm.disk.extensions.ColoredString fieldMessage) {
		this.fieldMessage = fieldMessage;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return boolean
	 */
	public boolean isBinaryFormat() {
		return binaryFormat;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param binaryFormat boolean
	 */
	public void setBinaryFormat(boolean binaryFormat) {
		this.binaryFormat = binaryFormat;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return long
	 */
	public long getFileCount() {
		return fileCount;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param fileCount long
	 */
	public void setFileCount(long fileCount) {
		this.fileCount = fileCount;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return SFTPServerProperties
	 */
	public SFTPServerProperties getPrimarySFTPServer() {
		return primarySFTPServer;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param primarySFTPServer SFTPServerProperties
	 */
	public void setPrimarySFTPServer(SFTPServerProperties primarySFTPServer) {
		this.primarySFTPServer = primarySFTPServer;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return SFTPServerProperties
	 */
	public SFTPServerProperties getBackupSFTPServer() {
		return backupSFTPServer;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param backupSFTPServer SFTPServerProperties
	 */
	public void setBackupSFTPServer(SFTPServerProperties backupSFTPServer) {
		this.backupSFTPServer = backupSFTPServer;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return SFTPServerProperties
	 */
	public SFTPServerProperties getCurrentSFTPServer() {
		return currentSFTPServer;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param currentSFTPServer SFTPServerProperties
	 */
	public void setCurrentSFTPServer(SFTPServerProperties currentSFTPServer) {
		SFTPServerProperties oldValue = this.currentSFTPServer;
		this.currentSFTPServer = currentSFTPServer;
		firePropertyChange("sftpServer", oldValue, currentSFTPServer);
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return Session
	 */
	public Session getSession() {
		return session;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param session Session
	 */
	public void setSession(Session session) {
		this.session = session;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @return ChannelSftp
	 */
	public ChannelSftp getChannel() {
		return channel;
	}

	/**
	 * Creation date: 2021-08-09 11:03:18
	 * @param channel ChannelSftp
	 */
	public void setChannel(ChannelSftp channel) {
		this.channel = channel;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return java.lang.String
	 */
	public java.lang.String getDbMsgIDProfileName() {
		return dbMsgIDProfileName;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param dbMsgIDProfileName java.lang.String
	 */
	public void setDbMsgIDProfileName(java.lang.String dbMsgIDProfileName) {
		this.dbMsgIDProfileName = dbMsgIDProfileName;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return ibm.disk.profiles.DiskProfile
	 */
	public ibm.disk.profiles.DiskProfile getDbMsgIDProfile() {
		return dbMsgIDProfile;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param dbMsgIDProfile ibm.disk.profiles.DiskProfile
	 */
	public void setDbMsgIDProfile(ibm.disk.profiles.DiskProfile dbMsgIDProfile) {
		this.dbMsgIDProfile = dbMsgIDProfile;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return RemoveFileThread
	 */
	public RemoveFileThread getRemoveFileThread() {
		return removeFileThread;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param removeFileThread RemoveFileThread
	 */
	public void setRemoveFileThread(RemoveFileThread removeFileThread) {
		this.removeFileThread = removeFileThread;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return int
	 */
	public int getChangeToIdle() {
		return changeToIdle;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param changeToIdle int
	 */
	public void setChangeToIdle(int changeToIdle) {
		this.changeToIdle = changeToIdle;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return int
	 */
	public int getIdleSendInterval() {
		return idleSendInterval;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param idleSendInterval int
	 */
	public void setIdleSendInterval(int idleSendInterval) {
		this.idleSendInterval = idleSendInterval;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return int
	 */
	public int getWaitingForWriteEnded() {
		return waitingForWriteEnded;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param waitingForWriteEnded int
	 */
	public void setWaitingForWriteEnded(int waitingForWriteEnded) {
		this.waitingForWriteEnded = waitingForWriteEnded;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return int
	 */
	public int getMaxLineLength() {
		return maxLineLength;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param maxLineLength int
	 */
	public void setMaxLineLength(int maxLineLength) {
		this.maxLineLength = maxLineLength;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return int
	 */
	public int getMaxReadFiles() {
		return maxReadFiles;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param maxReadFiles int
	 */
	public void setMaxReadFiles(int maxReadFiles) {
		this.maxReadFiles = maxReadFiles;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @return java.lang.String
	 */
	public java.lang.String getMachineName() {
		return machineName;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param machineName java.lang.String
	 */
	public void setMachineName(java.lang.String machineName) {
		this.machineName = machineName;
	}

	/**
	 * Creation date: 2021-08-10 16:48:57
	 * @param propertyChange java.beans.PropertyChangeSupport
	 */
	public void setPropertyChange(java.beans.PropertyChangeSupport propertyChange) {
		this.propertyChange = propertyChange;
	}
	
	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @return java.lang.String
	 */
	public java.lang.String getPdsDirName() {
		return pdsDirName;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @param pdsDirName java.lang.String
	 */
	public void setPdsDirName(java.lang.String pdsDirName) {
		this.pdsDirName = pdsDirName;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @return java.io.File
	 */
	public java.io.File getPdsDir() {
		return pdsDir;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @param pdsDir java.io.File
	 */
	public void setPdsDir(java.io.File pdsDir) {
		this.pdsDir = pdsDir;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @return long
	 */
	public long getLastSendTime() {
		return lastSendTime;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @param lastSendTime long
	 */
	public void setLastSendTime(long lastSendTime) {
		this.lastSendTime = lastSendTime;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @return java.lang.String
	 */
	public java.lang.String getSftpServerDirName() {
		return sftpServerDirName;
	}

	/**
	 * Creation date: 2021-08-12 09:13:14
	 * @param sftpServerDirName java.lang.String
	 */
	public void setSftpServerDirName(java.lang.String sftpServerDirName) {
		this.sftpServerDirName = sftpServerDirName;
	}

	/**
	 * Creation date: 2021-08-13 09:10:17
	 * @return ibm.disk.utility.LogStream
	 */
	public ibm.disk.utility.LogStream getLogRemove() {
		return logRemove;
	}

	/**
	 * Creation date: 2021-08-13 09:10:17
	 * @param logRemove ibm.disk.utility.LogStream
	 */
	public void setLogRemove(ibm.disk.utility.LogStream logRemove) {
		this.logRemove = logRemove;
	}

	/**
	 * Creation date: 2021-08-13 09:10:17
	 * @return Thread
	 */
	public Thread getSftpSendThread() {
		return sftpSendThread;
	}

	/**
	 * Creation date: 2021-08-13 09:10:17
	 * @param sftpSendThread Thread
	 */
	public void setSftpSendThread(Thread sftpSendThread) {
		this.sftpSendThread = sftpSendThread;
	}

	/**
	 * Creation date: 2021-08-13 09:19:56
	 * @return String
	 */
	public String getSftpConnected() {
		return sftpConnected;
	}

	/**
	 * Creation date: 2021-08-13 09:19:56
	 * @param sftpConnected String
	 */
	public void setSftpConnected(String sftpConnected) {
		String oldValue = this.sftpConnected;
		this.sftpConnected = sftpConnected;
		firePropertyChange("sftpConnected", oldValue, sftpConnected);
	}

	/**
	 * Insert the method's description here.
	 * Creation date: (10/17/2003 10:07:25 AM)
	 * @param ms int
	 */
	public void sleep(int ms) {
		try {
			Thread.sleep(ms);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public void writeToLog(int level, String msg) {
		if (log == null) {
//			if (loglevel == LogStream.DEBUG_MESSAGE) {
				System.out.println(msg);
//			}
		} else {
			log.write(level,msg);
		}
		setMessage(new ColoredString(StringMethods.getCurrentTimeStamp() + " " +msg,LogStream.MessageColors[level]));	
	}

	public static void main(java.lang.String[] args) {
		if (args.length < 2) {
			System.out.println("Invalid start parameters: ");
			System.out.println("java wdc.disk.ecs.apps.SFTPSend.SFTPSendApp <profile> <block name>");
			System.out.println("     where profile is the name (including path) of the profile containing the block name");
			System.out.println("           block name is the block in the profile which contains the configuration for sending.");
			System.exit(0);
		} else {
			SFTPSendApp sendit = new SFTPSendApp();
			try {
				sendit.initialize(args[0],args[1]);
			} catch (Throwable e) {
				System.out.println("Unable to initialize SFTPSendApp, exception=" + e.toString());
				e.printStackTrace();
				System.exit(0);
			}
			
			
			if((args.length == 2) || ((args.length > 2) && !"DEBUG".equals(args[2]))) {
				MonitorSftpSendRunnable runnable = new MonitorSftpSendRunnable(args[0], sendit);
				Thread monitorThread = new Thread(runnable);
				monitorThread.start();								
			}
			
			
		} 
		
	}


	public String getMainProfile() {
		return mainProfile;
	}


	public void setMainProfile(String mainProfile) {
		this.mainProfile = mainProfile;
	}


	public long getMainProfileLastModifyDate() {
		return mainProfileLastModifyDate;
	}


	public void setMainProfileLastModifyDate(long mainProfileLastModifyDate) {
		this.mainProfileLastModifyDate = mainProfileLastModifyDate;
	}
	
	
	
}
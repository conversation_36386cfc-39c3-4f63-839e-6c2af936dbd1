# MQ_Upload.yml 多环境配置改造方案

本文档旨在为 `MQ_Upload.yml` 设计一个清晰、可扩展的多环境配置方案，以消除混乱并简化不同部署环境的管理。

## 1. 核心策略

我们将遵循 `multi_env.md` 中提出的标准实践，采用 **基础配置 + 环境覆盖** 的策略。

- **基础配置文件**: `src/test/resources/MQ_Upload.yml` 将作为所有环境共享的通用配置。
- **环境特定配置文件**: 为每个环境创建一个独立的 `.yml` 文件，例如 `MQ_Upload.dev_windows.yml`。此文件仅包含需要覆盖基础配置的键值对。

应用程序在启动时，将首先加载基础配置，然后加载特定环境的配置，并将后者深度合并到基础配置中，从而生成最终生效的配置。

## 2. 新的文件结构

所有配置文件都将存放在 `src/test/resources/` 目录下。

```
/src/test/resources/
├── MQ_Upload.yml               # 基础配置 (通用)
├── MQ_Upload.dev_localhost.yml   # 开发环境 (本地) 覆盖配置
├── MQ_Upload.dev_windows.yml     # 开发环境 (Windows) 覆盖配置
├── MQ_Upload.prod_windows.yml    # 生产环境 (Windows) 覆盖配置
├── MQ_Upload.dev_DECO_linux.yml  # 开发环境 (DECO Linux) 覆盖配置
└── MQ_Upload.dev_TV_linux.yml    # 开发环境 (TV Linux) 覆盖配置
```

## 3. 基础配置 (MQ_Upload.yml)

这是新的 `MQ_Upload.yml` 文件内容。它基于原始文件，但移除了特定于环境的嵌套结构，并使用开发环境 (`DEV`) 的值作为默认值。

```yaml
# Base configuration for all environments.
# Specific values will be overridden by environment-specific files.

# Default environment-related paths (can be overridden)
mq_storage_root: "C:\opt\ecs\storage\mq"
app_root: "C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao"

# Producer Configuration (shared across all environments)
producer:
  pds_dir:
    base: mq_storage_root
    relative: ""
  binary_format: true
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  to_dir:
    base: mq_storage_root
    relative: "sent"
  db_msg_id_profile:
    base: app_root
    relative: "profiles\pdsMsgId.pro"
  monitor_interval: 10
  housekeeping:
      enabled: false
      interval_hours: 24
      retention_days: 30
      directories:
        - sent
        - problem

# Default RabbitMQ configuration (will be overridden by most environments)
rabbitmq:
  host: "csf-md-rabitmq1.ad.shared"
  port: 5672
  username: "mqupload"
  password: "upload66"
  exchange: "ecs.direct"
  queue_config_path:
    base: app_root
    relative: "src\test\resources\test-queue-config.csv"

table_schema:
  message_mappings_path:
    base: app_root
    relative: "src\test\resources\mapper\message_mappings.json"
  profile_base_path:
    base: app_root
    relative: "profiles\generatedProfiles"

# Consumer Configuration (shared across all environments)
consumer:
  mybatis_config_path:
    base: app_root
    relative: "src\test\resources\mybatis-config.xml"
  parser_class_name: "wdc.disk.ecs.apps.MQUpload.parser.AsciiMessageParser"
  mappings_path:
    base: app_root
    relative: "src\test\resources\mapper\message_mappings.json"
  profiles_path:
    base: app_root
    relative: "profiles\generatedProfiles"
  queues:
    # Full list of queues remains here as it is common to all environments
    - name: "309"
      description: "Queue for 309 processing"
      enabled: true
      # ... (and all other queue definitions)
```

## 4. 环境特定覆盖文件（示例）

以下是几个环境特定配置文件的示例，展示了它们如何只定义差异化内容。

### `MQ_Upload.prod_windows.yml`
此文件用于 Windows 生产环境。它覆盖了文件路径和 RabbitMQ 服务器地址。

```yaml
# Overrides for Windows Production Environment

mq_storage_root: "C:\opt\ecs\storage\mq\prod"
app_root: "C:\opt\ecs\apps\mqupload"

rabbitmq:
  host: "csf-mp-talend03.ad.shared"
  queue_config_path:
    relative: "config\queue-config.csv"
```

### `MQ_Upload.dev_DECO_linux.yml`
此文件用于 DECO Linux 开发环境。注意路径使用了 Linux 格式 (`/`)。

```yaml
# Overrides for DECO Linux Development Environment

mq_storage_root: "/opt/ecs/storage/mq_deco"
app_root: "/opt/ecs/apps/mqupload_dev"

rabbitmq:
  # Assuming DECO dev uses the same RabbitMQ as other dev envs
  host: "csf-md-rabitmq1.ad.shared"
  queue_config_path:
    # Path separator changed for Linux
    relative: "src/test/resources/test-queue-config.csv"

table_schema:
  message_mappings_path:
    relative: "src/test/resources/mapper/message_mappings.json"
  profile_base_path:
    relative: "profiles/generatedProfiles"

consumer:
  mybatis_config_path:
    relative: "src/test/resources/mybatis-config.xml"
  mappings_path:
    relative: "src/test/resources/mapper/message_mappings.json"
  profiles_path:
    relative: "profiles/generatedProfiles"
```

### `MQ_Upload.dev_localhost.yml`
此文件用于开发人员本地机器，可能连接到一个本地运行的 RabbitMQ 实例。

```yaml
# Overrides for Localhost Development

# Paths might be the same as dev_windows, so no override needed unless different
# mq_storage_root: "C:\..."
# app_root: "C:\..."

rabbitmq:
  host: "localhost"
  port: 5672
```

## 5. 实施要求

要使此方案生效，应用程序的配置加载逻辑需要更新，以实现：
1.  读取 `APP_ENV` 环境变量或 `app.env` 系统属性来确定当前环境（例如 `prod_windows`）。
2.  首先加载 `MQ_Upload.yml`。
3.  然后根据环境名称加载对应的 `MQ_Upload.{env}.yml`。
4.  将环境配置深度合并到基础配置上。

可以完全参考 `rsc/doc/instructions/multi_env.md` 中提供的 `ConfigLoader.java` 示例代码，只需将文件名 `config.yml` 替换为 `MQ_Upload.yml` 即可。

```
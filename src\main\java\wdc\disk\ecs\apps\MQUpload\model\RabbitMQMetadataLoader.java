package wdc.disk.ecs.apps.MQUpload.model;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

public class RabbitMQMetadataLoader {
    private List<QueueMetadataEntry> entries = new ArrayList<>();
    
    public static RabbitMQMetadataLoader loadFromCsv(String filePath) throws IOException {
        RabbitMQMetadataLoader rabbitMQMetadataLoader = new RabbitMQMetadataLoader();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String headerLine = reader.readLine();
            if (!"Name,Type,Exchange,ExchangeType,RoutingKey,Durable,Exclusive,AutoDelete,QuorumSize".equals(headerLine)) {
                throw new IOException("Invalid CSV format: header line does not match expected format");
            }
            String line;
            while ((line = reader.readLine()) != null) {
                String[] values = line.split(",");
                if (values.length >= 9) { // Updated to account for new fields
                    QueueMetadataEntry entry = new QueueMetadataEntry();
                    entry.setName(values[0].trim());
                    entry.setType(values[1].trim());
                    entry.setExchange(values[2].trim());
                    entry.setExchangeType(values[3].trim());
                    entry.setRoutingKey(values[4].trim());
                    entry.setDurable(Boolean.parseBoolean(values[5].trim()));
                    entry.setExclusive(Boolean.parseBoolean(values[6].trim()));
                    entry.setAutoDelete(Boolean.parseBoolean(values[7].trim()));
                    entry.setQuorumSize(Integer.parseInt(values[8].trim()));
               
                    
                    rabbitMQMetadataLoader.entries.add(entry);
                }
            }
        }
        return rabbitMQMetadataLoader;
    }
    
    public List<QueueMetadataEntry> getEntries() {
        return entries;
    }

    public void setEntries(List<QueueMetadataEntry> entries) {
        this.entries = entries;
    }
}

package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for DECOGAUGE_26
 */
public class DECOGAUGE_26  {
    private java.time.LocalDateTime dtinsert;
    private Short spindle;
    private String unitid;
    private java.time.LocalDateTime starttime;
    private java.time.LocalDateTime stoptime;
    private Short datapoint;
    private String surface;
    private String headid;
    private String finaljudgement;
    private Double lfMaster;
    private Double lfSubmaster;
    private Double lfCorrection;
    private Double lfLowlimit;
    private Double lfHighlimit;
    private String lfJudgement;
    private Double hfMaster;
    private Double hfSubmaster;
    private Double hfCorrection;
    private Double hfLowlimit;
    private Double hfHighlimit;
    private String hfJudgement;
    private Double resMaster;
    private Double resSubmaster;
    private Double resCorrection;
    private Double resLowlimit;
    private Double resHighlimit;
    private String resJudgement;
    private Double owMaster;
    private Double owSubmaster;
    private Double owCorrection;
    private Double owLowlimit;
    private Double owHighlimit;
    private String owJudgement;
    private Double cnMaster;
    private Double cnSubmaster;
    private Double cnCorrection;
    private Double cnLowlimit;
    private Double cnHighlimit;
    private String cnJudgement;
    private Double snMaster;
    private Double snSubmaster;
    private Double snCorrection;
    private Double snLowlimit;
    private Double snHighlimit;
    private String snJudgement;
    private Double devMaster;
    private Double devSubmaster;
    private Double devCorrection;
    private Double devLowlimit;
    private Double devHighlimit;
    private String devJudgement;
    private Double pwMaster;
    private Double pwSubmaster;
    private Double pwCorrection;
    private Double pwLowlimit;
    private Double pwHighlimit;
    private String pwJudgement;
    private Double vasymMaster;
    private Double vasymSubmaster;
    private Double vasymCorrection;
    private Double vasymLowlimit;
    private Double vasymHighlimit;
    private String vasymJudgement;
    private Integer correlid;

    // Default constructor
    public DECOGAUGE_26() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public String getUnitid() {
        return unitid;
    }

    public void setUnitid(String unitid) {
        this.unitid = unitid;
    }

    public java.time.LocalDateTime getStarttime() {
        return starttime;
    }

    public void setStarttime(java.time.LocalDateTime starttime) {
        this.starttime = starttime;
    }

    public java.time.LocalDateTime getStoptime() {
        return stoptime;
    }

    public void setStoptime(java.time.LocalDateTime stoptime) {
        this.stoptime = stoptime;
    }

    public Short getDatapoint() {
        return datapoint;
    }

    public void setDatapoint(Short datapoint) {
        this.datapoint = datapoint;
    }

    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    public String getHeadid() {
        return headid;
    }

    public void setHeadid(String headid) {
        this.headid = headid;
    }

    public String getFinaljudgement() {
        return finaljudgement;
    }

    public void setFinaljudgement(String finaljudgement) {
        this.finaljudgement = finaljudgement;
    }

    public Double getLfMaster() {
        return lfMaster;
    }

    public void setLfMaster(Double lfMaster) {
        this.lfMaster = lfMaster;
    }

    public Double getLfSubmaster() {
        return lfSubmaster;
    }

    public void setLfSubmaster(Double lfSubmaster) {
        this.lfSubmaster = lfSubmaster;
    }

    public Double getLfCorrection() {
        return lfCorrection;
    }

    public void setLfCorrection(Double lfCorrection) {
        this.lfCorrection = lfCorrection;
    }

    public Double getLfLowlimit() {
        return lfLowlimit;
    }

    public void setLfLowlimit(Double lfLowlimit) {
        this.lfLowlimit = lfLowlimit;
    }

    public Double getLfHighlimit() {
        return lfHighlimit;
    }

    public void setLfHighlimit(Double lfHighlimit) {
        this.lfHighlimit = lfHighlimit;
    }

    public String getLfJudgement() {
        return lfJudgement;
    }

    public void setLfJudgement(String lfJudgement) {
        this.lfJudgement = lfJudgement;
    }

    public Double getHfMaster() {
        return hfMaster;
    }

    public void setHfMaster(Double hfMaster) {
        this.hfMaster = hfMaster;
    }

    public Double getHfSubmaster() {
        return hfSubmaster;
    }

    public void setHfSubmaster(Double hfSubmaster) {
        this.hfSubmaster = hfSubmaster;
    }

    public Double getHfCorrection() {
        return hfCorrection;
    }

    public void setHfCorrection(Double hfCorrection) {
        this.hfCorrection = hfCorrection;
    }

    public Double getHfLowlimit() {
        return hfLowlimit;
    }

    public void setHfLowlimit(Double hfLowlimit) {
        this.hfLowlimit = hfLowlimit;
    }

    public Double getHfHighlimit() {
        return hfHighlimit;
    }

    public void setHfHighlimit(Double hfHighlimit) {
        this.hfHighlimit = hfHighlimit;
    }

    public String getHfJudgement() {
        return hfJudgement;
    }

    public void setHfJudgement(String hfJudgement) {
        this.hfJudgement = hfJudgement;
    }

    public Double getResMaster() {
        return resMaster;
    }

    public void setResMaster(Double resMaster) {
        this.resMaster = resMaster;
    }

    public Double getResSubmaster() {
        return resSubmaster;
    }

    public void setResSubmaster(Double resSubmaster) {
        this.resSubmaster = resSubmaster;
    }

    public Double getResCorrection() {
        return resCorrection;
    }

    public void setResCorrection(Double resCorrection) {
        this.resCorrection = resCorrection;
    }

    public Double getResLowlimit() {
        return resLowlimit;
    }

    public void setResLowlimit(Double resLowlimit) {
        this.resLowlimit = resLowlimit;
    }

    public Double getResHighlimit() {
        return resHighlimit;
    }

    public void setResHighlimit(Double resHighlimit) {
        this.resHighlimit = resHighlimit;
    }

    public String getResJudgement() {
        return resJudgement;
    }

    public void setResJudgement(String resJudgement) {
        this.resJudgement = resJudgement;
    }

    public Double getOwMaster() {
        return owMaster;
    }

    public void setOwMaster(Double owMaster) {
        this.owMaster = owMaster;
    }

    public Double getOwSubmaster() {
        return owSubmaster;
    }

    public void setOwSubmaster(Double owSubmaster) {
        this.owSubmaster = owSubmaster;
    }

    public Double getOwCorrection() {
        return owCorrection;
    }

    public void setOwCorrection(Double owCorrection) {
        this.owCorrection = owCorrection;
    }

    public Double getOwLowlimit() {
        return owLowlimit;
    }

    public void setOwLowlimit(Double owLowlimit) {
        this.owLowlimit = owLowlimit;
    }

    public Double getOwHighlimit() {
        return owHighlimit;
    }

    public void setOwHighlimit(Double owHighlimit) {
        this.owHighlimit = owHighlimit;
    }

    public String getOwJudgement() {
        return owJudgement;
    }

    public void setOwJudgement(String owJudgement) {
        this.owJudgement = owJudgement;
    }

    public Double getCnMaster() {
        return cnMaster;
    }

    public void setCnMaster(Double cnMaster) {
        this.cnMaster = cnMaster;
    }

    public Double getCnSubmaster() {
        return cnSubmaster;
    }

    public void setCnSubmaster(Double cnSubmaster) {
        this.cnSubmaster = cnSubmaster;
    }

    public Double getCnCorrection() {
        return cnCorrection;
    }

    public void setCnCorrection(Double cnCorrection) {
        this.cnCorrection = cnCorrection;
    }

    public Double getCnLowlimit() {
        return cnLowlimit;
    }

    public void setCnLowlimit(Double cnLowlimit) {
        this.cnLowlimit = cnLowlimit;
    }

    public Double getCnHighlimit() {
        return cnHighlimit;
    }

    public void setCnHighlimit(Double cnHighlimit) {
        this.cnHighlimit = cnHighlimit;
    }

    public String getCnJudgement() {
        return cnJudgement;
    }

    public void setCnJudgement(String cnJudgement) {
        this.cnJudgement = cnJudgement;
    }

    public Double getSnMaster() {
        return snMaster;
    }

    public void setSnMaster(Double snMaster) {
        this.snMaster = snMaster;
    }

    public Double getSnSubmaster() {
        return snSubmaster;
    }

    public void setSnSubmaster(Double snSubmaster) {
        this.snSubmaster = snSubmaster;
    }

    public Double getSnCorrection() {
        return snCorrection;
    }

    public void setSnCorrection(Double snCorrection) {
        this.snCorrection = snCorrection;
    }

    public Double getSnLowlimit() {
        return snLowlimit;
    }

    public void setSnLowlimit(Double snLowlimit) {
        this.snLowlimit = snLowlimit;
    }

    public Double getSnHighlimit() {
        return snHighlimit;
    }

    public void setSnHighlimit(Double snHighlimit) {
        this.snHighlimit = snHighlimit;
    }

    public String getSnJudgement() {
        return snJudgement;
    }

    public void setSnJudgement(String snJudgement) {
        this.snJudgement = snJudgement;
    }

    public Double getDevMaster() {
        return devMaster;
    }

    public void setDevMaster(Double devMaster) {
        this.devMaster = devMaster;
    }

    public Double getDevSubmaster() {
        return devSubmaster;
    }

    public void setDevSubmaster(Double devSubmaster) {
        this.devSubmaster = devSubmaster;
    }

    public Double getDevCorrection() {
        return devCorrection;
    }

    public void setDevCorrection(Double devCorrection) {
        this.devCorrection = devCorrection;
    }

    public Double getDevLowlimit() {
        return devLowlimit;
    }

    public void setDevLowlimit(Double devLowlimit) {
        this.devLowlimit = devLowlimit;
    }

    public Double getDevHighlimit() {
        return devHighlimit;
    }

    public void setDevHighlimit(Double devHighlimit) {
        this.devHighlimit = devHighlimit;
    }

    public String getDevJudgement() {
        return devJudgement;
    }

    public void setDevJudgement(String devJudgement) {
        this.devJudgement = devJudgement;
    }

    public Double getPwMaster() {
        return pwMaster;
    }

    public void setPwMaster(Double pwMaster) {
        this.pwMaster = pwMaster;
    }

    public Double getPwSubmaster() {
        return pwSubmaster;
    }

    public void setPwSubmaster(Double pwSubmaster) {
        this.pwSubmaster = pwSubmaster;
    }

    public Double getPwCorrection() {
        return pwCorrection;
    }

    public void setPwCorrection(Double pwCorrection) {
        this.pwCorrection = pwCorrection;
    }

    public Double getPwLowlimit() {
        return pwLowlimit;
    }

    public void setPwLowlimit(Double pwLowlimit) {
        this.pwLowlimit = pwLowlimit;
    }

    public Double getPwHighlimit() {
        return pwHighlimit;
    }

    public void setPwHighlimit(Double pwHighlimit) {
        this.pwHighlimit = pwHighlimit;
    }

    public String getPwJudgement() {
        return pwJudgement;
    }

    public void setPwJudgement(String pwJudgement) {
        this.pwJudgement = pwJudgement;
    }

    public Double getVasymMaster() {
        return vasymMaster;
    }

    public void setVasymMaster(Double vasymMaster) {
        this.vasymMaster = vasymMaster;
    }

    public Double getVasymSubmaster() {
        return vasymSubmaster;
    }

    public void setVasymSubmaster(Double vasymSubmaster) {
        this.vasymSubmaster = vasymSubmaster;
    }

    public Double getVasymCorrection() {
        return vasymCorrection;
    }

    public void setVasymCorrection(Double vasymCorrection) {
        this.vasymCorrection = vasymCorrection;
    }

    public Double getVasymLowlimit() {
        return vasymLowlimit;
    }

    public void setVasymLowlimit(Double vasymLowlimit) {
        this.vasymLowlimit = vasymLowlimit;
    }

    public Double getVasymHighlimit() {
        return vasymHighlimit;
    }

    public void setVasymHighlimit(Double vasymHighlimit) {
        this.vasymHighlimit = vasymHighlimit;
    }

    public String getVasymJudgement() {
        return vasymJudgement;
    }

    public void setVasymJudgement(String vasymJudgement) {
        this.vasymJudgement = vasymJudgement;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



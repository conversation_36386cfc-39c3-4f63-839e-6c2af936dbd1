package wdc.disk.ecs.apps.MQUpload.parser;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

public class AsciiMessageParserTest {
    private MessageParser parser;
    private static final String VALID_MESSAGE = 
        "2025-03-12-16.18.45.000000 FTPTAPE PDKJ 7 */FTPTAPE/YAC-3045R/20307Y1100307L0419/FTP/175186/0/FTP Tape Changed";
    private static final DateTimeFormatter DATE_FORMAT = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd-HH.mm.ss.SSSSSS");

    @BeforeEach
    public void setUp() {
        parser = new AsciiMessageParser();
    }

    @Test
    public void testParseValidMessage() {
        ParseResult result = parser.parse(VALID_MESSAGE);
        
        String[] expectedFields = {
            "FTPTAPE",
            "YAC-3045R",
            "20307Y1100307L0419",
            "FTP",
            "175186",
            "0",
            "FTP Tape Changed"
        };
        
        LocalDateTime expectedDateTime = 
            LocalDateTime.parse("2025-03-12-16.18.45.000000", DATE_FORMAT);
        
        assertNotNull(result.getParsedFields());
        assertArrayEquals(expectedFields, result.getParsedFields());
        assertEquals(expectedDateTime, result.getDateTime());
        assertEquals("FTPTAPE", result.getTabCode());  
    }

    @Test
    public void testParseNullMessage() {
        ParseResult result = parser.parse(null);
        assertNull(result.getParsedFields());
        assertNull(result.getDateTime());
        assertNull(result.getTabCode());  
    }

    @Test
    public void testParseEmptyMessage() {
        ParseResult result = parser.parse("");
        assertNull(result.getParsedFields());
        assertNull(result.getDateTime());
    }

    @Test
    public void testParseMessageWithInvalidDateTime() {
        String message = "invalid-datetime FTPTAPE PDKJ 7 */field1/field2";
        ParseResult result = parser.parse(message);
        assertNull(result.getParsedFields());
        assertNull(result.getDateTime());
    }

    @Test
    public void testParseMessageWithoutDelimiterMarker() {
        String message = "2025-03-12-16.18.45.000000 FTPTAPE PDKJ 7 FTPTAPE";
        ParseResult result = parser.parse(message);
        assertNull(result.getParsedFields());
    }

    @Test
    public void testParseMessageWithSpaceDelimiter() {
        String message = "2025-03-12-16.18.45.000000 FTPTAPE PDKJ 7 * field1 field2";
        ParseResult result = parser.parse(message);
        assertNull(result.getParsedFields());
    }

    @Test
    public void testParseMessageWithIncompleteHeader() {
        String message = "2025-03-12-16.18.45.000000 FTPTAPE */field1/field2";
        ParseResult result = parser.parse(message);
        assertNull(result.getParsedFields());
    }

    @Test
    public void testParseMessageExceedingMaxArraySize() {
        StringBuilder message = new StringBuilder("2025-03-12-16.18.45.000000 FTPTAPE PDKJ 2000 */");
        for (int i = 0; i < 2000; i++) {
            message.append("field").append(i).append("/");
        }
        
        ParseResult result = parser.parse(message.toString());
        assertNotNull(result.getParsedFields());
        assertTrue(result.getParsedFields().length <= 1024);
    }

    @Test
    public void testParseMessageWithEmptyFields() {
        String message = "2025-03-12-16.18.45.000000 FTPTAPE PDKJ 3 */field1//field3";
        ParseResult result = parser.parse(message);
        
        String[] expectedFields = {"field1","","field3"};
        assertNotNull(result.getParsedFields());
        assertArrayEquals(expectedFields, result.getParsedFields());
    }

    @Test
    public void testParseMessageWithTrailingDelimiter() {
        String message = "2025-03-12-16.18.45.000000 FTPTAPE PDKJ 2 */field1/field2/";
        ParseResult result = parser.parse(message);
        
        String[] expectedFields = {"field1", "field2"};
        assertNotNull(result.getParsedFields());
        assertArrayEquals(expectedFields, result.getParsedFields());
    }
}





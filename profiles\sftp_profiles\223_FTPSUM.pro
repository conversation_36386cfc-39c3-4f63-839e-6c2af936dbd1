*| ColumnName            |  DBColumnType  |UniqueIndex |  DBColumnLength  |  SystemType      |BinaryLength|ColumnNo|  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp     |            |   4              |  System.DateTime |4           |  0     |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp     |    Y       |   4              |  System.DateTime |4           |  1     |         |  0      |  1      |  
|  LOT                   |  Char          |    Y       |   10             |  System.String   |10          |  2     |         |  0      |  2      |  
|  PRODUCT               |  Char          |            |   6              |  System.String   |6           |  3     |         |  0      |  3      |  
|  RESOURCE              |  Char          |            |   10             |  System.String   |10          |  4     |         |  0      |  4      |  
|  RECIPE                |  SmallInt      |            |   2              |  System.Int16    |2           |  5     |         |  0      |  5      |  
|  MEAN_RPM_ID           |  Double        |            |   8              |  System.Double   |8           |  6     |         |  0      |  6      |  
|  MEAN_RPM_OD           |  Double        |            |   8              |  System.Double   |8           |  7     |         |  0      |  7      |  
|  MEAN_F_LOAD           |  Double        |            |   8              |  System.Double   |8           |  8     |         |  0      |  8      |  
|  MEAN_R_LOAD           |  Double        |            |   8              |  System.Double   |8           |  9     |         |  0      |  9      |  
|  MEAN_PT5_COUNT        |  Double        |            |   8              |  System.Double   |8           |  10    |         |  0      |  10     |  
|  MEAN_PT3_COUNT        |  Double        |            |   8              |  System.Double   |8           |  11    |         |  0      |  11     |  
|  MEAN_LOADTIME         |  Double        |            |   8              |  System.Double   |8           |  12    |         |  0      |  12     |  
|  SIGMA_RPM_ID          |  Double        |            |   8              |  System.Double   |8           |  13    |         |  0      |  13     |  
|  SIGMA_RPM_OD          |  Double        |            |   8              |  System.Double   |8           |  14    |         |  0      |  14     |  
|  SIGMA_F_LOAD          |  Double        |            |   8              |  System.Double   |8           |  15    |         |  0      |  15     |  
|  SIGMA_R_LOAD          |  Double        |            |   8              |  System.Double   |8           |  16    |         |  0      |  16     |  
|  SIGMA_PT5_COUNT       |  Double        |            |   8              |  System.Double   |8           |  17    |         |  0      |  17     |  
|  SIGMA_PT3_COUNT       |  Double        |            |   8              |  System.Double   |8           |  18    |         |  0      |  18     |  
|  SIGMA_LOADTIME        |  Double        |            |   8              |  System.Double   |8           |  19    |         |  0      |  19     |  
|  FTPTAPE               |  Char          |            |   18             |  System.String   |18          |  20    |         |  0      |  20     |  
|  WIPETAPE              |  Char          |            |   18             |  System.String   |18          |  21    |         |  0      |  21     |  

package wdc.disk.ecs.apps.SFTPSend.util;

public class SendWarningMsgEntity {
	public enum Type{ fileTimeout, folderQtyExceed}
	
	private String folderName;
	private Type type;
	private String comment;
	
	public SendWarningMsgEntity(String folderName, Type type, String comment) {
		super();
		this.folderName = folderName;
		this.type = type;
		this.comment = comment;
	}

	public String getFolderName() {
		return folderName;
	}

	public void setFolderName(String folderName) {
		this.folderName = folderName;
	}

	public Type getType() {
		return type;
	}

	public void setType(Type type) {
		this.type = type;
	}

	public String getComment() {
		return comment;
	}

	public void setComment(String comment) {
		this.comment = comment;
	}

	@Override
	public String toString() {
		String folderName = String.format("%-20s", getFolderName());
		String type = String.format("%-20s", getType());
		String comment = String.format("%-20s", getComment());
		return folderName + "     " + type + "     " + comment + "."; 
	}
	
}

# Profile Conversion Flow Design

## 1. Objective

The goal of this process is to automate the enhancement of binary data profiles (`.pro` files). The enhancement involves adding a `UniIdx` column to the profile, which indicates the columns that form the primary or a unique key for the corresponding database table. This ensures that data processing systems can correctly identify unique records.

## 2. Input

The process is triggered by a JSON message with the following structure:

```json
{
  "tabCode": "BANDRSLT_8",
  "mqMessageId": "8",
  "msgType": "Binary",
  "tableName": "BANDRSLT"
}
```

- `tabCode`: An identifier for the data type.
- `mqMessageId`: The message ID, used to locate the profile file.
- `msgType`: The type of the message (e.g., "Binary").
- `tableName`: The name of the DB2 table associated with the data.
- please be sure the tabCode and tableName looks similar, for example, if the tabCode is GLDRSLT_2, the tableName is GLDRSLT.But they are not the same.

## 3. Process Flow

The conversion process will follow these steps:

### Step 1: Identify the Profile File

Based on the input JSON, the system will locate the relevant **source** profile file. The file path is constructed using the `mqMessageId` and `tableName`.

- **Pattern:** `{mqMessageId}_{tableName}.pro`
- **Directory:** `C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\profiles\sftp_profiles\`
- **Example:** For the sample input, the file path will be `C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\profiles\sftp_profiles\8_BANDRSLT.pro`.

### Step 2: Retrieve Table Metadata from DB2

The system will fetch the schema information for the table specified in `tableName` to identify the columns that constitute a unique key (PRIMARY KEY or UNIQUE constraint).

This is done by executing a SQL query using the `run_sql` tool, which is part of the `db2-mcp` toolset. The tool handles the database connection automatically, so you will not be asked for connection details.

**SQL Query:**
The following query identifies all columns that are part of a unique index (either a primary key or a unique constraint).

```sql
SELECT c.COLNAME
FROM SYSCAT.INDEXES i
JOIN SYSCAT.INDEXCOLUSE c ON i.INDNAME = c.INDNAME AND i.INDSCHEMA = c.INDSCHEMA
WHERE i.TABNAME = '[tableName]'
  AND i.UNIQUERULE IN ('P', 'U')
ORDER BY c.COLSEQ
```

### Step 3: Read and Parse the Existing Profile

The system will read the contents of the existing profile file (e.g., `8_BANDRSLT.pro`) using the `read_file` tool. The file is a pipe-delimited text file which will be parsed to extract the definition for each column.

### Step 4: Generate the New Profile

A new profile will be generated by augmenting the information from the old profile with the unique key data retrieved from the database.

1.  **Create Header:** Two new header lines will be added at the top of the file:
    - A line indicating the table and format: `* Table: {schema}.{tableName}(BINARY format)`
    - A well-aligned column header line: `*| ColumnName | DBColType |DBColLen| SysType |BinLength|ColNo|NULLS |StartIdx |ValueIdx| UniIdx |`
2.  **Add `UniIdx` Column:** A new column, `UniIdx`, will be added to the profile data.
3.  **Mark Key Columns:** For each column in the profile, the system will check if it is part of the unique key identified in Step 2.
    - If the column is part of the key, its `UniIdx` value will be set to `Y`.
    - Otherwise, the value will be empty.
4.  **Format Output:** The new profile data rows will be formatted and padded to align with the new column headers.

### Step 5: Save the New Profile

The newly generated profile content will be saved to the `profiles/generatedProfiles/` directory. The **output** file name is constructed using the `mqMessageId` and `tabCode`.

- **Important Note on Naming:**
  - The **source** profile uses `{mqMessageId}_{tableName}.pro`.
  - The **generated** profile uses `{mqMessageId}_{tabCode}.pro`.

- **New File Name Pattern:** `{mqMessageId}_{tabCode}.pro`
- **Example:** For the input `{"tabCode": "BANDRSLT_8", "mqMessageId": "8"}`, the new file will be named `8_BANDRSLT_8.pro`.

## 4. Example Walkthrough

**Input:** `{"tabCode": "BANDRSLT_8", "mqMessageId": "8", "msgType": "Binary", "tableName": "BANDRSLT"}`

1.  **Profile File:** The source file `C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao\profiles\sftp_profiles\8_BANDRSLT.pro` is identified (using `tableName`).


2.  **DB Metadata:** The system connects to DB2 and queries the metadata for the `BANDRSLT` table. Let's assume the query returns that `SPINDLE`, `TESTNUM`, and `BANDNUM` form a unique key.

3.  **Read Profile:** The content of `8_BANDRSLT.pro` is read.

4.  **Generate New Profile:** The new profile is generated with the requested formatting and headers.

    **Original `8_BANDRSLT.pro`:**
    ```
    *| ColumnName | DBColType |DBColLen| SysType |BinLength|ColNo|NULLS |StartIdx |ValueIdx| UniIdx |

    |  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  0      |  Y      |  0      |  0      |
    |  TESTNUM          |  Integer     |  4      |  System.Int32     |  4      |  1      |  Y      |  8      |  1      |
    |  BANDNUM          |  SmallInt    |  2      |  System.Int16     |  2      |  2      |  Y      |  16     |  2      |
    ...
    ```

    **New Generated Profile (e.g., `8_BANDRSLT_8.pro`):**
    ```
    * Table: DB2SYS.BANDRSLT(BINARY format)
    *| ColumnName       | DBColType | DBColLen | SysType       | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
    | SPINDLE          | SmallInt  | 2        | System.Int16  | 2         | 0     | Y     | 0        | 0        | Y      |
    | TESTNUM          | Integer   | 4        | System.Int32  | 4         | 1     | Y     | 8        | 1        | Y      |
    | BANDNUM          | SmallInt  | 2        | System.Int16  | 2         | 2     | Y     | 16       | 2        | Y      |
    | REQOPERNUM       | SmallInt  | 2        | System.Int16  | 2         | 3     | Y     | 20       | 3        |        |
    | TOPRESULT        | Double    | 8        | System.Double | 8         | 4     |       | 24       | 4        |        |
    | BOTTOMRESULT     | Double    | 8        | System.Double | 8         | 5     |       | 40       | 5        |        |
    | DTINSERT         | Timestamp | 4        | System.DateTime| 4        | 6     |       | 0        | 6        |        |
    | CORRELID         | Integer   | 4        | System.Int32  | 4         | 7     |       | 0        | 7        |        |
    ```

5.  **Save:** The new content is written to the target file.
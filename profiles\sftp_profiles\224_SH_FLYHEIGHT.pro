*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |  Y      |  0      |  1      |  
|  IBMSITE               |  Char        |  4      |  System.String         |  4      |  2      |  Y      |  0      |  2      |  
|  STARTDATE             |  Date        |  4      |  System.DateTime       |  4      |  3      |  Y      |  0      |  3      |  
|  STARTTIME             |  Time        |  3      |  System.TimeSpan       |  3      |  4      |  Y      |  0      |  4      |  
|  ORIGID                |  Char        |  17     |  System.String         |  17     |  5      |  Y      |  0      |  5      |  
|  FINISDATE             |  Date        |  4      |  System.DateTime       |  4      |  6      |  Y      |  0      |  6      |  
|  FINISTIME             |  Time        |  3      |  System.TimeSpan       |  3      |  7      |  Y      |  0      |  7      |  
|  OPERATOR              |  Char        |  7      |  System.String         |  7      |  8      |  Y      |  0      |  8      |  
|  TESTERID              |  SmallInt    |  2      |  System.Int16          |  2      |  9      |  Y      |  0      |  9      |  
|  FCODEA                |  Char        |  4      |  System.String         |  4      |  10     |  Y      |  0      |  10     |  
|  TESTCODE              |  Char        |  4      |  System.String         |  4      |  11     |  Y      |  0      |  11     |  
|  SERIALNO              |  Char        |  7      |  System.String         |  7      |  12     |  Y      |  0      |  12     |  
|  SHIFT                 |  SmallInt    |  2      |  System.Int16          |  2      |  13     |  Y      |  0      |  13     |  
|  SETUP                 |  Char        |  20     |  System.String         |  20     |  14     |  Y      |  0      |  14     |  
|  SLIDER                |  Char        |  10     |  System.String         |  10     |  15     |  Y      |  0      |  15     |  
|  DIRECTION             |  Char        |  3      |  System.String         |  3      |  16     |  Y      |  0      |  16     |  
|  RADIUS                |  Double      |  8      |  System.Double         |  8      |  17     |  Y      |  0      |  17     |  
|  SKEW                  |  Double      |  8      |  System.Double         |  8      |  18     |  Y      |  0      |  18     |  
|  VELOCITY              |  Double      |  8      |  System.Double         |  8      |  19     |  Y      |  0      |  19     |  
|  IT                    |  Double      |  8      |  System.Double         |  8      |  20     |  Y      |  0      |  20     |  
|  IL                    |  Double      |  8      |  System.Double         |  8      |  21     |  Y      |  0      |  21     |  
|  OT                    |  Double      |  8      |  System.Double         |  8      |  22     |  Y      |  0      |  22     |  
|  OL                    |  Double      |  8      |  System.Double         |  8      |  23     |  Y      |  0      |  23     |  
|  CT                    |  Double      |  8      |  System.Double         |  8      |  24     |  Y      |  0      |  24     |  
|  CL                    |  Double      |  8      |  System.Double         |  8      |  25     |  Y      |  0      |  25     |  
|  CAM                   |  Double      |  8      |  System.Double         |  8      |  26     |  Y      |  0      |  26     |  
|  ICRN                  |  Double      |  8      |  System.Double         |  8      |  27     |  Y      |  0      |  27     |  
|  OCRN                  |  Double      |  8      |  System.Double         |  8      |  28     |  Y      |  0      |  28     |  
|  CCRN                  |  Double      |  8      |  System.Double         |  8      |  29     |  Y      |  0      |  29     |  
|  CPITCH                |  Double      |  8      |  System.Double         |  8      |  30     |  Y      |  0      |  30     |  
|  CXTRAP                |  Double      |  8      |  System.Double         |  8      |  31     |  Y      |  0      |  31     |  
|  C2                    |  Double      |  8      |  System.Double         |  8      |  32     |  Y      |  0      |  32     |  
|  EXTCRN                |  Double      |  8      |  System.Double         |  8      |  33     |  Y      |  0      |  33     |  
|  C3                    |  Double      |  8      |  System.Double         |  8      |  34     |  Y      |  0      |  34     |  
|  EXTCMBR               |  Double      |  8      |  System.Double         |  8      |  35     |  Y      |  0      |  35     |  
|  ROLL_ANG              |  Double      |  8      |  System.Double         |  8      |  36     |  Y      |  0      |  36     |  
|  I6                    |  Double      |  8      |  System.Double         |  8      |  37     |  Y      |  0      |  37     |  
|  O6                    |  Double      |  8      |  System.Double         |  8      |  38     |  Y      |  0      |  38     |  
|  EXTTWIST              |  Double      |  8      |  System.Double         |  8      |  39     |  Y      |  0      |  39     |  
|  CMINFH                |  Double      |  8      |  System.Double         |  8      |  40     |  Y      |  0      |  40     |  
|  EWR                   |  Char        |  12     |  System.String         |  12     |  41     |  Y      |  0      |  41     |  
|  RIMTEVC               |  Double      |  8      |  System.Double         |  8      |  42     |  Y      |  0      |  42     |  
|  RIM01VC               |  Double      |  8      |  System.Double         |  8      |  43     |  Y      |  0      |  43     |  
|  RIM02VC               |  Double      |  8      |  System.Double         |  8      |  44     |  Y      |  0      |  44     |  
|  RIM03VC               |  Double      |  8      |  System.Double         |  8      |  45     |  Y      |  0      |  45     |  
|  RIM04VC               |  Double      |  8      |  System.Double         |  8      |  46     |  Y      |  0      |  46     |  
|  RIM05VC               |  Double      |  8      |  System.Double         |  8      |  47     |  Y      |  0      |  47     |  
|  RIM06VC               |  Double      |  8      |  System.Double         |  8      |  48     |  Y      |  0      |  48     |  
|  RIM07VC               |  Double      |  8      |  System.Double         |  8      |  49     |  Y      |  0      |  49     |  
|  EXPMT                 |  Char        |  8      |  System.String         |  8      |  50     |  Y      |  0      |  50     |  
|  O1                    |  Double      |  8      |  System.Double         |  8      |  51     |  Y      |  0      |  51     |  
|  O5                    |  Double      |  8      |  System.Double         |  8      |  52     |  Y      |  0      |  52     |  
|  SPARE1                |  Char        |  20     |  System.String         |  20     |  53     |  Y      |  0      |  53     |  

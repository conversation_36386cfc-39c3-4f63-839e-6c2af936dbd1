package wdc.disk.ecs.apps.SFTPSend.util;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class CompareTwoFileSequence {
	
	public static boolean compareTwoFileIfSameLastModify(String fileName1, String fileName2) {
		int compareResult = compareFileNameDateBefore(fileName1, fileName2);
//		System.out.println("compareResult:" + (compareResult == 0));
		
		//file1 before file2
		if(compareResult < 0) {
//			System.out.println("<0");
			return true;
		//file1 after file2
		}else if(compareResult > 0){
//			System.out.println(">0");
			return false;
		//file1 equals to file2
		//Try to compare last index
		}else if(compareResult == 0) {
//			System.out.println("compareResult:" +  (compareResult == 0));
			boolean indexFirst = isIndexFirst(fileName1, fileName2);
//			System.out.println(indexFirst);
			return indexFirst;
		}
		
		System.out.println("Unexpected compareTwoFileIfSameLastModify result.");
		return false;
	}
	
	/**
	 * if file1 < file2 then return true;
	 * File name example:   NoProfile_c0726102639.76_line1
	 * @param file1
	 * @param file2
	 * @return
	 */
	public static boolean isIndexFirst(String file1, String file2) {
		String extractIndex = extractIndex(file1);
		String extractIndex2 = extractIndex(file2);
		
		int intValue1 = Integer.valueOf(extractIndex).intValue();
		int intValue2 = Integer.valueOf(extractIndex2).intValue();
		return (intValue1 < intValue2);
	}
	
	/**
	 *  In case there are some extra string after the index.
	 *  For example, NoProfile_c0726102639.76_line1
	 * @param str
	 * @return
	 */
	public static String extractNumberByRegex(String str) {
		Pattern pattern = Pattern.compile("(\\d+)(\\w*)");
		Matcher matcher = pattern.matcher(str);
		if(matcher.find()) {
			String digits = matcher.group(1);
			return digits;
		}
		return null;
	}

    /**
     * Compares two Dates for ordering.
     *
     * @param   anotherDate   the <code>Date</code> to be compared.
     * @return  the value <code>0</code> if the argument Date is equal to
     *          this Date; a value less than <code>0</code> if this Date
     *          is before the Date argument; and a value greater than
     *      <code>0</code> if this Date is after the Date argument
     **/
	public static int compareFileNameDateBefore(String file1, String file2) {
		Date extractDate1 = extractDate(file1);
		Date extraactDate2 = extractDate(file2);
		return extractDate1.compareTo(extraactDate2);
	}
	
	public static Date extractDate(String fileName) {
		int lastIndexOfDot = fileName.lastIndexOf(".");
//		System.out.println("last index of . : " + lastIndexOfDot); 
		int dateIndex = lastIndexOfDot - 10 ;
		String dateString = fileName.substring(dateIndex, lastIndexOfDot);
//		System.out.println("Date str: " + dateString);
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		int year = cal.get(Calendar.YEAR);
//		System.out.println("Year:" + year);
		Date parse = null;
		try {
			parse = df.parse(year + dateString + "000");
//			String strDateFormat = "yyyy-MM-dd HH:mm:ss";
//	        SimpleDateFormat sdf = new SimpleDateFormat(strDateFormat);
//	        String format = sdf.format(parse);
//	        System.out.println("result:" + format);			
		} catch (ParseException e) {
			e.printStackTrace();
		}

		return parse;
	}
	
	/**
	 * For example: 
	 * @param fileName
	 * @return
	 */
	public static String extractIndex(String fileName) {
		int lastIndexOfDot = fileName.lastIndexOf(".");
		String substring = fileName.substring(lastIndexOfDot + 1);
		String extractNumberStr = extractNumberByRegex(substring);
		
		return extractNumberStr;
	}
}

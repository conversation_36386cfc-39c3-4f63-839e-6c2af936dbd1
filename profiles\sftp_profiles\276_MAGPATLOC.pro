*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  UNITID                |  Char        |  12     |  System.String         |  12     |  1      |         |  0      |  1      |  
|  SIDE                  |  Char        |  1      |  System.String         |  1      |  2      |         |  0      |  2      |  
|  TESTER                |  Char        |  1      |  System.String         |  1      |  3      |         |  0      |  3      |  
|  CLUSTER_ID            |  SmallInt    |  2      |  System.Int16          |  2      |  4      |         |  0      |  4      |  
|  PATTERN               |  VarChar     |  50     |  System.String         |  50     |  5      |         |  0      |  5      |  
|  GEN_PATTERN           |  VarChar     |  50     |  System.String         |  50     |  6      |         |  0      |  6      |  
|  DEF_ID                |  Integer     |  4      |  System.Int32          |  4      |  7      |         |  0      |  7      |  
|  X                     |  Double      |  8      |  System.Double         |  8      |  8      |         |  0      |  8      |  
|  Y                     |  Double      |  8      |  System.Double         |  8      |  9      |         |  0      |  9      |  
|  RADIUS                |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  THETA                 |  Double      |  8      |  System.Double         |  8      |  11     |         |  0      |  11     |  
|  DTPATTERN             |  Timestamp   |  4      |  System.DateTime       |  4      |  12     |         |  0      |  12     |  

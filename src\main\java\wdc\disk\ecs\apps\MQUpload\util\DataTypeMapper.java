package wdc.disk.ecs.apps.MQUpload.util;

import java.util.HashMap;
import java.util.Map;
import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser.ColumnDefinition;


/**
 * Utility class for data type mappings
 * Centralizes type definitions used across the application
 */
public class DataTypeMapper {
    // Type mapping from DB2 to System types
    private static final Map<String, String> TYPE_MAPPING = new HashMap<>();
    static {
        TYPE_MAPPING.put("TIMESTAMP", "System.DateTime");
        TYPE_MAPPING.put("CHAR", "System.String");
        TYPE_MAPPING.put("VARCHAR", "System.String");
        TYPE_MAPPING.put("SMALLINT", "System.Int16");
        TYPE_MAPPING.put("INTEGER", "System.Int32");
        TYPE_MAPPING.put("DOUBLE", "System.Double");
        TYPE_MAPPING.put("REAL", "System.Double");
    }
    
    // Binary length mapping
    private static final Map<String, Integer> BINARY_LENGTH_MAPPING = new HashMap<>();
    static {
        BINARY_LENGTH_MAPPING.put("TIMESTAMP", 4);
        BINARY_LENGTH_MAPPING.put("SMALLINT", 2);
        BINARY_LENGTH_MAPPING.put("INTEGER", 4);
        BINARY_LENGTH_MAPPING.put("DOUBLE", 8);
        BINARY_LENGTH_MAPPING.put("REAL", 4);
        // String types will use their actual length
    }
    
    // Java type mapping from system types
    private static final Map<String, String> JAVA_TYPE_MAPPING = new HashMap<>();
    static {
        JAVA_TYPE_MAPPING.put("System.DateTime", "java.time.LocalDateTime");
        JAVA_TYPE_MAPPING.put("System.Double", "Double");
        JAVA_TYPE_MAPPING.put("System.String", "String");
        JAVA_TYPE_MAPPING.put("System.Int32", "Integer");
        JAVA_TYPE_MAPPING.put("System.Int16", "Short");
    }
    
    /**
     * Converts a string value to the appropriate type based on column definition
     * @param value String value to convert
     * @param column Column definition containing type information
     * @return Converted value of appropriate type
     */
    public static Object convertValue(String value, ColumnDefinition column) {
        if (value == null || value.trim().isEmpty() || "null".equalsIgnoreCase(value.trim())) {
            return null;
        }

        switch (column.getSystemType()) {
            case "System.DateTime":
                return DateTimeUtils.parseCustomFormat(value.trim());
            case "System.Double":
                if(value.trim().equalsIgnoreCase("null")) {
                    return 0.0;
                }else{
                    return Double.parseDouble(value.trim());
                }
            case "System.Int32":
                if(value.trim().equalsIgnoreCase("null")) {
                    return 0;
                }else{
                    return Integer.parseInt(value.trim());
                }
            case "System.Int16":
                if(value.trim().equalsIgnoreCase("null")) {
                    return 0;
                }else{
                    return Short.parseShort(value.trim());
                }
            case "System.String":
                int maxLength = column.getDbColumnLength();
                String trimmedValue = value.trim();
                if (trimmedValue.length() > maxLength) {
                    return trimmedValue.substring(0, maxLength);
                }
                return trimmedValue;
            default:
                return value.trim();
        }
    }
    
    /**
     * Maps system type to corresponding Java type
     * @param systemType System type name
     * @return Corresponding Java type
     */
    public static String mapToJavaType(String systemType) {
        if (systemType == null) return "String";
        return JAVA_TYPE_MAPPING.getOrDefault(systemType, "String");
    }
    
    /**
     * Get system type for a DB type
     * @param dbType Database type
     * @return Corresponding system type
     */
    public static String getSystemType(String dbType) {
        return TYPE_MAPPING.getOrDefault(dbType, "System.String");
    }
    
    /**
     * Get binary length for a DB type
     * @param dbType Database type
     * @param defaultLength Default length to use if no mapping exists
     * @return Binary length
     */
    public static int getBinaryLength(String dbType, int defaultLength) {
        return BINARY_LENGTH_MAPPING.getOrDefault(dbType, defaultLength);
    }
}


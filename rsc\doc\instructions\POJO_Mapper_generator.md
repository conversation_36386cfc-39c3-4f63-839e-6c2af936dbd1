# POJO and Mapper Generation Flow

## 1. Objective

The goal of this process is to automatically generate Plain Old Java Objects (POJOs) and corresponding Mapper files for database tables. These generated files are essential for data handling and mapping within the application.

## 2. Prerequisites

Before running this generator, ensure that you have completed the steps outlined in the `profile_convertion.md` document. This ensures that the necessary profile files are up-to-date.

## 3. Process Flow

The generation process will follow these steps:

### Step 1: Configure the Generator

1.  Open the `ProfileAndMapperGenerator.java` file located at `src/main/java/wdc/disk/ecs/apps/MQUpload/generator/ProfileAndMapperGenerator.java`.
2.  Locate the `main` method within this file.
3.  In the `main` method, you will find a list of tables to be processed. Comment out any existing `tables.add(...)` lines.
4.  Add a new entry for the table you wish to generate POJOs and Mappers for, based on the JSON string you provide. For a JSON string like `{"tabCode": "GLDRSLT", "mqMessageId": "2", "tableName": "GLDRSLT"}` you would add the following line:

    ```java
    // tables.add(new TableInfo(schema, "BANDRSLT", "BANDRSLT", "8", true));
    tables.add(new TableInfo(schema, "GLDRSLT", "GLDRSLT", "2", true));
    ```

    **Note:** The `true` parameter indicates that we are only generating the POJO and Mapper, assuming the profile file already exists.



### Step 3: Compile and Run the Generator

Once you have verified the configuration, I will execute the following commands to compile the project and run the generator.

1.  **Compile the project using Maven:** This ensures all dependencies are correctly handled.

    ```bash
    mvn clean install -DskipTests
    ```

2.  **Run the generator:** This command executes the `main` method of the `ProfileAndMapperGenerator` class with the correct classpath, including the necessary JARs from the `rsc/jar` directory.

    ```bash
    java -cp "target/classes;target/SFTPSend-1.0-SNAPSHOT.jar;rsc/jar/*" wdc.disk.ecs.apps.MQUpload.generator.ProfileAndMapperGenerator
    ```

### Step 4: Output

The generated POJO and Mapper files will be placed in the appropriate directories within the `src/main/java/wdc/disk/ecs/apps/MQUpload/` path. You can then review and use these files in the application.

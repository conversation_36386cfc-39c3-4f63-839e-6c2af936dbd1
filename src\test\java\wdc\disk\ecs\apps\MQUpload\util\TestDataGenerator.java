package wdc.disk.ecs.apps.MQUpload.util;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.producer.MQUpload;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

public class TestDataGenerator {
    private static final String PDS_DIR = "C:\\opt\\ecs\\storage\\mq\\pds\\";
    private static final String CONFIG_PROFILE = System.getProperty("user.dir") + 
            "\\src\\test\\resources\\MQ_Upload.ini";
    private static final String BLOCK_NAME = "MQUpload_test";
    private static final SimpleDateFormat DATE_FORMAT = 
        new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
    
    private final LogStream log;
    private final ScheduledExecutorService scheduler;
    
    public TestDataGenerator() {
        this.log = new LogStream("TestDataGenerator");
        this.scheduler = Executors.newScheduledThreadPool(1);
    }
    
    public void start() {
        // Ensure PDS directory exists
        new File(PDS_DIR).mkdirs();
        
        // Start MQUpload service
        MQUpload mqUpload = new MQUpload(CONFIG_PROFILE, log);
        try {
            mqUpload.initialize();
            mqUpload.start();
        } catch (Exception e) {
            log.writeExceptionStack(e);
            return;
        }
        
        // Schedule test data generation
        scheduler.scheduleAtFixedRate(() -> generateTestData(), 0, 5, TimeUnit.SECONDS);
    }
    
    private void generateTestData() {
        try {
            String timestamp = DATE_FORMAT.format(new Date());
            String fileName = "test_" + timestamp + ".txt";
            File file = new File(PDS_DIR, fileName);
            
            // Create test data in ASCII format that AsciiFileProcessor can parse
            // Format: timestamp LID PROD 9 * /test/path/data timestamp
            try (FileWriter writer = new FileWriter(file)) {
                writer.write(String.format("%s LID PROD 9 * /test/path/data %s\n", timestamp,timestamp));
                writer.write(String.format("%s POKE_MON_EOD PROD 9 * /test/path/data %s\n", timestamp,timestamp));
            }
            
            log.write(LogStream.INFORMATION_MESSAGE, "Generated test file: " + fileName);
            
        } catch (IOException e) {
            log.writeExceptionStack(e);
        }
    }
    
    public void stop() {
        scheduler.shutdown();
    }
    
    public static void main(String[] args) {
        TestDataGenerator generator = new TestDataGenerator();
        generator.start();
        
        // Run for 1 hour then stop
        try {
            Thread.sleep(TimeUnit.HOURS.toMillis(1));
        } catch (InterruptedException e) {
            // Ignore
        }
        generator.stop();
    }
}
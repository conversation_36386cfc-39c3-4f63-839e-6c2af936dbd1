package wdc.disk.ecs.apps.SFTPSend.common.pds.conversion;

import java.util.Vector;

import ibm.disk.cfgreader.ConfigReader;
import ibm.disk.cfgreader.ParameterRecord;
import ibm.disk.cfgreader.ReadEcsIniException;


public class PdsFileConvertManager {
	
	public enum FileType
	{
		Binary,ASCII,others
	}
	
	private static volatile PdsFileConvertManager pdsFileConvertManager;
	private String profileFile = null;
    private PdsFileConvertManager(String profile) throws ReadEcsIniException{ 
		try {
			ConfigReader cr = new ConfigReader(profile);
			cr.startGroup("PN_DERIVE_PARAMETERS"); //
			Vector ecsAttrib = null;
			boolean bPrefix = false;
			boolean bStartIdx = false;
			boolean bEndIdx = false;
			if (cr.hasMoreGroups()) {
				ecsAttrib = cr.nextGroup();
				for (int i = 0; i < ecsAttrib.size(); i++) {
					ParameterRecord rec = (ParameterRecord) ecsAttrib.get(i);
					if (rec.getKey().equals("PN_DERIVE_PREFIX")) {
						bPrefix = false; 
						//this.setPnDerivePrefix(rec.getValue().trim());
					}else if(rec.getKey().equals("PN_DERIVE_START_INDEX")){
						//this.setPnDeriveStartIdx(Integer.parseInt(rec.getValue().trim()));
					}else if(rec.getKey().equals("PN_DERIVE_END_INDEX")){
						//this.setPnDeriveEndIdx(Integer.parseInt(rec.getValue().trim()));
					}
				}
			}
		} catch (ReadEcsIniException e1) {
			throw e1;
		} catch (NullPointerException e2) {
			throw e2;
		}
		
    }
    
    public static PdsFileConvertManager getInstance(String profile) throws Exception {
        try {
            if (null == pdsFileConvertManager) {
                //Thread.sleep(1000);
                synchronized (PdsFileConvertManager.class) {
                    if(null == pdsFileConvertManager) {
                    	pdsFileConvertManager = new PdsFileConvertManager(profile);
                    }
                }
            }
        } catch (Exception e) {
        	throw e;
        }
        return pdsFileConvertManager;
    }
    
    public void GetMessageObj(String messageid, FileType fileType)
    {
    	
    }
}

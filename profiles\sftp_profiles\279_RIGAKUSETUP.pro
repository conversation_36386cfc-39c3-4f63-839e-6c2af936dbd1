*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |  Y      |  0      |  1      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  2      |  Y      |  0      |  2      |  
|  SETUP_TYPE            |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |  Y      |  0      |  4      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  5      |  Y      |  0      |  5      |  
|  RIGAKUNAME            |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  DT_MSRMT              |  Timestamp   |  4      |  System.DateTime       |  4      |  7      |  Y      |  0      |  7      |  
|  RECIPE                |  Char        |  15     |  System.String         |  15     |  8      |         |  0      |  8      |  
|  AMAGAVG               |  Double      |  8      |  System.Double         |  8      |  9      |         |  0      |  9      |  
|  ARUAVG                |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  ANIWAVG               |  Double      |  8      |  System.Double         |  8      |  11     |         |  0      |  11     |  
|  ASULAVG               |  Double      |  8      |  System.Double         |  8      |  12     |         |  0      |  12     |  
|  ATIAVG                |  Double      |  8      |  System.Double         |  8      |  13     |         |  0      |  13     |  
|  AMAGRANGE             |  Double      |  8      |  System.Double         |  8      |  14     |         |  0      |  14     |  
|  ARURANGE              |  Double      |  8      |  System.Double         |  8      |  15     |         |  0      |  15     |  
|  ANIWRANGE             |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  ASULRANGE             |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  ATIRANGE              |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  BMAGAVG               |  Double      |  8      |  System.Double         |  8      |  19     |         |  0      |  19     |  
|  BRUAVG                |  Double      |  8      |  System.Double         |  8      |  20     |         |  0      |  20     |  
|  BNIWAVG               |  Double      |  8      |  System.Double         |  8      |  21     |         |  0      |  21     |  
|  BSULAVG               |  Double      |  8      |  System.Double         |  8      |  22     |         |  0      |  22     |  
|  BTIAVG                |  Double      |  8      |  System.Double         |  8      |  23     |         |  0      |  23     |  
|  BMAGRANGE             |  Double      |  8      |  System.Double         |  8      |  24     |         |  0      |  24     |  
|  BRURANGE              |  Double      |  8      |  System.Double         |  8      |  25     |         |  0      |  25     |  
|  BNIWRANGE             |  Double      |  8      |  System.Double         |  8      |  26     |         |  0      |  26     |  
|  BSULRANGE             |  Double      |  8      |  System.Double         |  8      |  27     |         |  0      |  27     |  

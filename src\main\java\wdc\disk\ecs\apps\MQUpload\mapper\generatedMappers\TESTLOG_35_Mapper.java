package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TESTLOG_35;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TESTLOG_35
 */
public interface TESTLOG_35_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TESTLOG AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{testnum},",
        "    #{batchid},",
        "    #{productcode},",
        "    #{diskcode},",
        "    #{disksequence},",
        "    #{magrule},",
        "    #{testcode},",
        "    #{detqcode1},",
        "    #{detqcode2},",
        "    #{parqcode1},",
        "    #{parqcode2},",
        "    #{startmag},",
        "    #{stopmag},",
        "    #{dtinsert},",
        "    #{correlid},",
        "    #{lot},",
        "    #{unitid},",
        "    #{magrulever},",
        "    #{magchecksum},",
        "    #{expid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BATCHID,",
        "    PRODUCTCODE,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    MAGRULE,",
        "    TESTCODE,",
        "    DETQCODE1,",
        "    DETQCODE2,",
        "    PARQCODE1,",
        "    PARQCODE2,",
        "    STARTMAG,",
        "    STOPMAG,",
        "    DTINSERT,",
        "    CORRELID,",
        "    LOT,",
        "    UNITID,",
        "    MAGRULEVER,",
        "    MAGCHECKSUM,",
        "    EXPID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TESTNUM = SOURCE.TESTNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.BATCHID = SOURCE.BATCHID,",
        "    TARGET.PRODUCTCODE = SOURCE.PRODUCTCODE,",
        "    TARGET.DISKCODE = SOURCE.DISKCODE,",
        "    TARGET.DISKSEQUENCE = SOURCE.DISKSEQUENCE,",
        "    TARGET.MAGRULE = SOURCE.MAGRULE,",
        "    TARGET.TESTCODE = SOURCE.TESTCODE,",
        "    TARGET.DETQCODE1 = SOURCE.DETQCODE1,",
        "    TARGET.DETQCODE2 = SOURCE.DETQCODE2,",
        "    TARGET.PARQCODE1 = SOURCE.PARQCODE1,",
        "    TARGET.PARQCODE2 = SOURCE.PARQCODE2,",
        "    TARGET.STARTMAG = SOURCE.STARTMAG,",
        "    TARGET.STOPMAG = SOURCE.STOPMAG,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID,",
        "    TARGET.LOT = SOURCE.LOT,",
        "    TARGET.UNITID = SOURCE.UNITID,",
        "    TARGET.MAGRULEVER = SOURCE.MAGRULEVER,",
        "    TARGET.MAGCHECKSUM = SOURCE.MAGCHECKSUM,",
        "    TARGET.EXPID = SOURCE.EXPID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BATCHID,",
        "    PRODUCTCODE,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    MAGRULE,",
        "    TESTCODE,",
        "    DETQCODE1,",
        "    DETQCODE2,",
        "    PARQCODE1,",
        "    PARQCODE2,",
        "    STARTMAG,",
        "    STOPMAG,",
        "    DTINSERT,",
        "    CORRELID,",
        "    LOT,",
        "    UNITID,",
        "    MAGRULEVER,",
        "    MAGCHECKSUM,",
        "    EXPID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.BATCHID,",
        "    SOURCE.PRODUCTCODE,",
        "    SOURCE.DISKCODE,",
        "    SOURCE.DISKSEQUENCE,",
        "    SOURCE.MAGRULE,",
        "    SOURCE.TESTCODE,",
        "    SOURCE.DETQCODE1,",
        "    SOURCE.DETQCODE2,",
        "    SOURCE.PARQCODE1,",
        "    SOURCE.PARQCODE2,",
        "    SOURCE.STARTMAG,",
        "    SOURCE.STOPMAG,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.LOT,",
        "    SOURCE.UNITID,",
        "    SOURCE.MAGRULEVER,",
        "    SOURCE.MAGCHECKSUM,",
        "    SOURCE.EXPID",
        "  )"
    })
    void insertOrUpdate(TESTLOG_35 record);
}











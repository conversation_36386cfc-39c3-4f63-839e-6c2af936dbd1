<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="$namespace">
    <insert id="insertOrUpdateData">
        MERGE INTO DB2SYS.${tableName} AS TARGET
        USING (VALUES (
            <foreach collection="data" index="key" item="value" separator=",">
                #{value}
            </foreach>
        )) AS SOURCE (
            <foreach collection="data" index="key" item="value" separator=",">
                ${key}
            </foreach>
        )
        ON #foreach($key in $primaryKeys)TARGET.$key = SOURCE.$key#if($foreach.hasNext) AND #end#end

        WHEN MATCHED THEN
            UPDATE SET 
            <foreach collection="data" index="key" item="value" separator=",">
                <if test="key #foreach($key in $primaryKeys)!= '$key'#if($foreach.hasNext) and #end#end">
                    TARGET.${key} = SOURCE.${key}
                </if>
            </foreach>
            

        WHEN NOT MATCHED THEN
            INSERT (
                <foreach collection="data" index="key" item="value" separator=",">
                    ${key}
                </foreach>
            )
            VALUES (
                <foreach collection="data" index="key" item="value" separator=",">
                    SOURCE.${key}
                </foreach>
            )
    </insert>
</mapper>

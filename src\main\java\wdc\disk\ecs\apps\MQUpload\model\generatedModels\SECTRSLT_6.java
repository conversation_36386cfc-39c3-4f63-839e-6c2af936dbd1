package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for SECTRSLT_6
 */
public class SECTRSLT_6  {
    private Short spindle;
    private Integer testnum;
    private Short bandnum;
    private Double track;
    private Short rev;
    private Short sector;
    private Short reqopernum;
    private String side;
    private Double result;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public SECTRSLT_6() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Short getBandnum() {
        return bandnum;
    }

    public void setBandnum(Short bandnum) {
        this.bandnum = bandnum;
    }

    public Double getTrack() {
        return track;
    }

    public void setTrack(Double track) {
        this.track = track;
    }

    public Short getRev() {
        return rev;
    }

    public void setRev(Short rev) {
        this.rev = rev;
    }

    public Short getSector() {
        return sector;
    }

    public void setSector(Short sector) {
        this.sector = sector;
    }

    public Short getReqopernum() {
        return reqopernum;
    }

    public void setReqopernum(Short reqopernum) {
        this.reqopernum = reqopernum;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public Double getResult() {
        return result;
    }

    public void setResult(Double result) {
        this.result = result;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



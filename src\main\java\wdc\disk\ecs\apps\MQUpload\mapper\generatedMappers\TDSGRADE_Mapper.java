package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSGRADE;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSGRADE
 */
public interface TDSGRADE_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_GRADE AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{grade},",
        "    #{surface}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    GRADE,",
        "    SURFACE",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.GRADE = SOURCE.GRADE AND TARGET.SURFACE = SOURCE.SURFACE",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    GRADE,",
        "    SURFACE",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.GRADE,",
        "    SOURCE.SURFACE",
        "  )"
    })
    void insertOrUpdate(TDSGRADE record);
}











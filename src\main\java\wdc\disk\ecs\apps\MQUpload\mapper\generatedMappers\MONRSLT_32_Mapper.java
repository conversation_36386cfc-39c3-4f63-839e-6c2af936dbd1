package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.MONRSLT_32;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for MONRSLT_32
 */
public interface MONRSLT_32_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.MONRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{product},",
        "    #{diskcode},",
        "    #{disksequence},",
        "    #{side},",
        "    #{testnum},",
        "    #{magrule},",
        "    #{magheadid},",
        "    #{batchtime},",
        "    #{dataindex},",
        "    #{dtinsert},",
        "    #{correlid},",
        "    #{failcode}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    PRODUCT,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    SIDE,",
        "    TESTNUM,",
        "    MAGRULE,",
        "    MAGHEADID,",
        "    BATCHTIME,",
        "    DATAINDEX,",
        "    DTINSERT,",
        "    CORRELID,",
        "    FAILCODE",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.PRODUCT = SOURCE.PRODUCT AND TARGET.DISKCODE = SOURCE.DISKCODE AND TARGET.DISKSEQUENCE = SOURCE.DISKSEQUENCE AND TARGET.SIDE = SOURCE.SIDE AND TARGET.TESTNUM = SOURCE.TESTNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.MAGRULE = SOURCE.MAGRULE,",
        "    TARGET.MAGHEADID = SOURCE.MAGHEADID,",
        "    TARGET.BATCHTIME = SOURCE.BATCHTIME,",
        "    TARGET.DATAINDEX = SOURCE.DATAINDEX,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID,",
        "    TARGET.FAILCODE = SOURCE.FAILCODE",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    PRODUCT,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    SIDE,",
        "    TESTNUM,",
        "    MAGRULE,",
        "    MAGHEADID,",
        "    BATCHTIME,",
        "    DATAINDEX,",
        "    DTINSERT,",
        "    CORRELID,",
        "    FAILCODE",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.PRODUCT,",
        "    SOURCE.DISKCODE,",
        "    SOURCE.DISKSEQUENCE,",
        "    SOURCE.SIDE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.MAGRULE,",
        "    SOURCE.MAGHEADID,",
        "    SOURCE.BATCHTIME,",
        "    SOURCE.DATAINDEX,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.FAILCODE",
        "  )"
    })
    void insertOrUpdate(MONRSLT_32 record);
}











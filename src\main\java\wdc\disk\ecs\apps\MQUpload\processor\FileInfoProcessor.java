package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ibm.disk.extensions.StringMethods;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryMsgFieldLength;
import wdc.disk.ecs.apps.SFTPSend.binary.PackedMsgIndex;
import wdc.disk.ecs.apps.SFTPSend.binary.SingleMessage;
import wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility;

/**
 * This processor scans a directory for binary data files, identifies the message ID
 * contained within each file, and creates a map where each unique message ID is
 * associated with the first file found containing it. This is useful for quickly
 * gathering a representative sample file for each type of message.
 *
 * The `main` method allows this processor to be executed from the command line,
 * taking a directory path as an argument and printing the resulting map of
 * message IDs and their corresponding file paths to standard output.
 *
 * Usage (from command line):
 * java wdc.disk.ecs.apps.MQUpload.processor.FileInfoProcessor <path_to_directory> <destination_path>
 */
public class FileInfoProcessor {

    public FileInfo processFile(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            return readBinaryFileContent(fis, file);
        }
    }

    private FileInfo readBinaryFileContent(FileInputStream fis, File file) throws IOException {
        StringBuffer hexBuffer = new StringBuffer();
        int data;
        while ((data = fis.read()) != -1) {
            hexBuffer.append(StringMethods.pad(Integer.toHexString(data), 2, '0', true));
        }
        
        String rawHexData = hexBuffer.toString();
        if (rawHexData.length() < 16) {
            return new FileInfo(false, new ArrayList<>());
        }

        boolean isMulti = isMultiMessage(rawHexData);
        List<String> messageIds = new ArrayList<>();

        if (isMulti) {
            List<SingleMessage> singleMessages = splitPackedMsgToArrayList(rawHexData);
            for (SingleMessage singleMsg : singleMessages) {
                messageIds.add(singleMsg.getMsgId());
            }
        } else {
            messageIds.add(getMsgIdForIndividualMsg(rawHexData));
        }
        
        return new FileInfo(isMulti, messageIds);
    }

    private boolean isMultiMessage(String rawStr) {
        String rawMsg = rawStr.substring(0, 8);
        return "e6030000".equals(rawMsg);
    }

    private String getMsgIdForIndividualMsg(String rawStr) {
        String rawMsgId = rawStr.substring(0, 8);
        int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawMsgId, true);
        return Integer.valueOf(msgId).toString();
    }

    private List<SingleMessage> splitPackedMsgToArrayList(String rawData) {
        rawData = rawData.substring(BinaryMsgFieldLength.MSG_ID + BinaryMsgFieldLength.CORREL_ID);
        
        List<SingleMessage> packedMsgList = new ArrayList<>();
        boolean isInLoop = true;
        while (isInLoop) {
            SingleMessage singleMessage = new SingleMessage();
            int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.MSG_ID, PackedMsgIndex.MSG_ID + BinaryMsgFieldLength.MSG_ID), true);
            int correlid = ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.CORRELID, PackedMsgIndex.CORRELID + BinaryMsgFieldLength.CORREL_ID), true);
            int msgLength = 2 * ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.MSG_LEN, PackedMsgIndex.MSG_LEN + BinaryMsgFieldLength.MSG_LENGTH), true);
            String rawMsgContent = rawData.substring(PackedMsgIndex.MSG_CONTENT, PackedMsgIndex.MSG_CONTENT + msgLength);
            singleMessage.setMsgId(Integer.valueOf(msgId).toString());
            singleMessage.setCorrelid(Integer.valueOf(correlid).toString());
            singleMessage.setMsgLength(Integer.valueOf(msgLength).toString());
            singleMessage.setMsgContent(rawMsgContent);
            rawData = rawData.substring(PackedMsgIndex.MSG_CONTENT + msgLength);
            
            packedMsgList.add(singleMessage);
            
            if ("".equals(rawData.trim())) {
                isInLoop = false;
            }
        }
        
        return packedMsgList;
    }

    public static class FileInfo {
        private final boolean isMultiMessage;
        private final List<String> messageIds;

        public FileInfo(boolean isMultiMessage, List<String> messageIds) {
            this.isMultiMessage = isMultiMessage;
            this.messageIds = messageIds;
        }

        public boolean isMultiMessage() {
            return isMultiMessage;
        }

        public List<String> getMessageIds() {
            return messageIds;
        }

        @Override
        public String toString() {
            return "FileInfo{" +
                    "isMultiMessage=" + isMultiMessage +
                    ", messageIds=" + messageIds +
                    '}';
        }
    }

    public static void main(String[] args) {
        if (args.length < 2) {
            System.out.println("Usage: java wdc.disk.ecs.apps.MQUpload.processor.FileInfoProcessor <directory_path> <destination_path>");
            return;
        }

        File directory = new File(args[0]);
        if (!directory.exists() || !directory.isDirectory()) {
            System.out.println("Error: Provided path is not a valid directory: " + args[0]);
            return;
        }

        File destDir = new File(args[1]);
        if (!destDir.exists()) {
            destDir.mkdirs();
        }

        Map<String, String> messageIdToFileMap = new HashMap<>();
        FileInfoProcessor processor = new FileInfoProcessor();

        File[] files = directory.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isFile()) {
                    try {
                        FileInfo info = processor.processFile(file);
                        for (String msgId : info.getMessageIds()) {
                            if (!messageIdToFileMap.containsKey(msgId)) {
                                messageIdToFileMap.put(msgId, file.getAbsolutePath());
                            }
                        }
                    } catch (IOException e) {
                        System.err.println("Error processing file " + file.getName() + ": " + e.getMessage());
                    }
                }
            }
        }

        System.out.println("--- Message ID to File Path Map ---");
        for (Map.Entry<String, String> entry : messageIdToFileMap.entrySet()) {
            System.out.println("Message ID: " + entry.getKey() + " -> File: " + entry.getValue());
        }
        System.out.println("------------------------------------");

        copyFiles(messageIdToFileMap, args[1]);
    }

    private static void copyFiles(Map<String, String> messageIdToFileMap, String destPath) {
        for (Map.Entry<String, String> entry : messageIdToFileMap.entrySet()) {
            try {
                Files.copy(Paths.get(entry.getValue()), Paths.get(destPath, entry.getKey()), StandardCopyOption.REPLACE_EXISTING);
            } catch (IOException e) {
                System.err.println("Error copying file for message ID " + entry.getKey() + ": " + e.getMessage());
            }
        }
    }
}
package wdc.disk.ecs.apps.MQUpload.mapper;

import wdc.disk.ecs.apps.MQUpload.parser.ParseResult;
import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser.TableDefinition;
import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser.ColumnDefinition;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;
import wdc.disk.ecs.apps.MQUpload.util.DataTypeMapper;


import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import ibm.disk.utility.LogStream;

import java.beans.PropertyDescriptor;
import java.time.LocalDateTime;

/**
 * Maps ParseResult data to database columns based on table definitions
 */
public class MessageDataMapper {
    private final TableSchemaManager schemaManager;
    private final LogStream log;

    public MessageDataMapper(TableSchemaManager schemaManager, LogStream log) {
        this.schemaManager = schemaManager;
        this.log = log;
    }

    /**
     * Gets target table name from ParseResult using tabCode
     */
    public String getTableName(ParseResult parseResult) {
        String tabCode = parseResult.getTabCode();
        TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode(tabCode);
        return tableDef.getTableName();
    }

    /**
     * Maps parsed data to column values based on table definition
     * @param messageId Legacy message ID (not used for lookup)
     * @param parseResult Contains parsed fields and message type/tabCode
     * @return Map of column names to their values
     */
    public Map<String, Object> mapToColumns(ParseResult parseResult) {
        String tabCode = parseResult.getTabCode();
        TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode(tabCode);
        Map<String, Object> columnData = new HashMap<>();

        String[] fields = parseResult.getParsedFields();
        
        for (ColumnDefinition column : tableDef.getColumns()) {
            // Handle special timestamp columns
            if(column.getColumnName().equals("DT_ENTRY")) {
                columnData.put("DT_ENTRY", parseResult.getDateTime() != null ? 
                    parseResult.getDateTime() : null);
                continue;
            }
            if(column.getColumnName().equals("DTINSERT")) {
                columnData.put("DTINSERT", LocalDateTime.now());
                continue;
            }

            // Use valueIndex to determine which field to map
            int valueIndex = column.getValueIndex();
            if (valueIndex >= 0 && valueIndex < fields.length) {
                Object value = DataTypeMapper.convertValue(fields[valueIndex], column);
                columnData.put(column.getColumnName(), value);
                
                // Log mapping for debugging
                log.write(LogStream.DEBUG_MESSAGE, 
                    String.format("Mapped column %s[index=%d]: original='%s', converted='%s'", 
                        column.getColumnName(), 
                        valueIndex,
                        fields[valueIndex], 
                        value));
            }
        }

        return columnData;
    }

    /**
     * Maps parsed data to POJO based on table definition
     * @param parseResult Contains parsed fields and message type/tabCode
     * @param pojoClass The target POJO class
     * @return Instance of POJO with mapped values
     */
    public <T> T mapToPojo(ParseResult parseResult, Class<T> pojoClass) {
        try {
            if(parseResult == null || parseResult.getParsedFields() == null) {
                throw new IllegalArgumentException("Parsed fields cannot be null");
            }
            if(pojoClass == null) {
                throw new IllegalArgumentException("POJO class cannot be null");
            }

            T pojo = pojoClass.getDeclaredConstructor().newInstance();
            String tabCode = parseResult.getTabCode();
            TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode(tabCode);
            String[] fields = parseResult.getParsedFields();

            // Map fields based on column definitions
            for (ColumnDefinition column : tableDef.getColumns()) {
                String propertyName = toCamelCase(column.getColumnName());
                
                // Handle special timestamp columns
                if(column.getColumnName().equals("DT_ENTRY")) {
                    setProperty(pojo, "dtEntry", parseResult.getDateTime());
                    continue;
                }
                if(column.getColumnName().equals("DTINSERT")) {
                    TimeUnit.MICROSECONDS.sleep(1);
                    setProperty(pojo, "dtinsert", LocalDateTime.now());
                    continue;
                }

                // Use valueIndex to determine which field to map
                int valueIndex = column.getValueIndex();
                if (valueIndex >= 0 && valueIndex < fields.length) {
                    log.write(LogStream.DEBUG_MESSAGE, 
                        String.format("Mapping property %s[index=%d]: original='%s', type='%s'", 
                            propertyName, 
                            valueIndex,
                            fields[valueIndex], 
                            column.getJavaType()));
                    Object value = DataTypeMapper.convertValue(fields[valueIndex], column);
                    try {
                        setProperty(pojo, propertyName, value);
                    } catch (Exception e) {
                        log.write(LogStream.ERROR_MESSAGE, 
                            String.format("Failed to map property %s: value='%s', type='%s', column='%s', index=%d", 
                                propertyName, value, 
                                (value != null ? value.getClass().getName() : "null"), 
                                column.getColumnName(),
                                valueIndex));
                        throw e;
                    }
                    
                }
            }

            return pojo;
        } catch (Exception e) {
        	log.write(LogStream.ERROR_MESSAGE,"Failed to map data to POJO");
            log.write(LogStream.ERROR_MESSAGE,
                String.format("ParseResult details - TabCode: %s, Fields count: %d",
                    parseResult.getTabCode(),
                    parseResult.getParsedFields().length));
            log.write(LogStream.ERROR_MESSAGE, "Parse result: " + parseResult);        
            log.writeExceptionStack(e);
            throw new RuntimeException("Failed to map data to POJO", e);
        }
    }


    private void setProperty(Object obj, String propertyName, Object value) {
        try {
            PropertyDescriptor pd = new PropertyDescriptor(propertyName, obj.getClass());
            pd.getWriteMethod().invoke(obj, value);
        } catch (Exception e) {
            throw new RuntimeException("Failed to set property: " + propertyName, e);
        }
    }

    private String toCamelCase(String columnName) {
        String[] parts = columnName.toLowerCase().split("_");
        StringBuilder camelCase = new StringBuilder(parts[0]);
        for (int i = 1; i < parts.length; i++) {
            camelCase.append(Character.toUpperCase(parts[i].charAt(0)))
                    .append(parts[i].substring(1));
        }
        return camelCase.toString();
    }
}
















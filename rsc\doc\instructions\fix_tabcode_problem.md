# TabCode Mapping Problem Analysis and Solution (Final)

## Problem Analysis

1.  **Unique Identifier**: The application uses a composite `tabCode` in `message_mappings.json` to uniquely identify a message-to-table mapping. This is especially critical for binary types where one table might be associated with multiple `mqMessageId`s (e.g., the `GLDRSLT` table with `mqMessageId: "2"` has a unique `tabCode: "GLDRSLT_2"`). This composite `tabCode` is the true, unique key required for the mapping to function correctly at runtime.

2.  **Identifier Disconnect in Generation**: The code generation process was not consistently using this unique `tabCode`. This created a disconnect between the generated mapper's ID and the runtime lookup ID, causing a failure. The `RabbitMQConsumer` would correctly identify an incoming message with its unique `tabCode` (e.g., `GLDRSLT_2`) but could not find a corresponding MyBatis mapper.

3.  **Code Quality**: A direct side effect of the previous approach was the generation of Java classes with unconventional names (e.g., `GLDRSLT_2.java`). Java class names should follow the UpperCamelCase convention for readability and maintainability.

## Final Solution

The solution is to ensure the unique composite `tabCode` is used for lookups while generating code that adheres to Java's standard naming conventions.

1.  **Standardize on a Unique Profile Filename**: The profile filename is the vehicle for passing the unique identifier to the `PojoAndMapperGenerator`. We will use the following unambiguous pattern:
    *   **Pattern**: `{messageId}_{tabCode}.pro`
    *   **Example**: For `tableName: "TRCKRSLT"`, `mqMessageId: "7"`, and `tabCode: "TRCKRSLT_7"`, the generated filename will be `7_TRCKRSLT_7.pro`.

2.  **Update `ProfileAndMapperGenerator.java`**:
    *   **In the `main` method**: The `TableInfo` instantiations for all binary types **must** be updated to use the correct composite `tabCode` from `message_mappings.json`.
        ```java
        // In the main method's List<TableInfo> tables...
        // Change this:
        // tables.add(new TableInfo(schema, "TRCKRSLT", "TRCKRSLT", "7", true));

        // To this:
        tables.add(new TableInfo(schema, "TRCKRSLT", "TRCKRSLT_7", "7", true));
        ```
    *   The `generateAll` method will then automatically produce the correct, unique filename (e.g., `7_TRCKRSLT_7.pro`). No code change is needed in this method.

3.  **Refine `PojoAndMapperGenerator.java`**: This class will be updated to distinguish between the lookup key (`tabCode`) and the generated `className`.
    *   **Extract `tabCode`**: A method (`extractTabCodeFromProfile`) will parse the profile filename (e.g., `7_TRCKRSLT_7.pro`) to extract the unique `tabCode` (e.g., `TRCKRSLT_7`).
    *   **Normalize to `className`**: A new method (`normalizeTabCodeToClassName`) will convert the extracted `tabCode` into a conventional Java class name.
        *   `TRCKRSLT_7` becomes `Trckrslt7`
        *   `GLDRSLT_2` becomes `Gldrslt2`
    *   **Update Generator Methods**:
        *   `generateFromProfile` will orchestrate the extraction and normalization.
        *   `generatePojo` will use the normalized `className` to create the POJO file (e.g., `Trckrslt7.java`).
        *   `generateMapperInterface` will use the normalized `className` for the generated mapper's name (`Trckrslt7_Mapper.java`) but will use the original, unique `tabCode` (`TRCKRSLT_7`) when calling `schemaManager.getTableDefinitionFromTabCode()` to ensure the correct table metadata is retrieved.

This final solution ensures that the generated code is both functionally correct—by preserving the unique key for lookups—and high-quality, by adhering to standard Java naming conventions.

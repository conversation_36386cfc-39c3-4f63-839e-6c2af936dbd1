package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Properties;

/**
 * This util class is used to create a property file to record last time the folder was created.
 * The property file store the time which type is long.
 *
 * <AUTHOR>
 *
 */
public class FolderLastCreatedUtil {
	private Properties prop;
	private String propPath;
	private String propFiledName = "LastDirCreatedTimeMillis";
	private File propFile;
	
	
	public FolderLastCreatedUtil(String propPath) {
		super();
		this.prop = new Properties();
		this.propPath = propPath;
		this.propFile = new File(this.propPath);
	}

	public long getLatestFolderTimeFromPorp() throws FileNotFoundException, IOException {
		FileInputStream fileInputStream = new FileInputStream(propFile);
		this.prop.load(fileInputStream);
		String lastTime = this.prop.getProperty(propFiledName);
		fileInputStream.close();
		return Long.parseLong(lastTime);
	}

	public void writeCurrentTimeToPropertieyFile()
			throws FileNotFoundException, IOException {
		long current =  System.currentTimeMillis();
		this.prop.setProperty(propFiledName, Long.toString(current));
		writeToProp(this.prop, this.propFile);
	}
	
	private void writeToProp(Properties prop, File propFile) throws FileNotFoundException, IOException {
		FileOutputStream fileOutputStream = new FileOutputStream(propFile);
		prop.store(fileOutputStream, "Store file folder latest create time");
		fileOutputStream.close();
	}
	
	public boolean compare(int hour) throws FileNotFoundException, IOException {
		long currentTimeMillis = System.currentTimeMillis();
		long gap = ( hour * 60 * 60 * 1000);
		long fromPorp = getLatestFolderTimeFromPorp();
		if(currentTimeMillis - gap > fromPorp) {
//			System.out.println("currentTimeMillis - gap > fromPorp" + " : true");
			return true;
		}else {
//			System.out.println("currentTimeMillis - gap > fromPorp" + " : false");
			return false;
		}
	}
	
	public boolean compareByMillis(int millis) throws FileNotFoundException, IOException {
		long currentTimeMillis = System.currentTimeMillis();
		long gap = millis;
		long fromPorp = getLatestFolderTimeFromPorp();
		if(currentTimeMillis - gap > fromPorp) {
//			System.out.println("currentTimeMillis - gap > fromPorp" + " : true");
			return true;
		}else {
//			System.out.println("currentTimeMillis - gap > fromPorp" + " : false");
			return false;
		}
	}
	
	public String createFolderName() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
		Date date = new Date();
		String dateStr = sdf.format(date);
		return dateStr;
	}
	
//	public String getFolderNameFromProp() throws IOException {
//		FileInputStream fileInputStream = new FileInputStream(propFile);
//		prop.load(fileInputStream);
//		String folderName = prop.getProperty(propFiledName);
//		fileInputStream.close();
//		return folderName;
//	}
//
//	public void setFolderName(String folderName) throws IOException {
//		prop.setProperty("FolderName", folderName);
//		writeToProp(prop, propFile);
//	}
	
	public String getFolderNameFromTime(long millis) {
		Date date = new Date(millis);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHH");
		String dateStr = sdf.format(date);
		return dateStr;
	}

	public File getPropFile() {
		return propFile;
	}

	public void setPropFile(File propFile) {
		this.propFile = propFile;
	}
	
	
}

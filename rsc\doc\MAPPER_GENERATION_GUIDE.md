# Mapper Generation Guide

This guide explains how the MyBatis Mapper generation works in the MQUpload application.

## 1. Overview

The Mapper generation system automatically creates MyBatis mapper interfaces and POJO models from profile definitions. It uses Velocity templates to generate Java code that handles DB2 MERGE operations. The system supports both complete generation (profile + POJO + mapper) and POJO/mapper-only generation from existing profiles.

## 2. Required Files

### 2.1 Message Mappings
Location: `src/test/resources/mapper/message_mappings.json`
```json
{
  "messageProfiles": [
    {"tabCode": "LID", "mqMessageId": "309", "msgType": "ASCII", "tableName": "LID"},
    {"tabCode": "FTPRAW", "mqMessageId": "222", "msgType": "ASCII", "tableName": "FTP_RAW"},
    {"tabCode": "GLDRSLT", "mqMessageId": "2", "msgType": "Binary", "tableName": "GLDRSLT"}
  ]
}
```

### 2.2 Profile Files
Location: `profiles/generatedProfiles/<mqMessageId>_<tabCode>.pro`
```
*|ColumnName|DBType|IsUnique|Length|SystemType|
*|DT_ENTRY|TIMESTAMP|Y|...|System.DateTime|
*|LOT|VARCHAR|Y|...|System.String|
*|PRODUCT|VARCHAR|N|...|System.String|
```

### 2.3 Velocity Templates
- POJO Template: `src/main/resources/templates/Pojo.vm`
- Mapper Template: `src/main/resources/templates/Mapper.vm`

## 3. Generation Process
open PojoAndMapperGenerator.java and run PojoAndMapperGenerator:main 
```mermaid
graph TD
    A[Add TableInfo to List] -->|onlyPOJOAndMapper = false| B[ProfileAndMapperGenerator]
    A[Add TableInfo to List] -->|onlyPOJOAndMapper = true | D[PojoAndMapperGenerator]

    B --> C[Generate Profile File]
    B --> E[Generate POJO]
    B --> F[Generate Mapper]

    D --> G[Generate POJO]
    D --> H[Generate Mapper]



    
```

## 4. Generated Files Structure

```
src/main/java/wdc/disk/ecs/apps/MQUpload/
├── mapper/
│   └── generatedMappers/
│       ├── FTPRAW_Mapper.java
│       ├── LID_Mapper.java
│       └── WASHCANDELA_Mapper.java
└── model/
    └── generatedModels/
        ├── FTPRAW.java
        ├── LID.java
        └── WASHCANDELA.java

profiles/generatedProfiles/
├── 222_FTPRAW.pro
├── 309_LID.pro
└── 309_WASHCANDELA.pro
```




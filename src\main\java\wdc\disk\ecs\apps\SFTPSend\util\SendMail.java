package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Vector;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpException;

import ibm.disk.cfgreader.ConfigReader;
import ibm.disk.cfgreader.ParameterRecord;
import ibm.disk.extensions.StringMethods;
import ibm.disk.utility.LogStream;


public class SendMail {
	private boolean isDebug;
	private int loglevel = LogStream.INFORMATION_MESSAGE;
	private ibm.disk.utility.LogStream log;	
	private String ServerAddress = null;
	private String remoteEmailPath = null;
	private String UserID = null;
	private String PassWord = null;
	
	private int IntervalTime = 0;
	private String MonitorDirectory = null;
	private String toWhomProfile = null;
	private String localEmailPath = null;

	private boolean fileUpdate = false;
	private long fileModifiedTime;
	private String msg = null;
	private String toWho = null;
	private String hostName = null;
	private HashMap<String,String> InitialValueMap = new java.util.HashMap<String,String>();
	private String subject;
	private String blockName;

	public SendMail(){
		super();	
	}
	
	/*
	 * Will read profile first, 
	 * The default block is block will be 'sendMail',then will read configuration in 'sendMail' 
	 * If you need to use other blocks, choose another constructor
	 */
	public SendMail(String profile){
		super();	
		String blockName = "SendMail";
		readProfile(profile, blockName);
		
		setHostFromSystem(this.hostName);
		this.blockName = blockName;
		this.toWho = toWho();
	}

	public SendMail(String profile,String blockName){
		super();	
		readProfile(profile, blockName);
		setHostFromSystem(this.hostName);
		this.toWho = toWho();
		this.blockName = blockName;
	}
	

	public void writeEmailMsgToFile(File emailFile, String toWho, String subject,String body) throws IOException {
//		String str = "From:"+ " Monitor "
//				+ "\nSent: "+StringMethods.getCurrentTimeStamp() +"\n"
//				+ toWho +"\n"
//				+ "Subject: " + subject + "\n"+"\n"
//				+ body + "\n";
		
		
		FileWriter fWriter = new FileWriter(emailFile);
		BufferedWriter bWriter = new BufferedWriter(fWriter);
		bWriter.append("From:"+ " Monitor ");
		bWriter.append("\nSent: ");
		bWriter.append(StringMethods.getCurrentTimeStamp());
		bWriter.append("\n");
		bWriter.append(toWho);
		bWriter.append("\n");
		bWriter.append("Subject: ");
		bWriter.append(subject);
		bWriter.append("\n");
		bWriter.append("Host name :" + getHostName() + " \n");
		bWriter.append("Mail time:" + (new Date().toString()) + " \n");
		bWriter.append(body);
		bWriter.append("\n");
		
		bWriter.flush();
		
		String result = bWriter.toString();
		bWriter.close();
		showAtTerminalAndLog("EmailFile:" + emailFile.getAbsolutePath());
		showAtTerminalAndLog(result);
	}
	
		
	public void sendWarningMail(String body) {
		showAtTerminalAndLog("Trying to Write to Mail file");
		File emailFile = null;
		String fileName = this.hostName + "_" + this.blockName + ".mail";
		try {
			String emailPathDir = getLocalEmailPath();
			File emailDir = new File(emailPathDir);
			if(!emailDir.exists()) {
				emailDir.mkdir();
			}
			
			emailFile = new File(emailDir, fileName);
			writeEmailMsgToFile(emailFile, toWho, subject, body);
			showAtTerminalAndLog("Completed write to file.");
		} catch (IOException e) {
			e.printStackTrace();
			log.writeExceptionStack(e);
		}

		if(!isDebug) {
			showAtTerminalAndLog("Trying to sent to server.");
			try {
				sendFileToMailServer(emailFile);				
			} catch (Exception e) {
				e.printStackTrace();
				log.writeExceptionStack(e);
			}
			showAtTerminalAndLog("Completed sent to server.");
		}
	}	
	
	public void sendFileToMailServer(File file) throws JSchException, SftpException {
		JSch jsch = new JSch();
		Session session = null;
			session = jsch.getSession(getUserID(), getServerAddress(), 22);
			session.setConfig("StrictHostKeyChecking", "no");
			session.setPassword(getPassWord());
			session.connect();

			Channel channel = session.openChannel("sftp");
			channel.connect();
			ChannelSftp sftpChannel = (ChannelSftp) channel;
			if(file.exists()) {
				String src = file.getAbsolutePath();
				String dest = getEMAIL_PATH();
				sftpChannel.put(src,dest);
				showAtTerminalAndLog("Mail is sent successfully.");
				
//				if(file.delete()) {
//					log.write("Warning email in the local host has been deleted.");					
//				}
			}else {
				showAtTerminalAndLog("File not exist." + file.getAbsolutePath());
			}


			sftpChannel.exit();
			session.disconnect();
	}


	public boolean readProfile(String profile, String blockname) {
		ConfigReader cr = null;
		try {
			cr = new ConfigReader(profile);
		} catch (Exception e) {
			System.out.println("Read config failed.");
			return false;
		}
		
		// Get the general configurations for SFTPSend
		cr.startGroup(blockname);
		Vector mqAttrib = null;
		if (cr.hasMoreGroups()) {
			mqAttrib = cr.nextGroup();
			for (int i = 0; i < mqAttrib.size();i++) {
				ParameterRecord rec = (ParameterRecord)mqAttrib.get(i);
				if (rec.getKey().equals ("LOG_LEVEL")) {
					try {
						loglevel = Integer.parseInt(rec.getValue());
					} catch (Exception e) {}   // defaults to log level information
				}else if (rec.getKey().equals ("LOG_FILE")) {
					String logFile = rec.getValue();
					if (logFile != null) {
						log = new LogStream(logFile);
					}
				}else if (rec.getKey().equals ("SERVER_ADDRESS")) {
					String serverAddr = rec.getValue().trim();
					if(serverAddr != null) {
						setServerAddress(serverAddr);
						System.out.println("ServerAddr set to :" + getServerAddress());
					}
				}else if (rec.getKey().equals ("USER_ID")) {
					String userId = rec.getValue().trim();
					if(userId != null) {
						setUserID(userId);
						System.out.println("UserId set to "+ getUserID());
					}
				}else if (rec.getKey().equals ("PASSWORD")) {
					String pass = rec.getValue().trim();
					if(pass != null) {
//						String parsed = PasswordEncoding.en_decode(pass);
						setPassWord(pass);
						System.out.println("Pass set to "+ getPassWord());
					}
				}else if (rec.getKey().equals ("MONITOR_DIRECTORY")) {
					String monitoryDirString = rec.getValue().trim();
					if(monitoryDirString!= null) {
						setMonitorDirectory(monitoryDirString);
						System.out.println("MonitorDirectory set to "+ getMonitorDirectory());
					}
				}else if (rec.getKey().equals ("TO_WHOM_PROFILE")) {
					String emailProfile = rec.getValue().trim();
					if(emailProfile!= null) {
						setToWhomProfile(emailProfile);
						System.out.println("emailProfile set to "+ getToWhomProfile());
					}
				}else if (rec.getKey().equals ("REMOTE_EMAIL_PATH")) {
					String emailpath = rec.getValue().trim();
					if(emailpath != null) {
						setEMAIL_PATH(emailpath);
						System.out.println("emailPath set to "+ getEMAIL_PATH());
					}
				}else if (rec.getKey().equals ("LOCAL_HOST")) {
					String localHost = rec.getValue().trim();
					if(localHost != null) {
						setLocalHost(localHost);
						System.out.println("localHost set to "+ getLocalHost());
					}
					
				}else if (rec.getKey().equals ("LOCAL_EMAIL_PATH")) {
					String emailpath = rec.getValue().trim();
					if(emailpath != null) {
						setLocalEmailPath(emailpath);
						System.out.println("LOCAL_EMAIL_PATH set to "+ getLocalEmailPath());
					}
				}else if (rec.getKey().equals ("IS_DEBUG")) {
					String val = rec.getValue();
					if(val != null && !"".equals(val.trim())) {
						this.isDebug = StringMethods.getBoolean(val);
					}
				}else if(rec.getKey().equals("SUBJECT")) {
					String val = rec.getValue();
					if(val != null && !"".equals(val.trim())) {
						this.subject = val.trim();
					}else {
						this.subject = "No subject";
					}
				}
			}
		}
		
		return(true);
	}
	
	public String toWho() {
		String strLine = null;
	   	if (getToWhomProfile() == null) {
    		String info = "Warning: EMAIL_PROFILE key not found in tool block for " + String.valueOf(InitialValueMap.get("Resource")) + ". Messages will not be emailed";
    		System.out.println(info);	
    	}
    	
    	if (getToWhomProfile() != null) {
    		try {
    			File file = new File(getToWhomProfile());
    			FileReader frIni = new FileReader(file);
    			BufferedReader brIni = new BufferedReader(frIni);
    			String line = null;
    			while ((line = brIni.readLine())!=null){
    				if (!((line.startsWith("//"))|| (line.startsWith("*")) || (line.startsWith("\\")))){
    					strLine = line;
    				}	
    			}   			
    			brIni.close();
    		}catch (FileNotFoundException efn){
			      efn.printStackTrace();
			      System.out.println("Error" + efn.getMessage());
			}catch (IOException eio){
				  eio.printStackTrace();
				  System.out.println("Error" + eio.getMessage());
			}
    	}
    	return strLine;				
	}
	
	

	public String getServerAddress() {
		return ServerAddress;
	}

	public void setServerAddress(String serverAddress) {
		ServerAddress = serverAddress;
	}

	public String getEMAIL_PATH() {
		return remoteEmailPath;
	}

	public void setEMAIL_PATH(String eMAIL_PATH) {
		remoteEmailPath = eMAIL_PATH;
	}

	public String getUserID() {
		return UserID;
	}

	public void setUserID(String userID) {
		UserID = userID;
	}

	public String getPassWord() {
		return PassWord;
	}

	public void setPassWord(String passWord) {
		PassWord = passWord;
	}

	public int getIntervalTime() {
		return IntervalTime;
	}

	public void setIntervalTime(String intervalTime) {
		IntervalTime = Integer.valueOf(intervalTime);
	}

	public String getMonitorDirectory() {
		return MonitorDirectory;
	}

	public void setMonitorDirectory(String monitorDirectory) {
		MonitorDirectory = monitorDirectory;
	}

	public boolean isFileUpdate() {
		return fileUpdate;
	}

	public void setFileUpdate(boolean fileUpdate) {
		this.fileUpdate = fileUpdate;
	}

	public long getFileModifiedTime() {
		return fileModifiedTime;
	}

	public void setFileModifiedTime(long fileModifiedTime) {
		this.fileModifiedTime = fileModifiedTime;
	}

	public String getMsg() {
		return msg;
	}

	public void setMsg(String msg) {
		this.msg = msg;
	}


	public HashMap<String,String> getInitialValueMap() {
		return InitialValueMap;
	}

	public void setInitialValueMap(HashMap<String,String> initialValueMap) {
		InitialValueMap = initialValueMap;
	}

	public String getToWhomProfile() {
		return toWhomProfile;
	}

	public void setToWhomProfile(String toWhomProfile) {
		this.toWhomProfile = toWhomProfile;
	}

	public int getLoglevel() {
		return loglevel;
	}

	public void setLoglevel(int loglevel) {
		this.loglevel = loglevel;
	}

	public ibm.disk.utility.LogStream getLog() {
		return log;
	}

	public void setLog(ibm.disk.utility.LogStream log) {
		this.log = log;
	}

	public void setIntervalTime(int intervalTime) {
		IntervalTime = intervalTime;
	}

	public String getToWho() {
		return toWho;
	}

	public void setToWho(String toWho) {
		this.toWho = toWho;
	}

	public String getLocalHost() {
		return hostName;
	}

	public void setLocalHost(String localHost) {
		this.hostName = localHost;
	}

	public String getLocalEmailPath() {
		return localEmailPath;
	}

	public void setLocalEmailPath(String localEmailPath) {
		this.localEmailPath = localEmailPath;
	}
	
	public void setHostFromSystem(String localHost) {
		if(localHost == null || "".equals(localHost)) {
			String[] hostname;
			try {
				hostname = java.net.InetAddress.getLocalHost().getHostName().toUpperCase().split("\\.");
				setLocalHost(hostname[0]);
				System.out.println("LocalHost set to auto fetch: "+ getLocalHost());
			} catch (java.net.UnknownHostException e) {
				e.printStackTrace();
			}
		}
	}

	
	
	public boolean isDebug() {
		return isDebug;
	}

	public void setDebug(boolean isDebug) {
		this.isDebug = isDebug;
	}

	public String getRemoteEmailPath() {
		return remoteEmailPath;
	}

	public void setRemoteEmailPath(String remoteEmailPath) {
		this.remoteEmailPath = remoteEmailPath;
	}

	public String getHostName() {
		return hostName;
	}

	public void setHostName(String hostName) {
		this.hostName = hostName;
	}

	public String getSubject() {
		return subject;
	}

	public void setSubject(String subject) {
		this.subject = subject;
	}

	public String getBlockName() {
		return blockName;
	}

	public void setBlockName(String blockName) {
		this.blockName = blockName;
	}

	public void showAtTerminalAndLog(String msg) {
		System.out.println(msg);
		log.write(LogStream.INFORMATION_MESSAGE, msg);
	}
}

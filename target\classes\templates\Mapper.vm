package ${package};

import ${modelPackage}.${className};
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for ${className}
 */
public interface ${className}_Mapper {
    
    @Insert({
    #if(!$uniqueColumns.isEmpty())
        "MERGE INTO DB2SYS.${tableName} AS TARGET",
        "USING (VALUES (",
        #foreach($col in $allColumns)
            #if($foreach.hasNext)
        "    #{${col.camelCaseName}},",
            #else
        "    #{${col.camelCaseName}}",
            #end
        #end
        ")) AS SOURCE (",
        #foreach($col in $allColumns)
            #if($foreach.hasNext)
        "    ${col.columnName},",
            #else
        "    ${col.columnName}",
            #end
        #end
        ")",
        "ON #foreach($col in $uniqueColumns)TARGET.${col.columnName} = SOURCE.${col.columnName}#if($foreach.hasNext) AND #end#end",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        #foreach($col in $updateColumns)
            #if($foreach.hasNext)
        "    TARGET.${col.columnName} = SOURCE.${col.columnName},",
            #else
        "    TARGET.${col.columnName} = SOURCE.${col.columnName}",
            #end
        #end
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        #foreach($col in $allColumns)
            #if($foreach.hasNext)
        "    ${col.columnName},",
            #else
        "    ${col.columnName}",
            #end
        #end
        "  )",
        "  VALUES (",
        #foreach($col in $allColumns)
            #if($foreach.hasNext)
        "    SOURCE.${col.columnName},",
            #else
        "    SOURCE.${col.columnName}",
            #end
        #end
        "  )"
    #else
        "INSERT INTO DB2SYS.${tableName} (",
        #foreach($col in $allColumns)
            #if($foreach.hasNext)
        "    ${col.columnName},",
            #else
        "    ${col.columnName}",
            #end
        #end
        ")",
        "VALUES (",
        #foreach($col in $allColumns)
            #if($foreach.hasNext)
        "    #{${col.camelCaseName}},",
            #else
        "    #{${col.camelCaseName}}",
            #end
        #end
        ")"
    #end
    })
    void insertOrUpdate(${className} record);
}











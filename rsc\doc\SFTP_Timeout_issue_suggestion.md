## SFTP Timeout Issue: Analysis and Suggestion

### 1. Root Cause Analysis

The root cause of the SFTP timeout issue lies in the logic of the `MonitorSftpSendRunnable.java` class, specifically in the interplay between the `isUnprocessPdsQtyExceeded` and `needToCreateNewThread` methods.

The `needToCreateNewThread` method determines whether to restart the SFTP worker thread. Its logic is as follows:

```java
public boolean needToCreateNewThread(boolean isTimeout, boolean isUnprocessPdsQtyExceeded, boolean isSendingTimeout) {
    // ...
	return (isTimeout && isUnprocessPdsQtyExceeded) || isSendingTimeout; 
}
```

The application log `needToCreateNewThread:false` indicates that this condition is not being met, even when a timeout has occurred (`isTimeout:true`). This implies that `isUnprocessPdsQtyExceeded` is evaluating to `false`.

The `isUnprocessPdsQtyExceeded` method is implemented as:

```java
public boolean isUnprocessPdsQtyExceeded(int unprocessLimit, int dirQty) throws SFTPSendException {
	if(dirQty <= unprocessLimit) {
		return false;
	}else {
		return true;
	}
}
```

This logic is equivalent to `return dirQty > unprocessLimit;`. The `unprocessLimit` is configured in a profile and defaults to `1`.

The problem arises when there is exactly one file to be processed (`dirQty == 1`). In this scenario:
1. `dirQty <= unprocessLimit` (1 <= 1) evaluates to `true`.
2. `isUnprocessPdsQtyExceeded` returns `false`.
3. The `needToCreateNewThread` condition `(isTimeout && isUnprocessPdsQtyExceeded)` becomes `(true && false)`, which is `false`.

As a result, even though the process has timed out, the monitoring thread does not restart the worker thread, causing the application to hang until it is manually restarted.

### 2. Suggested Fix

To resolve this issue, I recommend modifying the `isUnprocessPdsQtyExceeded` method to correctly flag that the unprocessed file quantity has been met or exceeded. The check should be inclusive of the limit.

**Current Code:**
```java
public boolean isUnprocessPdsQtyExceeded(int unprocessLimit, int dirQty) throws SFTPSendException {
	if(dirQty <= unprocessLimit) {
		return false;
	}else {
		return true;
	}
}
```

**Suggested Change:**

The conditional should be changed from `dirQty <= unprocessLimit` to `dirQty < unprocessLimit`. This will make the logic equivalent to `dirQty >= unprocessLimit`.

```java
public boolean isUnprocessPdsQtyExceeded(int unprocessLimit, int dirQty) throws SFTPSendException {
	if(dirQty < unprocessLimit) {
		return false;
	}else {
		return true;
	}
}
```

With this change, when `dirQty` is 1 and `unprocessLimit` is 1, the condition `1 < 1` is false, and the method will correctly return `true`. This will allow the `needToCreateNewThread` condition to be met, triggering a restart of the SFTP thread as intended.

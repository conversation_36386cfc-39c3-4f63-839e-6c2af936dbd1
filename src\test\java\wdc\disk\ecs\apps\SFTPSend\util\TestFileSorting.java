package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

import org.junit.jupiter.api.Test;


public class TestFileSorting {
	@Test
	public void testSorting() throws FileNotFoundException {
		System.setOut(new PrintStream(new File("C:\\Users\\<USER>\\Desktop\\sortFiles")));
		String pathString = "\\\\csfsvmip01a-419-d03.wdc.com\\CSF_Talend_Share\\InsertedArchive\\POC\\TEST\\8\\202208021";
//		String pathString =  "\\\\csfsvmip01a-419-d03.wdc.com\\CSF_Talend_Share\\InsertedArchive\\POC\\309\\20220803";
		FileSortUtil fileSortUtil = new FileSortUtil();
		ArrayList<FileEntity> files = new ArrayList<FileEntity>();
		List<FileEntity> fileList = fileSortUtil.getFiles(files, new File(pathString));
		List<FileEntity> sortedFiles = fileSortUtil.sortFiles(fileList);
		for(int i=0; i < sortedFiles.size(); i++) {
			System.out.println("i:" + i + " : " + sortedFiles.get(i).toString());
		}
	}

	@Test
	public void testSorting2() throws FileNotFoundException {
//		System.setOut(new PrintStream(new File("C:\\Users\\<USER>\\Desktop\\sortFiles3")));
//		String pathString = "\\\\csfsvmip01a-419-d03.wdc.com\\CSF_Talend_Share\\InsertedArchive\\POC\\TEST\\8\\202208021";
//		ArrayList<FileEntity> files = fileSortUtil.getFiles(new File(pathString));
//		ArrayList<FileEntity> sortedFiles = fileSortUtil.sortFiles(files);
		FileSortUtil fileSortUtil = new FileSortUtil();
		LinkedList<FileEntity> files = new LinkedList<FileEntity>();
		FileEntity f1 = new FileEntity("1", 101, 100, 1);
		FileEntity f2 = new FileEntity("2", 100, 100, 1);
		FileEntity f3 = new FileEntity("3", 102, 100, 1);
		FileEntity f4 = new FileEntity("4", 102, 101, 2);
		FileEntity f5 = new FileEntity("5", 102, 101, 1);
		files.add(f1);
		files.add(f2);
		files.add(f3);
		files.add(f4);
		files.add(f5);
		
//		List<FileEntity> sortedFiles = fileSortUtil.sortFiles(files);
//		for(int i=0; i < sortedFiles.size(); i++) {
//			System.out.println("i:" + i + " : " + sortedFiles.get(i).toString());
//		}
//		
//		FileEntity[] array = sortedFiles.toArray(new FileEntity[0]);
		
	}
	
	
	@Test
	public void testArray() {
		ArrayList<String> list = new ArrayList<String>();
		List<String>  anotherList = input(list);
		String aString = anotherList.get(0);
		System.out.println(aString);
	}
	
	public List<String> input(List<String> init) {
		init.add("a");
		return init;
	}
}

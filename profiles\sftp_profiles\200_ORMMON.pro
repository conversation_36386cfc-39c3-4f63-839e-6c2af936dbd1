*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  3      |         |  0      |  3      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  4      |  Y      |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  7      |  Y      |  0      |  7      |  
|  ORMNAME               |  Char        |  10     |  System.String         |  10     |  8      |         |  0      |  8      |  
|  ORMPRG                |  Char        |  20     |  System.String         |  20     |  9      |         |  0      |  9      |  
|  DT_MSRMT              |  Timestamp   |  4      |  System.DateTime       |  4      |  10     |         |  0      |  10     |  
|  NUMPTS                |  Decimal     |  8      |  System.Decimal        |  8      |  11     |         |  0      |  11     |  
|  RADIAL                |  Decimal     |  8      |  System.Decimal        |  8      |  12     |         |  0      |  12     |  
|  MRT0T1                |  Decimal     |  8      |  System.Decimal        |  8      |  13     |         |  0      |  13     |  
|  MRT0T2                |  Decimal     |  8      |  System.Decimal        |  8      |  14     |         |  0      |  14     |  
|  MRT0T3                |  Decimal     |  8      |  System.Decimal        |  8      |  15     |         |  0      |  15     |  
|  MRT0T4                |  Decimal     |  8      |  System.Decimal        |  8      |  16     |         |  0      |  16     |  
|  MRT90T1               |  Decimal     |  8      |  System.Decimal        |  8      |  17     |         |  0      |  17     |  
|  MRT90T2               |  Decimal     |  8      |  System.Decimal        |  8      |  18     |         |  0      |  18     |  
|  MRT90T3               |  Decimal     |  8      |  System.Decimal        |  8      |  19     |         |  0      |  19     |  
|  MRT90T4               |  Decimal     |  8      |  System.Decimal        |  8      |  20     |         |  0      |  20     |  
|  MRT0B1                |  Decimal     |  8      |  System.Decimal        |  8      |  21     |         |  0      |  21     |  
|  MRT0B2                |  Decimal     |  8      |  System.Decimal        |  8      |  22     |         |  0      |  22     |  
|  MRT0B3                |  Decimal     |  8      |  System.Decimal        |  8      |  23     |         |  0      |  23     |  
|  MRT0B4                |  Decimal     |  8      |  System.Decimal        |  8      |  24     |         |  0      |  24     |  
|  MRT90B1               |  Decimal     |  8      |  System.Decimal        |  8      |  25     |         |  0      |  25     |  
|  MRT90B2               |  Decimal     |  8      |  System.Decimal        |  8      |  26     |         |  0      |  26     |  
|  MRT90B3               |  Decimal     |  8      |  System.Decimal        |  8      |  27     |         |  0      |  27     |  
|  MRT90B4               |  Decimal     |  8      |  System.Decimal        |  8      |  28     |         |  0      |  28     |  
|  AVGMRT0T              |  Decimal     |  8      |  System.Decimal        |  8      |  29     |         |  0      |  29     |  
|  AVGMRT90T             |  Decimal     |  8      |  System.Decimal        |  8      |  30     |         |  0      |  30     |  
|  AVGORT                |  Decimal     |  8      |  System.Decimal        |  8      |  31     |         |  0      |  31     |  
|  AVGMRT0B              |  Decimal     |  8      |  System.Decimal        |  8      |  32     |         |  0      |  32     |  
|  AVGMRT90B             |  Decimal     |  8      |  System.Decimal        |  8      |  33     |         |  0      |  33     |  

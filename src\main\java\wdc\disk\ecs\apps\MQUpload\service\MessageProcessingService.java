package wdc.disk.ecs.apps.MQUpload.service;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

public class MessageProcessingService {
    private final LogStream log;
    private final ConcurrentHashMap<String, AtomicLong> processedMessageIds;

    public MessageProcessingService(LogStream log) {
        this.log = log;
        this.processedMessageIds = new ConcurrentHashMap<>();
    }

    /**
     * Process a message from a specific queue.
     * Implements check and business logic processing.
     *
     * @param message The message content to process
     * @param entry The queue entry containing queue configuration
     * @throws Exception if processing fails
     */
    public void processMessage(String message, QueueMetadataEntry entry) throws Exception {
        String messageId = extractMessageId(message);
        
        // check
        if (!isMessageProcessed(messageId)) {
            try {
                // Validate message format
                validateMessage(message);
                
                // Transform message if needed
                String transformedMessage = transformMessage(message);
                
                // Process business logic
                processBusinessLogic(transformedMessage, entry);
                
                // Mark message as processed
                markMessageAsProcessed(messageId);
                
                log.write(LogStream.INFORMATION_MESSAGE, 
                    String.format("Successfully processed message %s from queue %s", 
                        messageId, entry.getName()));
                        
            } catch (Exception e) {
                log.write(LogStream.ERROR_MESSAGE, 
                    String.format("Failed to process message %s: %s", 
                        messageId, e.getMessage()));
                throw e;
            }
        } else {
            log.write(LogStream.WARNING_MESSAGE, 
                String.format("Message %s has already been processed, skipping", messageId));
        }
    }

    private String extractMessageId(String message) {
        // TODO: Implement message ID extraction logic
        // This should extract a unique identifier from the message
        // For now, return a hash of the message
        return String.valueOf(message.hashCode());
    }

    private boolean isMessageProcessed(String messageId) {
        return processedMessageIds.containsKey(messageId);
    }

    private void markMessageAsProcessed(String messageId) {
        processedMessageIds.putIfAbsent(messageId, new AtomicLong(System.currentTimeMillis()));
    }

    private void validateMessage(String message) throws Exception {
        // TODO: Implement message validation logic
        // This should validate the message format and content
        if (message == null || message.trim().isEmpty()) {
            throw new IllegalArgumentException("Message cannot be null or empty");
        }
    }

    private String transformMessage(String message) {
        // TODO: Implement message transformation logic
        // This should transform the message into the required format
        return message;
    }

    private void processBusinessLogic(String message, QueueMetadataEntry entry) throws Exception {
        // TODO: Implement business logic processing
        // This should contain the actual business logic for processing the message
        log.write(LogStream.INFORMATION_MESSAGE, 
            String.format("Processing business logic for message in queue %s", 
                entry.getName()));
    }

    /**
     * Clean up old processed message IDs to prevent memory leaks.
     * This should be called periodically to remove old entries.
     *
     * @param maxAgeMs The maximum age of processed message IDs to keep
     */
    public void cleanupProcessedMessageIds(long maxAgeMs) {
        long now = System.currentTimeMillis();
        processedMessageIds.entrySet().removeIf(entry -> 
            (now - entry.getValue().get()) > maxAgeMs);
    }
}
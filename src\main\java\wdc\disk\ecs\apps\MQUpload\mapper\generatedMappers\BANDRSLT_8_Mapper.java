package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.BANDRSLT_8;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for BANDRSLT_8
 */
public interface BANDRSLT_8_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.BANDRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{testnum},",
        "    #{bandnum},",
        "    #{reqopernum},",
        "    #{topresult},",
        "    #{bottomresult},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    REQOPERNUM,",
        "    TOPRESULT,",
        "    BOTTOMRESULT,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TESTNUM = SOURCE.TESTNUM AND TARGET.BANDNUM = SOURCE.BANDNUM AND TARGET.REQOPERNUM = SOURCE.REQOPERNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.TOPRESULT = SOURCE.TOPRESULT,",
        "    TARGET.BOTTOMRESULT = SOURCE.BOTTOMRESULT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    REQOPERNUM,",
        "    TOPRESULT,",
        "    BOTTOMRESULT,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.BANDNUM,",
        "    SOURCE.REQOPERNUM,",
        "    SOURCE.TOPRESULT,",
        "    SOURCE.BOTTOMRESULT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(BANDRSLT_8 record);
}











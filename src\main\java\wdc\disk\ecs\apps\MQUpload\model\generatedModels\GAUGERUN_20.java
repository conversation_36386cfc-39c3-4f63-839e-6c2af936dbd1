package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for GAUGERUN_20
 */
public class GAUGERUN_20  {
    private Short spindle;
    private Short typenum;
    private String headid;
    private Short prodNum;
    private java.time.LocalDateTime runTime;
    private Integer disksequence;
    private Short recsent;
    private Integer testnum;
    private Double hcf1f;
    private Double hcf2f;
    private Double hcf3f;
    private Double noise0;
    private Double noise1;
    private Double noise2;
    private Double noise3;
    private Double hcfNoise;
    private Double hcfSignal;
    private Short monStat;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;
    private Integer failcode;

    // Default constructor
    public GAUGERUN_20() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Short getTypenum() {
        return typenum;
    }

    public void setTypenum(Short typenum) {
        this.typenum = typenum;
    }

    public String getHeadid() {
        return headid;
    }

    public void setHeadid(String headid) {
        this.headid = headid;
    }

    public Short getProdNum() {
        return prodNum;
    }

    public void setProdNum(Short prodNum) {
        this.prodNum = prodNum;
    }

    public java.time.LocalDateTime getRunTime() {
        return runTime;
    }

    public void setRunTime(java.time.LocalDateTime runTime) {
        this.runTime = runTime;
    }

    public Integer getDisksequence() {
        return disksequence;
    }

    public void setDisksequence(Integer disksequence) {
        this.disksequence = disksequence;
    }

    public Short getRecsent() {
        return recsent;
    }

    public void setRecsent(Short recsent) {
        this.recsent = recsent;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Double getHcf1f() {
        return hcf1f;
    }

    public void setHcf1f(Double hcf1f) {
        this.hcf1f = hcf1f;
    }

    public Double getHcf2f() {
        return hcf2f;
    }

    public void setHcf2f(Double hcf2f) {
        this.hcf2f = hcf2f;
    }

    public Double getHcf3f() {
        return hcf3f;
    }

    public void setHcf3f(Double hcf3f) {
        this.hcf3f = hcf3f;
    }

    public Double getNoise0() {
        return noise0;
    }

    public void setNoise0(Double noise0) {
        this.noise0 = noise0;
    }

    public Double getNoise1() {
        return noise1;
    }

    public void setNoise1(Double noise1) {
        this.noise1 = noise1;
    }

    public Double getNoise2() {
        return noise2;
    }

    public void setNoise2(Double noise2) {
        this.noise2 = noise2;
    }

    public Double getNoise3() {
        return noise3;
    }

    public void setNoise3(Double noise3) {
        this.noise3 = noise3;
    }

    public Double getHcfNoise() {
        return hcfNoise;
    }

    public void setHcfNoise(Double hcfNoise) {
        this.hcfNoise = hcfNoise;
    }

    public Double getHcfSignal() {
        return hcfSignal;
    }

    public void setHcfSignal(Double hcfSignal) {
        this.hcfSignal = hcfSignal;
    }

    public Short getMonStat() {
        return monStat;
    }

    public void setMonStat(Short monStat) {
        this.monStat = monStat;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

    public Integer getFailcode() {
        return failcode;
    }

    public void setFailcode(Integer failcode) {
        this.failcode = failcode;
    }

}



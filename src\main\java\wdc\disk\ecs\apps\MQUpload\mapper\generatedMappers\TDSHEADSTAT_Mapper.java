package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSHEADSTAT;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSHEADSTAT
 */
public interface TDSHEADSTAT_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_HEAD_STAT AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{headId},",
        "    #{tdsActive},",
        "    #{mrActive},",
        "    #{mrAmpMv},",
        "    #{tdsRmsMv},",
        "    #{prescanTdsCount},",
        "    #{touchdown},",
        "    #{mrGain},",
        "    #{mrBias},",
        "    #{wrResistanceOhm},",
        "    #{mrResistanceOhm},",
        "    #{tdsResistanceOhm},",
        "    #{dfhResistanceOhm},",
        "    #{tdsResistancePreScanOhm},",
        "    #{hsaIdAuto},",
        "    #{touchdownInitial},",
        "    #{tdsBiasMode},",
        "    #{tdsBias},",
        "    #{tdsPositiveThresholdAvalanche},",
        "    #{tdsNegativeThresholdAvalanche},",
        "    #{psatLoGrassThresholdOffset},",
        "    #{psatHiGrassThresholdOffset}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    HEAD_ID,",
        "    TDS_ACTIVE,",
        "    MR_ACTIVE,",
        "    MR_AMP_MV,",
        "    TDS_RMS_MV,",
        "    PRESCAN_TDS_COUNT,",
        "    TOUCHDOWN,",
        "    MR_GAIN,",
        "    MR_BIAS,",
        "    WR_RESISTANCE_OHM,",
        "    MR_RESISTANCE_OHM,",
        "    TDS_RESISTANCE_OHM,",
        "    DFH_RESISTANCE_OHM,",
        "    TDS_RESISTANCE_PRE_SCAN_OHM,",
        "    HSA_ID_AUTO,",
        "    TOUCHDOWN_INITIAL,",
        "    TDS_BIAS_MODE,",
        "    TDS_BIAS,",
        "    TDS_POSITIVE_THRESHOLD_AVALANCHE,",
        "    TDS_NEGATIVE_THRESHOLD_AVALANCHE,",
        "    PSAT_LO_GRASS_THRESHOLD_OFFSET,",
        "    PSAT_HI_GRASS_THRESHOLD_OFFSET",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.HEAD_ID = SOURCE.HEAD_ID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.TDS_ACTIVE = SOURCE.TDS_ACTIVE,",
        "    TARGET.MR_ACTIVE = SOURCE.MR_ACTIVE,",
        "    TARGET.MR_AMP_MV = SOURCE.MR_AMP_MV,",
        "    TARGET.TDS_RMS_MV = SOURCE.TDS_RMS_MV,",
        "    TARGET.PRESCAN_TDS_COUNT = SOURCE.PRESCAN_TDS_COUNT,",
        "    TARGET.TOUCHDOWN = SOURCE.TOUCHDOWN,",
        "    TARGET.MR_GAIN = SOURCE.MR_GAIN,",
        "    TARGET.MR_BIAS = SOURCE.MR_BIAS,",
        "    TARGET.WR_RESISTANCE_OHM = SOURCE.WR_RESISTANCE_OHM,",
        "    TARGET.MR_RESISTANCE_OHM = SOURCE.MR_RESISTANCE_OHM,",
        "    TARGET.TDS_RESISTANCE_OHM = SOURCE.TDS_RESISTANCE_OHM,",
        "    TARGET.DFH_RESISTANCE_OHM = SOURCE.DFH_RESISTANCE_OHM,",
        "    TARGET.TDS_RESISTANCE_PRE_SCAN_OHM = SOURCE.TDS_RESISTANCE_PRE_SCAN_OHM,",
        "    TARGET.HSA_ID_AUTO = SOURCE.HSA_ID_AUTO,",
        "    TARGET.TOUCHDOWN_INITIAL = SOURCE.TOUCHDOWN_INITIAL,",
        "    TARGET.TDS_BIAS_MODE = SOURCE.TDS_BIAS_MODE,",
        "    TARGET.TDS_BIAS = SOURCE.TDS_BIAS,",
        "    TARGET.TDS_POSITIVE_THRESHOLD_AVALANCHE = SOURCE.TDS_POSITIVE_THRESHOLD_AVALANCHE,",
        "    TARGET.TDS_NEGATIVE_THRESHOLD_AVALANCHE = SOURCE.TDS_NEGATIVE_THRESHOLD_AVALANCHE,",
        "    TARGET.PSAT_LO_GRASS_THRESHOLD_OFFSET = SOURCE.PSAT_LO_GRASS_THRESHOLD_OFFSET,",
        "    TARGET.PSAT_HI_GRASS_THRESHOLD_OFFSET = SOURCE.PSAT_HI_GRASS_THRESHOLD_OFFSET",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    HEAD_ID,",
        "    TDS_ACTIVE,",
        "    MR_ACTIVE,",
        "    MR_AMP_MV,",
        "    TDS_RMS_MV,",
        "    PRESCAN_TDS_COUNT,",
        "    TOUCHDOWN,",
        "    MR_GAIN,",
        "    MR_BIAS,",
        "    WR_RESISTANCE_OHM,",
        "    MR_RESISTANCE_OHM,",
        "    TDS_RESISTANCE_OHM,",
        "    DFH_RESISTANCE_OHM,",
        "    TDS_RESISTANCE_PRE_SCAN_OHM,",
        "    HSA_ID_AUTO,",
        "    TOUCHDOWN_INITIAL,",
        "    TDS_BIAS_MODE,",
        "    TDS_BIAS,",
        "    TDS_POSITIVE_THRESHOLD_AVALANCHE,",
        "    TDS_NEGATIVE_THRESHOLD_AVALANCHE,",
        "    PSAT_LO_GRASS_THRESHOLD_OFFSET,",
        "    PSAT_HI_GRASS_THRESHOLD_OFFSET",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.HEAD_ID,",
        "    SOURCE.TDS_ACTIVE,",
        "    SOURCE.MR_ACTIVE,",
        "    SOURCE.MR_AMP_MV,",
        "    SOURCE.TDS_RMS_MV,",
        "    SOURCE.PRESCAN_TDS_COUNT,",
        "    SOURCE.TOUCHDOWN,",
        "    SOURCE.MR_GAIN,",
        "    SOURCE.MR_BIAS,",
        "    SOURCE.WR_RESISTANCE_OHM,",
        "    SOURCE.MR_RESISTANCE_OHM,",
        "    SOURCE.TDS_RESISTANCE_OHM,",
        "    SOURCE.DFH_RESISTANCE_OHM,",
        "    SOURCE.TDS_RESISTANCE_PRE_SCAN_OHM,",
        "    SOURCE.HSA_ID_AUTO,",
        "    SOURCE.TOUCHDOWN_INITIAL,",
        "    SOURCE.TDS_BIAS_MODE,",
        "    SOURCE.TDS_BIAS,",
        "    SOURCE.TDS_POSITIVE_THRESHOLD_AVALANCHE,",
        "    SOURCE.TDS_NEGATIVE_THRESHOLD_AVALANCHE,",
        "    SOURCE.PSAT_LO_GRASS_THRESHOLD_OFFSET,",
        "    SOURCE.PSAT_HI_GRASS_THRESHOLD_OFFSET",
        "  )"
    })
    void insertOrUpdate(TDSHEADSTAT record);
}











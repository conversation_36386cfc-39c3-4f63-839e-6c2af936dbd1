package wdc.disk.ecs.apps.MQUpload.parser;

/**
 * Utility class for data type conversions
 * Equivalent to the BldXXX functions in the original C code
 */
public class DataTypeConverter {
    
    public static double buildDouble(String src) {
        if (src == null) {
            return 0.0;
        }
        try {
            return Double.parseDouble(src);
        } catch (NumberFormatException e) {
            return 0.0;
        }
    }

    public static DoubleWithNull buildDoubleWithNull(String src) {
        if (src == null || "null".equalsIgnoreCase(src)) {
            return new DoubleWithNull(0.0, true);
        }
        try {
            return new DoubleWithNull(Double.parseDouble(src), false);
        } catch (NumberFormatException e) {
            return new DoubleWithNull(0.0, true);
        }
    }

    public static short buildShort(String src) {
        if (src == null) {
            return 0;
        }
        try {
            return Short.parseShort(src);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public static int buildInt(String src) {
        if (src == null) {
            return 0;
        }
        try {
            return Integer.parseInt(src);
        } catch (NumberFormatException e) {
            return 0;
        }
    }

    public static long buildInt64(String src) {
        if (src == null) {
            return 0L;
        }
        try {
            return Long.parseLong(src);
        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    public static String buildString(String src, int length) {
        if (src == null) {
            return "NULL".repeat((length + 3) / 4);
        }
        return String.format("%-" + length + "." + length + "s", src);
    }

    // Helper class for handling nullable doubles
    public static class DoubleWithNull {
        private final double value;
        private final boolean isNull;

        public DoubleWithNull(double value, boolean isNull) {
            this.value = value;
            this.isNull = isNull;
        }

        public double getValue() {
            return value;
        }

        public boolean isNull() {
            return isNull;
        }
    }
}
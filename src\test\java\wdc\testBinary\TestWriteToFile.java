package wdc.testBinary;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import org.junit.jupiter.api.Test;

public class TestWriteToFile {
	@Test
	public void testWrite() throws IOException {
		FileOutputStream outputStream = null;
		String fileName = "C:\\Users\\<USER>\\Desktop\\testfile.txt";
		try {
			outputStream = new FileOutputStream(fileName);
			outputStream.write("123".getBytes());
			outputStream.write("\n".getBytes());
			outputStream.write("345".getBytes());
		}catch (Exception e) {
			e.printStackTrace();
		}finally {
			if(outputStream != null) {
				outputStream.close();
			}
		}
	}

	@Test
	public void testMap() {
		Map<String, StringBuffer> map = new HashMap<String, StringBuffer>();
		String[] list = {"1","1","2","3"};
		String[] listVal = {"foo", "bar", "too", "kk"};
		for(int i=0; i<list.length; i++) {
			StringBuffer stringBuffer = null; 
			if(!map.containsKey(list[i])) {
				stringBuffer = new StringBuffer();
			}else {
				stringBuffer = map.get(list[i]);
			}
			map.put(list[i], stringBuffer.append(listVal[i]));
		}
		
		Collection<StringBuffer> values = map.values();
		Iterator<StringBuffer> iterator = values.iterator();
		while(iterator.hasNext()) {
			StringBuffer next = iterator.next();
			System.out.println(next.toString());
		}	
	}
}

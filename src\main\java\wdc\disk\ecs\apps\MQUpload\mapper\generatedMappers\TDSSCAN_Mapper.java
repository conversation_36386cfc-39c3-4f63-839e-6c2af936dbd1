package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSSCAN;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSSCAN
 */
public interface TDSSCAN_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_SCAN AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{rawdataFile},",
        "    #{lot},",
        "    #{disksequence},",
        "    #{scanTimeMsec},",
        "    #{binNumber},",
        "    #{vacummPressure},",
        "    #{spindle},",
        "    #{mapFilename},",
        "    #{product},",
        "    #{cellId},",
        "    #{finalGrade},",
        "    #{glideGrade},",
        "    #{certGrade},",
        "    #{recipeFilename},",
        "    #{hsa1Id},",
        "    #{hsa2Id},",
        "    #{hsa3Id},",
        "    #{totalToscCount},",
        "    #{evalCode},",
        "    #{hsa1CycleCount},",
        "    #{hsa2CycleCount},",
        "    #{hsa3CycleCount},",
        "    #{testerType},",
        "    #{softwareVersion},",
        "    #{scanStartRadiusMm},",
        "    #{scanStopRadiusMm},",
        "    #{formFactor},",
        "    #{scanOption},",
        "    #{hbo},",
        "    #{autoSitemarkByGrade},",
        "    #{supplyCassette},",
        "    #{outputCassette},",
        "    #{outputLot},",
        "    #{outputSlot},",
        "    #{unitid},",
        "    #{ruleNumber},",
        "    #{expid},",
        "    #{inputVariety},",
        "    #{robotClass},",
        "    #{retested},",
        "    #{acErased},",
        "    #{certifyScanned},",
        "    #{tdsScanned},",
        "    #{tdesDfaActive},",
        "    #{tdesDfaMarked},",
        "    #{testSequence},",
        "    #{finalgradePass},",
        "    #{topgradePass},",
        "    #{botgradePass},",
        "    #{topgrade},",
        "    #{botgrade},",
        "    #{checksumGeneral},",
        "    #{checksumTds},",
        "    #{checksumCert},",
        "    #{checksumGrading},",
        "    #{rttcCounts},",
        "    #{dcErased},",
        "    #{testedTonescan}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    RAWDATA_FILE,",
        "    LOT,",
        "    DISKSEQUENCE,",
        "    SCAN_TIME_MSEC,",
        "    BIN_NUMBER,",
        "    VACUMM_PRESSURE,",
        "    SPINDLE,",
        "    MAP_FILENAME,",
        "    PRODUCT,",
        "    CELL_ID,",
        "    FINAL_GRADE,",
        "    GLIDE_GRADE,",
        "    CERT_GRADE,",
        "    RECIPE_FILENAME,",
        "    HSA1_ID,",
        "    HSA2_ID,",
        "    HSA3_ID,",
        "    TOTAL_TOSC_COUNT,",
        "    EVAL_CODE,",
        "    HSA1_CYCLE_COUNT,",
        "    HSA2_CYCLE_COUNT,",
        "    HSA3_CYCLE_COUNT,",
        "    TESTER_TYPE,",
        "    SOFTWARE_VERSION,",
        "    SCAN_START_RADIUS_MM,",
        "    SCAN_STOP_RADIUS_MM,",
        "    FORM_FACTOR,",
        "    SCAN_OPTION,",
        "    HBO,",
        "    AUTO_SITEMARK_BY_GRADE,",
        "    SUPPLY_CASSETTE,",
        "    OUTPUT_CASSETTE,",
        "    OUTPUT_LOT,",
        "    OUTPUT_SLOT,",
        "    UNITID,",
        "    RULE_NUMBER,",
        "    EXPID,",
        "    INPUT_VARIETY,",
        "    ROBOT_CLASS,",
        "    RETESTED,",
        "    AC_ERASED,",
        "    CERTIFY_SCANNED,",
        "    TDS_SCANNED,",
        "    TDES_DFA_ACTIVE,",
        "    TDES_DFA_MARKED,",
        "    TEST_SEQUENCE,",
        "    FINALGRADE_PASS,",
        "    TOPGRADE_PASS,",
        "    BOTGRADE_PASS,",
        "    TOPGRADE,",
        "    BOTGRADE,",
        "    CHECKSUM_GENERAL,",
        "    CHECKSUM_TDS,",
        "    CHECKSUM_CERT,",
        "    CHECKSUM_GRADING,",
        "    RTTC_COUNTS,",
        "    DC_ERASED,",
        "    TESTED_TONESCAN",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.RAWDATA_FILE = SOURCE.RAWDATA_FILE,",
        "    TARGET.LOT = SOURCE.LOT,",
        "    TARGET.DISKSEQUENCE = SOURCE.DISKSEQUENCE,",
        "    TARGET.SCAN_TIME_MSEC = SOURCE.SCAN_TIME_MSEC,",
        "    TARGET.BIN_NUMBER = SOURCE.BIN_NUMBER,",
        "    TARGET.VACUMM_PRESSURE = SOURCE.VACUMM_PRESSURE,",
        "    TARGET.MAP_FILENAME = SOURCE.MAP_FILENAME,",
        "    TARGET.PRODUCT = SOURCE.PRODUCT,",
        "    TARGET.CELL_ID = SOURCE.CELL_ID,",
        "    TARGET.FINAL_GRADE = SOURCE.FINAL_GRADE,",
        "    TARGET.GLIDE_GRADE = SOURCE.GLIDE_GRADE,",
        "    TARGET.CERT_GRADE = SOURCE.CERT_GRADE,",
        "    TARGET.RECIPE_FILENAME = SOURCE.RECIPE_FILENAME,",
        "    TARGET.HSA1_ID = SOURCE.HSA1_ID,",
        "    TARGET.HSA2_ID = SOURCE.HSA2_ID,",
        "    TARGET.HSA3_ID = SOURCE.HSA3_ID,",
        "    TARGET.TOTAL_TOSC_COUNT = SOURCE.TOTAL_TOSC_COUNT,",
        "    TARGET.EVAL_CODE = SOURCE.EVAL_CODE,",
        "    TARGET.HSA1_CYCLE_COUNT = SOURCE.HSA1_CYCLE_COUNT,",
        "    TARGET.HSA2_CYCLE_COUNT = SOURCE.HSA2_CYCLE_COUNT,",
        "    TARGET.HSA3_CYCLE_COUNT = SOURCE.HSA3_CYCLE_COUNT,",
        "    TARGET.TESTER_TYPE = SOURCE.TESTER_TYPE,",
        "    TARGET.SOFTWARE_VERSION = SOURCE.SOFTWARE_VERSION,",
        "    TARGET.SCAN_START_RADIUS_MM = SOURCE.SCAN_START_RADIUS_MM,",
        "    TARGET.SCAN_STOP_RADIUS_MM = SOURCE.SCAN_STOP_RADIUS_MM,",
        "    TARGET.FORM_FACTOR = SOURCE.FORM_FACTOR,",
        "    TARGET.SCAN_OPTION = SOURCE.SCAN_OPTION,",
        "    TARGET.HBO = SOURCE.HBO,",
        "    TARGET.AUTO_SITEMARK_BY_GRADE = SOURCE.AUTO_SITEMARK_BY_GRADE,",
        "    TARGET.SUPPLY_CASSETTE = SOURCE.SUPPLY_CASSETTE,",
        "    TARGET.OUTPUT_CASSETTE = SOURCE.OUTPUT_CASSETTE,",
        "    TARGET.OUTPUT_LOT = SOURCE.OUTPUT_LOT,",
        "    TARGET.OUTPUT_SLOT = SOURCE.OUTPUT_SLOT,",
        "    TARGET.UNITID = SOURCE.UNITID,",
        "    TARGET.RULE_NUMBER = SOURCE.RULE_NUMBER,",
        "    TARGET.EXPID = SOURCE.EXPID,",
        "    TARGET.INPUT_VARIETY = SOURCE.INPUT_VARIETY,",
        "    TARGET.ROBOT_CLASS = SOURCE.ROBOT_CLASS,",
        "    TARGET.RETESTED = SOURCE.RETESTED,",
        "    TARGET.AC_ERASED = SOURCE.AC_ERASED,",
        "    TARGET.CERTIFY_SCANNED = SOURCE.CERTIFY_SCANNED,",
        "    TARGET.TDS_SCANNED = SOURCE.TDS_SCANNED,",
        "    TARGET.TDES_DFA_ACTIVE = SOURCE.TDES_DFA_ACTIVE,",
        "    TARGET.TDES_DFA_MARKED = SOURCE.TDES_DFA_MARKED,",
        "    TARGET.TEST_SEQUENCE = SOURCE.TEST_SEQUENCE,",
        "    TARGET.FINALGRADE_PASS = SOURCE.FINALGRADE_PASS,",
        "    TARGET.TOPGRADE_PASS = SOURCE.TOPGRADE_PASS,",
        "    TARGET.BOTGRADE_PASS = SOURCE.BOTGRADE_PASS,",
        "    TARGET.TOPGRADE = SOURCE.TOPGRADE,",
        "    TARGET.BOTGRADE = SOURCE.BOTGRADE,",
        "    TARGET.CHECKSUM_GENERAL = SOURCE.CHECKSUM_GENERAL,",
        "    TARGET.CHECKSUM_TDS = SOURCE.CHECKSUM_TDS,",
        "    TARGET.CHECKSUM_CERT = SOURCE.CHECKSUM_CERT,",
        "    TARGET.CHECKSUM_GRADING = SOURCE.CHECKSUM_GRADING,",
        "    TARGET.RTTC_COUNTS = SOURCE.RTTC_COUNTS,",
        "    TARGET.DC_ERASED = SOURCE.DC_ERASED,",
        "    TARGET.TESTED_TONESCAN = SOURCE.TESTED_TONESCAN",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    RAWDATA_FILE,",
        "    LOT,",
        "    DISKSEQUENCE,",
        "    SCAN_TIME_MSEC,",
        "    BIN_NUMBER,",
        "    VACUMM_PRESSURE,",
        "    SPINDLE,",
        "    MAP_FILENAME,",
        "    PRODUCT,",
        "    CELL_ID,",
        "    FINAL_GRADE,",
        "    GLIDE_GRADE,",
        "    CERT_GRADE,",
        "    RECIPE_FILENAME,",
        "    HSA1_ID,",
        "    HSA2_ID,",
        "    HSA3_ID,",
        "    TOTAL_TOSC_COUNT,",
        "    EVAL_CODE,",
        "    HSA1_CYCLE_COUNT,",
        "    HSA2_CYCLE_COUNT,",
        "    HSA3_CYCLE_COUNT,",
        "    TESTER_TYPE,",
        "    SOFTWARE_VERSION,",
        "    SCAN_START_RADIUS_MM,",
        "    SCAN_STOP_RADIUS_MM,",
        "    FORM_FACTOR,",
        "    SCAN_OPTION,",
        "    HBO,",
        "    AUTO_SITEMARK_BY_GRADE,",
        "    SUPPLY_CASSETTE,",
        "    OUTPUT_CASSETTE,",
        "    OUTPUT_LOT,",
        "    OUTPUT_SLOT,",
        "    UNITID,",
        "    RULE_NUMBER,",
        "    EXPID,",
        "    INPUT_VARIETY,",
        "    ROBOT_CLASS,",
        "    RETESTED,",
        "    AC_ERASED,",
        "    CERTIFY_SCANNED,",
        "    TDS_SCANNED,",
        "    TDES_DFA_ACTIVE,",
        "    TDES_DFA_MARKED,",
        "    TEST_SEQUENCE,",
        "    FINALGRADE_PASS,",
        "    TOPGRADE_PASS,",
        "    BOTGRADE_PASS,",
        "    TOPGRADE,",
        "    BOTGRADE,",
        "    CHECKSUM_GENERAL,",
        "    CHECKSUM_TDS,",
        "    CHECKSUM_CERT,",
        "    CHECKSUM_GRADING,",
        "    RTTC_COUNTS,",
        "    DC_ERASED,",
        "    TESTED_TONESCAN",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.RAWDATA_FILE,",
        "    SOURCE.LOT,",
        "    SOURCE.DISKSEQUENCE,",
        "    SOURCE.SCAN_TIME_MSEC,",
        "    SOURCE.BIN_NUMBER,",
        "    SOURCE.VACUMM_PRESSURE,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.MAP_FILENAME,",
        "    SOURCE.PRODUCT,",
        "    SOURCE.CELL_ID,",
        "    SOURCE.FINAL_GRADE,",
        "    SOURCE.GLIDE_GRADE,",
        "    SOURCE.CERT_GRADE,",
        "    SOURCE.RECIPE_FILENAME,",
        "    SOURCE.HSA1_ID,",
        "    SOURCE.HSA2_ID,",
        "    SOURCE.HSA3_ID,",
        "    SOURCE.TOTAL_TOSC_COUNT,",
        "    SOURCE.EVAL_CODE,",
        "    SOURCE.HSA1_CYCLE_COUNT,",
        "    SOURCE.HSA2_CYCLE_COUNT,",
        "    SOURCE.HSA3_CYCLE_COUNT,",
        "    SOURCE.TESTER_TYPE,",
        "    SOURCE.SOFTWARE_VERSION,",
        "    SOURCE.SCAN_START_RADIUS_MM,",
        "    SOURCE.SCAN_STOP_RADIUS_MM,",
        "    SOURCE.FORM_FACTOR,",
        "    SOURCE.SCAN_OPTION,",
        "    SOURCE.HBO,",
        "    SOURCE.AUTO_SITEMARK_BY_GRADE,",
        "    SOURCE.SUPPLY_CASSETTE,",
        "    SOURCE.OUTPUT_CASSETTE,",
        "    SOURCE.OUTPUT_LOT,",
        "    SOURCE.OUTPUT_SLOT,",
        "    SOURCE.UNITID,",
        "    SOURCE.RULE_NUMBER,",
        "    SOURCE.EXPID,",
        "    SOURCE.INPUT_VARIETY,",
        "    SOURCE.ROBOT_CLASS,",
        "    SOURCE.RETESTED,",
        "    SOURCE.AC_ERASED,",
        "    SOURCE.CERTIFY_SCANNED,",
        "    SOURCE.TDS_SCANNED,",
        "    SOURCE.TDES_DFA_ACTIVE,",
        "    SOURCE.TDES_DFA_MARKED,",
        "    SOURCE.TEST_SEQUENCE,",
        "    SOURCE.FINALGRADE_PASS,",
        "    SOURCE.TOPGRADE_PASS,",
        "    SOURCE.BOTGRADE_PASS,",
        "    SOURCE.TOPGRADE,",
        "    SOURCE.BOTGRADE,",
        "    SOURCE.CHECKSUM_GENERAL,",
        "    SOURCE.CHECKSUM_TDS,",
        "    SOURCE.CHECKSUM_CERT,",
        "    SOURCE.CHECKSUM_GRADING,",
        "    SOURCE.RTTC_COUNTS,",
        "    SOURCE.DC_ERASED,",
        "    SOURCE.TESTED_TONESCAN",
        "  )"
    })
    void insertOrUpdate(TDSSCAN record);
}











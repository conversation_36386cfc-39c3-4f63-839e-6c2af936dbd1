package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TESTLOG_30
 */
public class TESTLOG_30  {
    private Short spindle;
    private Integer testnum;
    private Integer batchid;
    private Short productcode;
    private Integer diskcode;
    private String disksequence;
    private Short magrule;
    private Short testcode;
    private Integer detqcode1;
    private Integer detqcode2;
    private Integer parqcode1;
    private Integer parqcode2;
    private java.time.LocalDateTime startmag;
    private java.time.LocalDateTime stopmag;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;
    private String lot;
    private String unitid;
    private Short magrulever;
    private String magchecksum;

    // Default constructor
    public TESTLOG_30() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Integer getBatchid() {
        return batchid;
    }

    public void setBatchid(Integer batchid) {
        this.batchid = batchid;
    }

    public Short getProductcode() {
        return productcode;
    }

    public void setProductcode(Short productcode) {
        this.productcode = productcode;
    }

    public Integer getDiskcode() {
        return diskcode;
    }

    public void setDiskcode(Integer diskcode) {
        this.diskcode = diskcode;
    }

    public String getDisksequence() {
        return disksequence;
    }

    public void setDisksequence(String disksequence) {
        this.disksequence = disksequence;
    }

    public Short getMagrule() {
        return magrule;
    }

    public void setMagrule(Short magrule) {
        this.magrule = magrule;
    }

    public Short getTestcode() {
        return testcode;
    }

    public void setTestcode(Short testcode) {
        this.testcode = testcode;
    }

    public Integer getDetqcode1() {
        return detqcode1;
    }

    public void setDetqcode1(Integer detqcode1) {
        this.detqcode1 = detqcode1;
    }

    public Integer getDetqcode2() {
        return detqcode2;
    }

    public void setDetqcode2(Integer detqcode2) {
        this.detqcode2 = detqcode2;
    }

    public Integer getParqcode1() {
        return parqcode1;
    }

    public void setParqcode1(Integer parqcode1) {
        this.parqcode1 = parqcode1;
    }

    public Integer getParqcode2() {
        return parqcode2;
    }

    public void setParqcode2(Integer parqcode2) {
        this.parqcode2 = parqcode2;
    }

    public java.time.LocalDateTime getStartmag() {
        return startmag;
    }

    public void setStartmag(java.time.LocalDateTime startmag) {
        this.startmag = startmag;
    }

    public java.time.LocalDateTime getStopmag() {
        return stopmag;
    }

    public void setStopmag(java.time.LocalDateTime stopmag) {
        this.stopmag = stopmag;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

    public String getLot() {
        return lot;
    }

    public void setLot(String lot) {
        this.lot = lot;
    }

    public String getUnitid() {
        return unitid;
    }

    public void setUnitid(String unitid) {
        this.unitid = unitid;
    }

    public Short getMagrulever() {
        return magrulever;
    }

    public void setMagrulever(Short magrulever) {
        this.magrulever = magrulever;
    }

    public String getMagchecksum() {
        return magchecksum;
    }

    public void setMagchecksum(String magchecksum) {
        this.magchecksum = magchecksum;
    }

}



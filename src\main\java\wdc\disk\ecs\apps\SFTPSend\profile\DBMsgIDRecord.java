package wdc.disk.ecs.apps.SFTPSend.profile;

import java.util.StringTokenizer;

import ibm.disk.profiles.ProfileException;

/**
 *********************************************************************
 *                                                                   *
 *  DBMsgIDRecord	                                                 *
 *                                                                   *
 *********************************************************************
 *Configured in DBMsgID.pro:
 **      +------------PDS Type
 **      |           +----------Product
 **      |           |         +------DB Message ID
 **      |           |         |            +-------For which table it will be inserted into
 **      |           |         |            |        
 **      |           |         |            |
 **      |           |         |            |
 *|LIDALARM      |   *   |    209   |   COMPONENT    (3.1)
 *|ECSCLASSES    |   *   |    268   |   ECSCLASSES   (5.17)
 *
 * @Date 2021-08-09 15:58:05
 * <AUTHOR>
 */
public class DBMsgIDRecord extends ibm.disk.profiles.ProfileObject {
	public static String COMPARE_BY_TYPE = "PDSTYPE";
	public static String COMPARE_BY_MSGID = "MSGID";
	private String pdsType;
	private String dbMsgID;
	private String msgType;
	private String tableName;
	private int sendQty;
	private String splitter = "/"; //default to "/", or mapping to splitter section in ecs.ini/sftp.ini
	private String splitMode;
	private String folder;
	
	/**
	 * DBMsgIDRecord constructor comment.
	 * @param values java.lang.String
	 * @exception ibm.disk.profiles.ProfileException The exception description.
	 */
	public DBMsgIDRecord(String values) throws ibm.disk.profiles.ProfileException {
		super(values);
		StringTokenizer st = new StringTokenizer(values.trim(), "|");
//		int MIN_TOKENS = 4;
//		if (st.countTokens() < MIN_TOKENS) {
//			throw (new ProfileException("Invalid number of values: Found " + st.countTokens() + ", must be " + MIN_TOKENS));
//		}
		String fieldName = null, fieldValue = null;
		try {
			fieldName = "PDS Type";
			fieldValue = "";
			setPdsType(st.nextToken().trim());
//			fieldName = "Product";
//			fieldValue = "";
//			setProduct(st.nextToken().trim());
			fieldName = "DB Message ID";
			fieldValue = "";
			setDBMsgID(st.nextToken().trim());
			fieldName = "Msg Type";
			setMsgType(st.nextToken().trim());
			fieldName = "Table Name";
			fieldValue = "";
			setTableName(st.nextToken().trim());
			fieldName = "Send Qty";
			fieldValue = st.nextToken().trim();
			if(fieldValue == null || fieldValue.isEmpty()) {
				sendQty = 10000;
			}else {
				sendQty = Integer.valueOf(fieldValue.trim());
			}
			
			fieldName = "splitter";
			fieldValue = "";	
			String tmp = st.nextToken();
			if(null != tmp && tmp.trim().length() != 0) {
				setSplitter(tmp.trim());				
			}
			
			fieldName = "splitMode";
			fieldValue = st.nextToken().trim();	
			if(!fieldValue.isEmpty()) {
				setSplitMode(fieldValue);
			}
			
			fieldName = "folder";
			fieldValue = st.nextToken().trim();	
			if(!fieldValue.isEmpty()) {
				setFolder(fieldValue);
			}
			
		} catch (Exception e) {
			throw (new ProfileException("Invalid field: " + fieldName + "=" + fieldValue));
		}
	}
	
	/**
	 * This method must be implemented so that it compares the values passed
	 * for the type of comparison.
	 * Creation date: (10/16/2003 2:04:46 PM)
	 * @return boolean true if the record matches the list of objects to compare,
	 * false is they do not match.
	 * @param compareType int This is used if there are more than one type of
	 * comparisons used for the profile. 
	 * @param value java.util.Vector List of Objects to compare to this record
	 * for a match. 
	 */
	public boolean compare(int compareType, java.util.Vector values) throws ibm.disk.profiles.ProfileException {
		
	
		boolean bResult = false;
		//	if(compareType == 0) {//ascii
		//	String pdsType = null;
			try {
				String typeString = ((String)values.get(0)).trim();
				String valueString = ((String)values.get(1)).trim();
				if(typeString.equals(DBMsgIDRecord.COMPARE_BY_TYPE))
				{
					if ((getPdsType().equalsIgnoreCase(valueString))) {
						bResult = true;
					}
				}else if (typeString.equals(DBMsgIDRecord.COMPARE_BY_MSGID)) {
					if ((getDBMsgID().equalsIgnoreCase(valueString))) {
						bResult = true;
					}
				}				

			} catch (Exception e) {
				throw (new ProfileException("Invalid pdsType value found in search Vector for DBMsgIDRecord.compare()"));
			}
						
//			String product = null;
//			try { 
//				product = ((String)values.get(1)).trim();
//			} catch (Exception e) {
//				throw (new ProfileException("Invalid product value found in search Vector for DBMsgIDRecord.compare()"));
//			}
			
	/*	}
		//binary
		if(compareType == 1) {
			String msgId = null;
			try {
				msgId = ((String)values.get(0)).trim();
				if (ibm.disk.extensions.StringMethods.wildStrCompare(getDBMsgID(), msgId)){
					return true;
				} 
			} catch (Exception e) {
				throw (new ProfileException("Invalid msg id value found in search Vector for DBMsgIDRecord.compare()"));
			}
		}
	*/
		return bResult;
	}
	
	/**
	 * Creation date: 2021-08-10 08:59:54
	 * @return String
	 */
	public String getPdsType() {
		return pdsType;
	}

	/**
	 * Creation date: 2021-08-10 08:59:54
	 * @param pdsType String
	 */
	public void setPdsType(String pdsType) {
		this.pdsType = pdsType;
	}

	/**
	 * Creation date: 2021-08-10 08:45:15
	 * @return String
	 */
	public String getProduct() {
		return msgType;
	}
	
	/**
	 * Creation date: 2021-08-10 08:45:15
	 * @param product String
	 */
	public void setProduct(String product) {
		this.msgType = product;
	}
	
	/**
	 * Creation date: 2021-08-10 08:45:15
	 * @return String
	 */
	public String getDBMsgID() {
		return dbMsgID;
	}
	
	/**
	 * Creation date: 2021-08-10 08:45:15
	 * @param dbMsgID String
	 */
	public void setDBMsgID(String dbMsgID) {
		this.dbMsgID = dbMsgID;
	}
	
	/**
	 * Creation date: 2021-08-10 08:45:15
	 * @return String
	 */
	public String getTableName() {
		return tableName;
	}
	
	/**
	 * Creation date: 2021-08-10 08:45:15
	 * @param tableName String
	 */
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}

	public int getSendQty() {
		return sendQty;
	}

	public void setSendQty(int sendQty) {
		this.sendQty = sendQty;
	}

	public String getMsgType() {
		return msgType;
	}

	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}

	public String getSplitter() {
		return splitter;
	}

	public void setSplitter(String splitter) {
		this.splitter = splitter;
	}

	public String getSplitMode() {
		return splitMode;
	}

	public void setSplitMode(String splitMode) {
		this.splitMode = splitMode;
	}

	public String getFolder() {
		return folder;
	}

	public void setFolder(String folder) {
		this.folder = folder;
	}
	
	
	
	
}

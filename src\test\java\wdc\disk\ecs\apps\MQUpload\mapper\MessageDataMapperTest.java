//package wdc.disk.ecs.apps.MQUpload.mapper;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
//import java.time.LocalDateTime;
//import java.util.ArrayList;
//import java.util.List;
//import java.util.Map;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import wdc.disk.ecs.apps.MQUpload.parser.ParseResult;
//import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser.TableDefinition;
//import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser.ColumnDefinition;
//import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;
//
//@ExtendWith(MockitoExtension.class)
//public class MessageDataMapperTest {
//
//    @Mock
//    private TableSchemaManager mockSchemaManager;
//
//    @Mock
//    private ParseResult mockParseResult;
//
//    private MessageDataMapper messageDataMapper;
//    private TableDefinition tableDefinition;
//    private List<ColumnDefinition> columns;
//
//    @BeforeEach
//    void setUp() {
//        messageDataMapper = new MessageDataMapper(mockSchemaManager);
//        
//        // Setup table definition with columns
//        columns = new ArrayList<>();
//        columns.add(createColumnDefinition("INT_COLUMN", "System.Int32", 0));
//        columns.add(createColumnDefinition("DECIMAL_COLUMN", "System.Decimal", 1));
//        columns.add(createColumnDefinition("DATETIME_COLUMN", "System.DateTime", 2));
//        columns.add(createColumnDefinition("STRING_COLUMN", "System.String", 3));
//        
//        tableDefinition = new TableDefinition("TEST_TABLE");
//        columns.forEach(tableDefinition::addColumn);
//    }
//
//    private ColumnDefinition createColumnDefinition(String name, String type, int index) {
//        ColumnDefinition column = new ColumnDefinition();
//        column.setColumnName(name);
//        column.setSystemType(type);
//        column.setValueIndex(index);
//        return column;
//    }
//
//    @Test
//    void testMapToColumns_WithValidData() {
//        // Arrange
//        String messageId = "TEST_MSG_ID";
//        String[] parsedFields = {"123", "45.67", "2023-12-25 10:30:00", "test string"};
//        LocalDateTime testDateTime = LocalDateTime.now();
//
//        when(mockSchemaManager.getTableMapping(messageId)).thenReturn("TEST_TABLE");
//        when(mockSchemaManager.getTableDefinition(messageId, "TEST_TABLE")).thenReturn(tableDefinition);
//        when(mockParseResult.getParsedFields()).thenReturn(parsedFields);
//        when(mockParseResult.getDateTime()).thenReturn(testDateTime);
//
//        // Act
//        Map<String, Object> result = messageDataMapper.mapToColumns(messageId, mockParseResult);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(6, result.size()); // 4 columns + MESSAGE_ID + DTINSERT
//        assertEquals(123, result.get("INT_COLUMN"));
//        assertEquals(45.67, result.get("DECIMAL_COLUMN"));
//        assertEquals("test string", result.get("STRING_COLUMN"));
//        assertEquals(messageId, result.get("MESSAGE_ID"));
//        assertEquals(testDateTime, result.get("DTINSERT"));
//    }
//
//    @Test
//    void testMapToColumns_WithNullValues() {
//        // Arrange
//        String messageId = "TEST_MSG_ID";
//        String[] parsedFields = {"", null, "  ", "valid"};
//        LocalDateTime testDateTime = LocalDateTime.now();
//
//        when(mockSchemaManager.getTableMapping(messageId)).thenReturn("TEST_TABLE");
//        when(mockSchemaManager.getTableDefinition(messageId, "TEST_TABLE")).thenReturn(tableDefinition);
//        when(mockParseResult.getParsedFields()).thenReturn(parsedFields);
//        when(mockParseResult.getDateTime()).thenReturn(testDateTime);
//
//        // Act
//        Map<String, Object> result = messageDataMapper.mapToColumns(messageId, mockParseResult);
//
//        // Assert
//        assertNotNull(result);
//        assertNull(result.get("INT_COLUMN"));
//        assertNull(result.get("DECIMAL_COLUMN"));
//        assertNull(result.get("DATETIME_COLUMN"));
//        assertEquals("valid", result.get("STRING_COLUMN"));
//    }
//
//    @Test
//    void testMapToColumns_WithInvalidNumberFormat() {
//        // Arrange
//        String messageId = "TEST_MSG_ID";
//        String[] parsedFields = {"not_a_number", "45.67", "2023-12-25 10:30:00", "test"};
//
//        when(mockSchemaManager.getTableMapping(messageId)).thenReturn("TEST_TABLE");
//        when(mockSchemaManager.getTableDefinition(messageId, "TEST_TABLE")).thenReturn(tableDefinition);
//        when(mockParseResult.getParsedFields()).thenReturn(parsedFields);
//        when(mockParseResult.getDateTime()).thenReturn(LocalDateTime.now());
//
//        // Act & Assert
//        assertThrows(NumberFormatException.class, () -> 
//            messageDataMapper.mapToColumns(messageId, mockParseResult)
//        );
//    }
//
//    @Test
//    void testMapToColumns_WithIndexOutOfBounds() {
//        // Arrange
//        String messageId = "TEST_MSG_ID";
//        String[] parsedFields = {"123"}; // Only one field
//        LocalDateTime testDateTime = LocalDateTime.now();
//
//        when(mockSchemaManager.getTableMapping(messageId)).thenReturn("TEST_TABLE");
//        when(mockSchemaManager.getTableDefinition(messageId, "TEST_TABLE")).thenReturn(tableDefinition);
//        when(mockParseResult.getParsedFields()).thenReturn(parsedFields);
//        when(mockParseResult.getDateTime()).thenReturn(testDateTime);
//
//        // Act
//        Map<String, Object> result = messageDataMapper.mapToColumns(messageId, mockParseResult);
//
//        // Assert
//        assertNotNull(result);
//        assertEquals(123, result.get("INT_COLUMN"));
//        assertNull(result.get("DECIMAL_COLUMN"));
//        assertNull(result.get("DATETIME_COLUMN"));
//        assertNull(result.get("STRING_COLUMN"));
//    }
//}

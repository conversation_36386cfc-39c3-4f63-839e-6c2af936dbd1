package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.BATCHINFO_11;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for BATCHINFO_11
 */
public interface BATCHINFO_11_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.BATCHINFO AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{batchid},",
        "    #{batchtime},",
        "    #{expid},",
        "    #{lot},",
        "    #{variety},",
        "    #{product},",
        "    #{prodtype},",
        "    #{resource},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    BATCHID,",
        "    BATCHTIME,",
        "    EXPID,",
        "    LOT,",
        "    VARIETY,",
        "    PRODUCT,",
        "    PRODTYPE,",
        "    RESOURCE,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.BATCHID = SOURCE.BATCHID AND TARGET.LOT = SOURCE.LOT",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.BATCHTIME = SOURCE.BATCHTIME,",
        "    TARGET.EXPID = SOURCE.EXPID,",
        "    TARGET.VARIETY = SOURCE.VARIETY,",
        "    TARGET.PRODUCT = SOURCE.PRODUCT,",
        "    TARGET.PRODTYPE = SOURCE.PRODTYPE,",
        "    TARGET.RESOURCE = SOURCE.RESOURCE,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    BATCHID,",
        "    BATCHTIME,",
        "    EXPID,",
        "    LOT,",
        "    VARIETY,",
        "    PRODUCT,",
        "    PRODTYPE,",
        "    RESOURCE,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.BATCHID,",
        "    SOURCE.BATCHTIME,",
        "    SOURCE.EXPID,",
        "    SOURCE.LOT,",
        "    SOURCE.VARIETY,",
        "    SOURCE.PRODUCT,",
        "    SOURCE.PRODTYPE,",
        "    SOURCE.RESOURCE,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(BATCHINFO_11 record);
}











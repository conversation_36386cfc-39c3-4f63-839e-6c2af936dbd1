package wdc.disk.ecs.apps.MQUpload.model;

public class QueueMetadataEntry {
    private String name;
    private String type;
    private boolean durable;
    private boolean exclusive;
    private boolean autoDelete;
    private String exchange;
    private String exchangeType;
    private String routingKey;
    private int quorumSize;
    

  
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public boolean isDurable() {
		return durable;
	}
	public void setDurable(boolean durable) {
		this.durable = durable;
	}
	public boolean isExclusive() {
		return exclusive;
	}
	public void setExclusive(boolean exclusive) {
		this.exclusive = exclusive;
	}
	public boolean isAutoDelete() {
		return autoDelete;
	}
	public void setAutoDelete(boolean autoDelete) {
		this.autoDelete = autoDelete;
	}
	public String getExchange() {
		return exchange;
	}
	public void setExchange(String exchange) {
		this.exchange = exchange;
	}
	public String getExchangeType() {
		return exchangeType;
	}
	public void setExchangeType(String exchangeType) {
		this.exchangeType = exchangeType;
	}
	public String getRoutingKey() {
		return routingKey;
	}
	public void setRoutingKey(String routingKey) {
		this.routingKey = routingKey;
	}
	public int getQuorumSize() {
		return quorumSize;
	}
	public void setQuorumSize(int quorumSize) {
		this.quorumSize = quorumSize;
	}
  


}

package wdc.disk.ecs.apps.MQUpload.util;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.ConsumerProperties;
import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeoutException;

public class RabbitMQSetupUtil {
    private static final String DLX_NAME = "ecs.dlx";

    public static Channel createChannel(Connection connection, LogStream log) throws IOException {
        // Thread currentThread = Thread.currentThread();
        // Channel channel = threadChannels.get(currentThread);
        Channel channel = null;
        if (channel == null || !channel.isOpen()) {
            log.write(LogStream.WARNING_MESSAGE, "Creating new channel for thread: " + Thread.currentThread().getName());
            channel = connection.createChannel();
            channel.addShutdownListener(cause -> {
                if (!cause.isInitiatedByApplication()) {
                    log.write(LogStream.ERROR_MESSAGE, 
                        "Channel was closed unexpectedly: " + cause.getReason());
                    log.writeExceptionStack(cause);
                }
            });
        }
        return channel;
    }

    public static void closeChannel(Channel channel, LogStream log) {
         if (channel != null && channel.isOpen()) {
                try {
                    channel.close();
                } catch (IOException | TimeoutException e) {
                    log.write(LogStream.ERROR_MESSAGE, 
                        "Error while closing initialization channel: " + e.getMessage());
                }
        }
    }
    
    public static void setupQueues(Channel initChannel, 
                                 ConsumerProperties consumerProps,
                                 RabbitMQMetadataLoader queueMetadataLoader,
                                 LogStream log) throws IOException {
        
        // First, declare all dead letter queues
        for (ConsumerProperties.QueueConfig consumerQueue : consumerProps.getQueues()) {
            if (!consumerQueue.isEnabled()) {
                continue;
            }
            
            QueueMetadataEntry queueMetadataEntry = findQueueMetadata(queueMetadataLoader, consumerQueue.getName());
            if (queueMetadataEntry == null) {
                continue;
            }

            setupDeadLetterQueue(initChannel, queueMetadataEntry, log);
        }

        // Then declare normal queues with DLQ configuration
        for (ConsumerProperties.QueueConfig consumerQueue : consumerProps.getQueues()) {
            if (!consumerQueue.isEnabled()) {
                log.write(LogStream.INFORMATION_MESSAGE,
                    "Skipping disabled queue: " + consumerQueue.getName());
                continue;
            }
            
            QueueMetadataEntry queueMetadataEntry = findQueueMetadata(queueMetadataLoader, consumerQueue.getName());
            if (queueMetadataEntry == null) {
                log.write(LogStream.WARNING_MESSAGE,
                    "No metadata found for queue: " + consumerQueue.getName());
                continue;
            }

            setupMainQueue(initChannel, queueMetadataEntry, log);
        }
    }
    
    private static void setupDeadLetterQueue(Channel channel, QueueMetadataEntry queueMetadataEntry, LogStream log) throws IOException {
        String dlqName = queueMetadataEntry.getName() + ".dlq";
        
        channel.exchangeDeclare(DLX_NAME, "direct", true);
        
        Map<String, Object> dlqArguments = new HashMap<>();
        dlqArguments.put("x-queue-type", "quorum");
        if (queueMetadataEntry.getQuorumSize() > 0) {
            dlqArguments.put("x-quorum-initial-group-size", queueMetadataEntry.getQuorumSize());
        }
        
        channel.queueDeclare(dlqName, true, false, false, dlqArguments);
        channel.queueBind(dlqName, DLX_NAME, queueMetadataEntry.getName());
        
        log.write(LogStream.INFORMATION_MESSAGE, 
            "Declared dead letter queue: " + dlqName + " with exchange: " + DLX_NAME);
    }
    
    private static void setupMainQueue(Channel channel, QueueMetadataEntry queueMetadataEntry, LogStream log) throws IOException {
        // Declare exchange
        channel.exchangeDeclare(
            queueMetadataEntry.getExchange(),
            queueMetadataEntry.getExchangeType(),
            queueMetadataEntry.isDurable()
        );
        
        // Prepare queue arguments including DLQ configuration
        Map<String, Object> arguments = new HashMap<>();
        if ("quorum".equals(queueMetadataEntry.getType())) {
            arguments.put("x-queue-type", "quorum");
            if (queueMetadataEntry.getQuorumSize() > 0) {
                arguments.put("x-quorum-initial-group-size", queueMetadataEntry.getQuorumSize());
            }
        }
        arguments.put("x-dead-letter-exchange", DLX_NAME);
        arguments.put("x-dead-letter-routing-key", queueMetadataEntry.getName());
        
        // Declare queue with DLQ configuration
        channel.queueDeclare(
            queueMetadataEntry.getName(),
            queueMetadataEntry.isDurable(),
            queueMetadataEntry.isExclusive(),
            queueMetadataEntry.isAutoDelete(),
            arguments
        );
        
        // Bind queue to exchange
        channel.queueBind(
            queueMetadataEntry.getName(),
            queueMetadataEntry.getExchange(),
            queueMetadataEntry.getRoutingKey()
        );
        
        log.write(LogStream.INFORMATION_MESSAGE, 
            "Declared queue: " + queueMetadataEntry.getName() + 
            " with exchange: " + queueMetadataEntry.getExchange());
    }
    
    private static QueueMetadataEntry findQueueMetadata(RabbitMQMetadataLoader loader, String queueName) {
        return loader.getEntries().stream()
            .filter(entry -> entry.getName().equals(queueName))
            .findFirst()
            .orElse(null);
    }
}
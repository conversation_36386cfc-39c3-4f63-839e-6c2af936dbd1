package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSCLUSTER
 */
public class TDSCLUSTER  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private Integer spindle;
    private String channelId;
    private Short clusterId;
    private String clusterType;
    private String clusterSize;
    private String surface;
    private String subType;
    private Double rlenUm;
    private Double clenUm;
    private Double midRadiusMm;
    private Double midAngleRad;
    private Double radius;
    private String surfaceTds;
    private String kind;
    private Double angle;
    private Double width;
    private Double length;
    private Integer max;
    private Integer min;
    private Integer aMax;
    private Integer rms;
    private Double advd;
    private Integer totalarea;
    private String oriChannel;
    private Double maxamp;
    private Double minamp;

    // Default constructor
    public TDSCLUSTER() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Short getClusterId() {
        return clusterId;
    }

    public void setClusterId(Short clusterId) {
        this.clusterId = clusterId;
    }

    public String getClusterType() {
        return clusterType;
    }

    public void setClusterType(String clusterType) {
        this.clusterType = clusterType;
    }

    public String getClusterSize() {
        return clusterSize;
    }

    public void setClusterSize(String clusterSize) {
        this.clusterSize = clusterSize;
    }

    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    public String getSubType() {
        return subType;
    }

    public void setSubType(String subType) {
        this.subType = subType;
    }

    public Double getRlenUm() {
        return rlenUm;
    }

    public void setRlenUm(Double rlenUm) {
        this.rlenUm = rlenUm;
    }

    public Double getClenUm() {
        return clenUm;
    }

    public void setClenUm(Double clenUm) {
        this.clenUm = clenUm;
    }

    public Double getMidRadiusMm() {
        return midRadiusMm;
    }

    public void setMidRadiusMm(Double midRadiusMm) {
        this.midRadiusMm = midRadiusMm;
    }

    public Double getMidAngleRad() {
        return midAngleRad;
    }

    public void setMidAngleRad(Double midAngleRad) {
        this.midAngleRad = midAngleRad;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    public String getSurfaceTds() {
        return surfaceTds;
    }

    public void setSurfaceTds(String surfaceTds) {
        this.surfaceTds = surfaceTds;
    }

    public String getKind() {
        return kind;
    }

    public void setKind(String kind) {
        this.kind = kind;
    }

    public Double getAngle() {
        return angle;
    }

    public void setAngle(Double angle) {
        this.angle = angle;
    }

    public Double getWidth() {
        return width;
    }

    public void setWidth(Double width) {
        this.width = width;
    }

    public Double getLength() {
        return length;
    }

    public void setLength(Double length) {
        this.length = length;
    }

    public Integer getMax() {
        return max;
    }

    public void setMax(Integer max) {
        this.max = max;
    }

    public Integer getMin() {
        return min;
    }

    public void setMin(Integer min) {
        this.min = min;
    }

    public Integer getAMax() {
        return aMax;
    }

    public void setAMax(Integer aMax) {
        this.aMax = aMax;
    }

    public Integer getRms() {
        return rms;
    }

    public void setRms(Integer rms) {
        this.rms = rms;
    }

    public Double getAdvd() {
        return advd;
    }

    public void setAdvd(Double advd) {
        this.advd = advd;
    }

    public Integer getTotalarea() {
        return totalarea;
    }

    public void setTotalarea(Integer totalarea) {
        this.totalarea = totalarea;
    }

    public String getOriChannel() {
        return oriChannel;
    }

    public void setOriChannel(String oriChannel) {
        this.oriChannel = oriChannel;
    }

    public Double getMaxamp() {
        return maxamp;
    }

    public void setMaxamp(Double maxamp) {
        this.maxamp = maxamp;
    }

    public Double getMinamp() {
        return minamp;
    }

    public void setMinamp(Double minamp) {
        this.minamp = minamp;
    }

}



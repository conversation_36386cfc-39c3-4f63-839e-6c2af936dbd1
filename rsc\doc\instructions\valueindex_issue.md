## 
### 流程介绍：
1. 流程1：在生产者程序中，一个二进制的数据文件需要由BinaryFileProcessor来处理，这个processor会将二进制文件解析成一个rabbit mq message数据，然后发送到rabbit mq的queue中。
2. 流程2：有一个Rabbitmq consumer来消费这个queue中的数据，consumer会将数据解析成一个java对象，然后插入到数据库中。
---
在流程1中，解析的时候会去根据profile中每一行的column来解析数据。
#### 以下我给出一个25_GEOMRSLT.pro文件为例：
* Table: DB2SYS.GEOMRSLT(BINARY format)
*| ColumnName       | DBColType   | DBColLen | SysType         | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
| SPINDLE          | SmallInt    | 2        | System.Int16    | 2         | 0     | Y     | 0        | 0        | Y      |
| GLIDENUM         | Integer     | 4        | System.Int32    | 4         | 1     | Y     | 8        | 1        | Y      |
| BANDNUM          | SmallInt    | 2        | System.Int16    | 2         | 8     | Y     | 16       | 8        | Y      |
| TOHVEL_TOP       | Double      | 8        | System.Double   | 8         | 2     |       | 24       | 2        |        |
| TOHVEL_BOT       | Double      | 8        | System.Double   | 8         | 3     |       | 40       | 3        |        |
| TOHNM_TOP        | Double      | 8        | System.Double   | 8         | 4     |       | 56       | 4        |        |
| TOHNM_BOT        | Double      | 8        | System.Double   | 8         | 5     |       | 72       | 5        |        |
| DTINSERT         | Timestamp   | 4        | System.DateTime | 4         | 6     |       | 0        | 6        |        |
| CORRELID         | Integer     | 4        | System.Int32    | 4         | 7     |       | 0        | 7        |        |

#### 在BinaryFileProcessor中，会按照第一行开始，根据startidx,binlength来解析数据。然后变成一个字符串。
解析完的字符串示例如下：2025-09-10-13.57.43.000434 GEOMRSLT_25 146965268 9 */21122/5294812/1/0.0/12.94/0.0/10500.0//146965268
从*/开始，以/作为分隔符，每一个就是对应一个column的值。

在流程2中，解析的时候会去根据profile的valueindex来解析数据。MessageDataMapper中的mapToPojo方法，

### valueindex的用意如下：
数据文件的顺序与数据库中的映射顺序可能是不一样的，所以需要通过valueindex来指定数据文件中的顺序与数据库中的顺序的对应关系。

### 我在运行程序的过程中，应该映射顺序出问题了，问题日志如下：
~0 14:35:42 Sep 10, 2025 Exception java.lang.RuntimeException occurred, message = Failed to map data to POJO
java.lang.RuntimeException: Failed to map data to POJO
	at wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper.mapToPojo(MessageDataMapper.java:153)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.processDeliveryListOneByOne(BatchMessageProcessor.java:124)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.handleBatchFailure(BatchMessageProcessor.java:414)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.processBatchDeliveriesBulkTransaction(BatchMessageProcessor.java:330)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.lambda$startConsuming$0(BatchMessageProcessor.java:496)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.NumberFormatException: Value out of range. Value:"146965268" Radix:10
	at java.base/java.lang.Short.parseShort(Short.java:140)
	at java.base/java.lang.Short.parseShort(Short.java:164)
	at wdc.disk.ecs.apps.MQUpload.util.DataTypeMapper.convertValue(DataTypeMapper.java:76)
	at wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper.mapToPojo(MessageDataMapper.java:128)
	... 5 more
~3 14:35:42 Sep 10, 2025 Batch processing completed for GEOMRSLT_25. Total time: 4ms, Processed: 0, Failed: 1
~3 14:35:42 Sep 10, 2025 Bulk transaction processing completed in 9ms. Processed: 0, Failed: 1
~3 14:35:42 Sep 10, 2025 Processed batch of 1 messages. Total processed: 9, Queue size: 0
~3 14:35:42 Sep 10, 2025 currentSize: 1

---

### 问题诊断与解决方案

#### 1. 问题诊断

根据错误日志 `java.lang.NumberFormatException: Value out of range. Value:"146965268"`，根本原因是程序试图将一个超出范围的巨大数值 `146965268` 转换成 `Short` 类型。

通过分析代码和配置文件，定位到问题根源在于生产者和消费者对数据字段顺序的约定不一致：

- **生产者 (`BinaryFileProcessor`)**：在生成RabbitMQ消息时，它按照列在 `.pro` 配置文件中的**物理行顺序**来拼接字段，生成以 `/` 分隔的字符串。
- **消费者 (`MessageDataMapper`)**：在解析消息时，它严格按照 `.pro` 配置文件中 `ValueIdx` 字段定义的**逻辑顺序**来映射字段值到Java对象。

当 `.pro` 文件中的物理行顺序与 `ValueIdx` 的逻辑顺序不一致时，就会发生字段映射错位。在本次问题中，一个本应是 `CORRELID` (Integer类型) 的巨大数值 `146965268`，被错误地映射到了 `BANDNUM` (SmallInt/Short类型) 字段上，从而导致了类型转换异常。

#### 2. 解决方案

为了一劳永逸地解决此类问题，并避免依赖于维护配置文件的物理顺序，我们采取了修改生产者代码的方案。

**修改点**：
- **文件**: `src/main/java/wdc/disk/ecs/apps/MQUpload/processor/BinaryFileProcessor.java`
- **方法**: `parseMsg(String msg, String correlid, TableConfigParser.TableDefinition tableDef)`

**修改内容**：

在该方法中，从 `tableDef` 获取列定义列表后，增加了一行代码，利用 `java.util.Comparator` 对该列表进行排序，确保其顺序与 `ValueIdx` 的逻辑顺序一致。

```java
// ...
List<TableConfigParser.ColumnDefinition> columns = tableDef.getColumns();
// 在拼接字符串前，根据 ValueIdx 对列定义进行排序
columns.sort(java.util.Comparator.comparingInt(TableConfigParser.ColumnDefinition::getValueIdx));

for (TableConfigParser.ColumnDefinition column : columns) {
    // ... 后续拼接逻辑不变
}
// ...
```

#### 3. 结论

通过在生产者端强制实现对 `ValueIdx` 的排序，确保了其输出的消息字符串字段顺序严格遵守了 `ValueIdx` 的约定。这使得消费者端的解析逻辑可以正确工作，并且完全消除了对 `.pro` 配置文件物理行顺序的依赖，使系统更加健壮和易于维护。
package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.MISCDATA_24;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for MISCDATA_24
 */
public interface MISCDATA_24_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.MISCDATA AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{glidenum},",
        "    #{datatype},",
        "    #{data1},",
        "    #{data2},",
        "    #{data3},",
        "    #{data4},",
        "    #{data5},",
        "    #{data6},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    DATATYPE,",
        "    DATA1,",
        "    DATA2,",
        "    DATA3,",
        "    DATA4,",
        "    DATA5,",
        "    DATA6,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.GLIDENUM = SOURCE.GLIDENUM AND TARGET.DATATYPE = SOURCE.DATATYPE",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DATA1 = SOURCE.DATA1,",
        "    TARGET.DATA2 = SOURCE.DATA2,",
        "    TARGET.DATA3 = SOURCE.DATA3,",
        "    TARGET.DATA4 = SOURCE.DATA4,",
        "    TARGET.DATA5 = SOURCE.DATA5,",
        "    TARGET.DATA6 = SOURCE.DATA6,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    DATATYPE,",
        "    DATA1,",
        "    DATA2,",
        "    DATA3,",
        "    DATA4,",
        "    DATA5,",
        "    DATA6,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.GLIDENUM,",
        "    SOURCE.DATATYPE,",
        "    SOURCE.DATA1,",
        "    SOURCE.DATA2,",
        "    SOURCE.DATA3,",
        "    SOURCE.DATA4,",
        "    SOURCE.DATA5,",
        "    SOURCE.DATA6,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(MISCDATA_24 record);
}











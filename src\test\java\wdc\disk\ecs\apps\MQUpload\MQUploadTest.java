//package wdc.disk.ecs.apps.MQUpload;
//
//import static org.junit.jupiter.api.Assertions.*;
//import static org.mockito.Mockito.*;
//
//import java.util.concurrent.ScheduledExecutorService;
//import java.util.concurrent.TimeUnit;
//
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.Mock;
//import org.mockito.junit.jupiter.MockitoExtension;
//
//import ibm.disk.utility.LogStream;
//import wdc.disk.ecs.apps.MQUpload.model.ConfigurationProperties;
//import wdc.disk.ecs.apps.MQUpload.processor.FileProcessingTemplate;
//import wdc.disk.ecs.apps.MQUpload.producer.MQUpload;
//import wdc.disk.ecs.apps.MQUpload.service.FileOperationService;
//import wdc.disk.ecs.apps.MQUpload.service.RabbitMQProducerService;
//
//@ExtendWith(MockitoExtension.class)
//public class MQUploadTest {
//
//    @Mock
//    private LogStream mockLog;
//    @Mock
//    private ConfigurationProperties mockConfig;
//    @Mock
//    private FileOperationService mockFileMonitorService;
//    @Mock
//    private RabbitMQProducerService mockRabbitMQService;
//    @Mock
//    private FileProcessingTemplate mockFileProcessingTemplate;
//    @Mock
//    private ScheduledExecutorService mockScheduler;
//
//    private MQUpload mqUpload;
//
//    @BeforeEach
//    void setUp() {
//        mqUpload = new MQUpload("testProfile", "testBlock", mockLog);
//        mqUpload.setConfig(mockConfig);
//        mqUpload.setFileMonitorService(mockFileMonitorService);
//        mqUpload.setRabbitMQService(mockRabbitMQService);
//        mqUpload.setFileProcessingTemplate(mockFileProcessingTemplate);
//        mqUpload.setScheduler(mockScheduler);
//    }
//
//    @Test
//    void testStart_WhenNotRunning() {
//        // Arrange
//        when(mockConfig.getMonitorInterval()).thenReturn(5);
//        when(mockConfig.getPdsDir()).thenReturn("testDir");
//
//        // Act
//        mqUpload.start();
//
//        // Assert
//        assertTrue(mqUpload.isRunning());
//        verify(mockScheduler).scheduleAtFixedRate(mqUpload, 0, 5, TimeUnit.SECONDS);
//        verify(mockLog).write(eq(LogStream.INFORMATION_MESSAGE), contains("MQUpload started"));
//    }
//
//    @Test
//    void testStart_WhenAlreadyRunning() {
//        // Arrange
//        mqUpload.start(); // First start
//        clearInvocations(mockScheduler, mockLog);
//
//        // Act
//        mqUpload.start(); // Second start
//
//        // Assert
//        verify(mockLog).write(eq(LogStream.WARNING_MESSAGE), contains("already running"));
//        verifyNoInteractions(mockScheduler);
//    }
//
//    @Test
//    void testStop_WhenRunning() throws InterruptedException {
//        // Arrange
//        mqUpload.start();
//        when(mockScheduler.awaitTermination(60, TimeUnit.SECONDS)).thenReturn(true);
//
//        // Act
//        mqUpload.stop();
//
//        // Assert
//        assertFalse(mqUpload.isRunning());
//        verify(mockScheduler).shutdown();
//        verify(mockScheduler).awaitTermination(60, TimeUnit.SECONDS);
//        verify(mockRabbitMQService).close();
//        verify(mockLog).write(eq(LogStream.INFORMATION_MESSAGE), contains("MQUpload stopped"));
//    }
//
//    @Test
//    void testStop_WhenNotRunning() {
//        // Act
//        mqUpload.stop();
//
//        // Assert
//        verifyNoInteractions(mockScheduler, mockRabbitMQService);
//    }
//
//    @Test
//    void testRun_WhenRunning() throws Exception {
//        // Arrange
//        mqUpload.start();
//
//        // Act
//        mqUpload.run();
//
//        // Assert
//        verify(mockFileProcessingTemplate).processAndUploadFiles();
//    }
//
//    @Test
//    void testRun_WhenNotRunning() {
//        // Act
//        mqUpload.run();
//
//        // Assert
//        verifyNoInteractions(mockFileProcessingTemplate);
//    }
//
//    @Test
//    void testRun_WithException() throws Exception {
//        // Arrange
//        mqUpload.start();
//        doThrow(new RuntimeException("Test error")).when(mockFileProcessingTemplate).processAndUploadFiles();
//
//        // Act
//        mqUpload.run();
//
//        // Assert
//        verify(mockLog).write(eq(LogStream.ERROR_MESSAGE), contains("Error in run cycle"));
//    }
//
//    @Test
//    void testStop_WithInterruptedException() throws InterruptedException {
//        // Arrange
//        mqUpload.start();
//        when(mockScheduler.awaitTermination(60, TimeUnit.SECONDS))
//            .thenThrow(new InterruptedException("Test interruption"));
//
//        // Act
//        mqUpload.stop();
//
//        // Assert
//        verify(mockLog).write(eq(LogStream.ERROR_MESSAGE), contains("Error while stopping"));
//        verify(mockRabbitMQService).close();
//        assertFalse(mqUpload.isRunning());
//    }
//}
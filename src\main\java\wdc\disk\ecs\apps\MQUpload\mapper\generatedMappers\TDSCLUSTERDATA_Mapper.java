package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSCLUSTERDATA;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSCLUSTERDATA
 */
public interface TDSCLUSTERDATA_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_CLUSTER_DATA AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{channelId},",
        "    #{clusterId},",
        "    #{radiusMm},",
        "    #{angleRad}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    CLUSTER_ID,",
        "    RADIUS_MM,",
        "    ANGLE_RAD",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CHANNEL_ID = SOURCE.CHANNEL_ID AND TARGET.CLUSTER_ID = SOURCE.CLUSTER_ID AND TARGET.RADIUS_MM = SOURCE.RADIUS_MM AND TARGET.ANGLE_RAD = SOURCE.ANGLE_RAD",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    CLUSTER_ID,",
        "    RADIUS_MM,",
        "    ANGLE_RAD",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CHANNEL_ID,",
        "    SOURCE.CLUSTER_ID,",
        "    SOURCE.RADIUS_MM,",
        "    SOURCE.ANGLE_RAD",
        "  )"
    })
    void insertOrUpdate(TDSCLUSTERDATA record);
}











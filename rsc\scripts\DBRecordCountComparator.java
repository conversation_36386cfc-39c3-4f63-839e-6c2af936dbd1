package wdc.disk.ecs.apps.dbcompare;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Properties;

public class DBRecordCountComparator {
    private String prodUrl;
    private String prodUser;
    private String prodPassword;
    private String testUrl;
    private String testUser;
    private String testPassword;
    
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    public DBRecordCountComparator(String configFile) throws IOException {
        loadConfig(configFile);
    }
    
    private void loadConfig(String configFile) throws IOException {
        Properties props = new Properties();
        File file = new File(configFile);
        if (!file.exists()) {
            throw new IOException("Configuration file not found: " + configFile);
        }
        try (FileInputStream fis = new FileInputStream(file)) {
            props.load(fis);
        } catch (IOException e) {
            throw new IOException("Error loading configuration file: " + e.getMessage(), e);
        }
        
        prodUrl = props.getProperty("prod.db.url");
        prodUser = props.getProperty("prod.db.user");
        prodPassword = props.getProperty("prod.db.password");
        testUrl = props.getProperty("test.db.url");
        testUser = props.getProperty("test.db.user");
        testPassword = props.getProperty("test.db.password");
    }
    
    public Connection getProdConnection() throws SQLException {
        return DriverManager.getConnection(prodUrl, prodUser, prodPassword);
    }
    
    public Connection getTestConnection() throws SQLException {
        return DriverManager.getConnection(testUrl, testUser, testPassword);
    }
    
    public void compareTableCounts(List<TableComparisonConfig> tables, int hoursBack) {
        System.out.println("=== DB Record Count Comparison Report ===");
        System.out.println("Comparing records from the past " + hoursBack + " hours");
        System.out.println("Time: " + DATE_FORMAT.format(new Date()));
        System.out.println("==========================================");
        System.out.printf("%-30s | %-10s | %-10s | %-10s | %-10s%n", 
                "Table Name", "Prod Count", "Test Count", "Difference", "Match?");
        System.out.println("-----------------------------------------------------------------------------------");
        
        for (TableComparisonConfig table : tables) {
            try {
                long prodCount = getRecordCount(getProdConnection(), table, hoursBack);
                long testCount = getRecordCount(getTestConnection(), table, hoursBack);
                long diff = prodCount - testCount;
                boolean match = (diff == 0);
                
                System.out.printf("%-30s | %-10d | %-10d | %-10d | %-10s%n", 
                        table.getTableName(), prodCount, testCount, diff, match ? "YES" : "NO");
                
            } catch (SQLException e) {
                System.out.printf("%-30s | %-10s | %-10s | %-10s | %-10s%n", 
                        table.getTableName(), "ERROR", "ERROR", "N/A", "NO");
                System.out.println("Error: " + e.getMessage());
            }
        }
    }
    
    private long getRecordCount(Connection conn, TableComparisonConfig table, int hoursBack) throws SQLException {
        String whereClause = "";
        if (table.getDateColumn() != null && !table.getDateColumn().isEmpty()) {
            whereClause = " WHERE " + table.getDateColumn() + " >= CURRENT TIMESTAMP - " + hoursBack + " HOURS";
        }
        
        if (table.getAdditionalCondition() != null && !table.getAdditionalCondition().isEmpty()) {
            whereClause += whereClause.isEmpty() ? " WHERE " : " AND ";
            whereClause += table.getAdditionalCondition();
        }
        
        String sql = "SELECT COUNT(*) FROM " + table.getTableName() + whereClause;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        }
    }
    
    /**
     * Compares record counts hour by hour for the specified period, organized by table
     * @param tables List of tables to compare
     * @param hoursBack Number of hours to look back
     * @return List of hourly comparison results
     */
    public List<HourlyComparisonResult> compareTableCountsHourByHour(List<TableComparisonConfig> tables, int hoursBack) {
        List<HourlyComparisonResult> results = new ArrayList<>();
        
        System.out.println("=== DB Record Count Hourly Comparison Report ===");
        System.out.println("Comparing records from the past " + hoursBack + " hours");
        System.out.println("Time: " + DATE_FORMAT.format(new Date()));
        System.out.println("=================================================");
        
        // First collect all results
        for (int hour = 1; hour <= hoursBack; hour++) {
            HourlyComparisonResult hourResult = new HourlyComparisonResult(hour);
            
            for (TableComparisonConfig table : tables) {
                try {
                    long prodCount = getHourlyRecordCount(getProdConnection(), table, hour);
                    long testCount = getHourlyRecordCount(getTestConnection(), table, hour);
                    long diff = prodCount - testCount;
                    boolean match = (diff == 0);
                    
                    TableComparisonResult tableResult = new TableComparisonResult(
                            table.getTableName(), prodCount, testCount, diff, match);
                    hourResult.addTableResult(tableResult);
                    
                } catch (SQLException e) {
                    TableComparisonResult tableResult = new TableComparisonResult(
                            table.getTableName(), -1, -1, 0, false);
                    tableResult.setErrorMessage(e.getMessage());
                    hourResult.addTableResult(tableResult);
                }
            }
            
            results.add(hourResult);
        }
        
        // Then display results organized by table
        for (TableComparisonConfig table : tables) {
            System.out.println("\nTable: " + table.getTableName());
            System.out.printf("%-15s | %-10s | %-10s | %-10s | %-10s%n", 
                    "Hour", "Prod Count", "Test Count", "Difference", "Match?");
            System.out.println("-----------------------------------------------------------------------------------");
            
            for (int i = 0; i < results.size(); i++) {
                HourlyComparisonResult hourResult = results.get(i);
                int hour = hourResult.getHour();
                
                // Find the result for this table in this hour
                for (TableComparisonResult tableResult : hourResult.getTableResults()) {
                    if (tableResult.getTableName().equals(table.getTableName())) {
                        if (tableResult.getErrorMessage() != null) {
                            System.out.printf("%-15s | %-10s | %-10s | %-10s | %-10s%n", 
                                    "Hour -" + hour, "ERROR", "ERROR", "N/A", "NO");
                            System.out.println("Error: " + tableResult.getErrorMessage());
                        } else {
                            System.out.printf("%-15s | %-10d | %-10d | %-10d | %-10s%n", 
                                    "Hour -" + hour, 
                                    tableResult.getProdCount(), 
                                    tableResult.getTestCount(), 
                                    tableResult.getDifference(), 
                                    tableResult.isMatch() ? "YES" : "NO");
                        }
                        break;
                    }
                }
            }
        }
        
        return results;
    }
    
    private long getHourlyRecordCount(Connection conn, TableComparisonConfig table, int hour) throws SQLException {
        String whereClause = "";
        if (table.getDateColumn() != null && !table.getDateColumn().isEmpty()) {
            whereClause = " WHERE " + table.getDateColumn() + " >= CURRENT TIMESTAMP - " + hour + " HOURS " +
                          "AND " + table.getDateColumn() + " < CURRENT TIMESTAMP - " + (hour-1) + " HOURS";
        }
        
        if (table.getAdditionalCondition() != null && !table.getAdditionalCondition().isEmpty()) {
            whereClause += whereClause.isEmpty() ? " WHERE " : " AND ";
            whereClause += table.getAdditionalCondition();
        }
        
        String sql = "SELECT COUNT(*) FROM " + table.getTableName() + whereClause;
        
        try (PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            if (rs.next()) {
                return rs.getLong(1);
            }
            return 0;
        }
    }
    
    public static void main(String[] args) {
        try {
            // Default config file path
            String configFile = "src/main/resources/db-compare-config.properties";
            if (args.length > 0) {
                configFile = args[0];
            }
            
            // Default hours back
            int hoursBack = 4;
            if (args.length > 1) {
                hoursBack = Integer.parseInt(args[1]);
            }
            
            DBRecordCountComparator comparator = new DBRecordCountComparator(configFile);
            
            // Load tables to compare from config file
            List<TableComparisonConfig> tables = loadTableConfigurations(configFile);
            
            // Run comparison
            comparator.compareTableCountsHourByHour(tables, hoursBack);
            
        } catch (Exception e) {
            System.err.println("Error: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static List<TableComparisonConfig> loadTableConfigurations(String configFile) throws IOException {
        List<TableComparisonConfig> tables = new ArrayList<>();
        Properties props = new Properties();
        
        try (FileInputStream fis = new FileInputStream(configFile)) {
            props.load(fis);
        }
        
        // Get table count from properties
        int tableCount = Integer.parseInt(props.getProperty("tables.count", "0"));
        
        // Load each table configuration
        for (int i = 1; i <= tableCount; i++) {
            String tableName = props.getProperty("table." + i + ".name");
            String dateColumn = props.getProperty("table." + i + ".dateColumn");
            String condition = props.getProperty("table." + i + ".condition", "");
            
            if (tableName != null && dateColumn != null) {
                tables.add(new TableComparisonConfig(tableName, dateColumn, condition));
            }
        }
        
        return tables;
    }
    
    // Inner class to hold table comparison configuration
    public static class TableComparisonConfig {
        private String tableName;
        private String dateColumn;
        private String additionalCondition;
        
        public TableComparisonConfig(String tableName, String dateColumn, String additionalCondition) {
            this.tableName = tableName;
            this.dateColumn = dateColumn;
            this.additionalCondition = additionalCondition;
        }
        
        public String getTableName() {
            return tableName;
        }
        
        public String getDateColumn() {
            return dateColumn;
        }
        
        public String getAdditionalCondition() {
            return additionalCondition;
        }
    }
    
    // Class to hold hourly comparison results
    public static class HourlyComparisonResult {
        private int hour;
        private List<TableComparisonResult> tableResults = new ArrayList<>();
        
        public HourlyComparisonResult(int hour) {
            this.hour = hour;
        }
        
        public void addTableResult(TableComparisonResult result) {
            tableResults.add(result);
        }
        
        public int getHour() {
            return hour;
        }
        
        public List<TableComparisonResult> getTableResults() {
            return tableResults;
        }
        
        public boolean allTablesMatch() {
            for (TableComparisonResult result : tableResults) {
                if (!result.isMatch()) {
                    return false;
                }
            }
            return true;
        }
    }
    
    // Class to hold table comparison results
    public static class TableComparisonResult {
        private String tableName;
        private long prodCount;
        private long testCount;
        private long difference;
        private boolean match;
        private String errorMessage;
        
        public TableComparisonResult(String tableName, long prodCount, long testCount, 
                                    long difference, boolean match) {
            this.tableName = tableName;
            this.prodCount = prodCount;
            this.testCount = testCount;
            this.difference = difference;
            this.match = match;
        }
        
        public String getTableName() {
            return tableName;
        }
        
        public long getProdCount() {
            return prodCount;
        }
        
        public long getTestCount() {
            return testCount;
        }
        
        public long getDifference() {
            return difference;
        }
        
        public boolean isMatch() {
            return match;
        }
        
        public String getErrorMessage() {
            return errorMessage;
        }
        
        public void setErrorMessage(String errorMessage) {
            this.errorMessage = errorMessage;
        }
    }
}




package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.BANDRSLT_STRING_27;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for BANDRSLT_STRING_27
 */
public interface BANDRSLT_STRING_27_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.BANDRSLT_STRING AS TARGET",
        "USING (VALUES (",
        "    #{lot},",
        "    #{unitid},",
        "    #{spindle},",
        "    #{testnum},",
        "    #{results},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    LOT,",
        "    UNITID,",
        "    SPINDLE,",
        "    TESTNUM,",
        "    RESULTS,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TESTNUM = SOURCE.TESTNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.LOT = SOURCE.LOT,",
        "    TARGET.UNITID = SOURCE.UNITID,",
        "    TARGET.RESULTS = SOURCE.RESULTS,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    LOT,",
        "    UNITID,",
        "    SPINDLE,",
        "    TESTNUM,",
        "    RESULTS,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.LOT,",
        "    SOURCE.UNITID,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.RESULTS,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(BANDRSLT_STRING_27 record);
}











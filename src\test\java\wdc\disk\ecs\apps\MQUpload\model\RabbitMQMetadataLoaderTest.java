package wdc.disk.ecs.apps.MQUpload.model;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import java.util.ArrayList;
import java.util.List;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

class RabbitMQMetadataLoaderTest {

    @Test
    void testMockRabbitMQMetadataLoader() {
        // Create mock
        RabbitMQMetadataLoader mockedLoader = Mockito.mock(RabbitMQMetadataLoader.class);
        
        // Create sample test data
        List<QueueMetadataEntry> testEntries = new ArrayList<>();
        QueueMetadataEntry entry = new QueueMetadataEntry();
        entry.setName("test-queue");
        entry.setType("classic");
        entry.setExchange("test-exchange");
        entry.setExchangeType("direct");
        entry.setRoutingKey("test.key");
        entry.setDurable(true);
        entry.setExclusive(false);
        entry.setAutoDelete(false);
        entry.setQuorumSize(3);
        testEntries.add(entry);

        // Configure mock behavior
        when(mockedLoader.getEntries()).thenReturn(testEntries);

        // Verify mock
        List<QueueMetadataEntry> result = mockedLoader.getEntries();
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("test-queue", result.get(0).getName());
    }

    @Test
    void testMockLoadFromCsv() throws Exception {
        // Create sample test data
        RabbitMQMetadataLoader loader = Mockito.mock(RabbitMQMetadataLoader.class);
        List<QueueMetadataEntry> testEntries = new ArrayList<>();
        QueueMetadataEntry entry = new QueueMetadataEntry();
        entry.setName("test-queue");
        testEntries.add(entry);

        // Mock static method using Mockito
        try (MockedStatic<RabbitMQMetadataLoader> mockedStatic = Mockito.mockStatic(RabbitMQMetadataLoader.class)) {
            mockedStatic.when(() -> RabbitMQMetadataLoader.loadFromCsv(Mockito.anyString()))
                    .thenReturn(loader);
            when(loader.getEntries()).thenReturn(testEntries);

            // Test the mock
            RabbitMQMetadataLoader result = RabbitMQMetadataLoader.loadFromCsv("dummy.csv");
            assertEquals(1, result.getEntries().size());
            assertEquals("test-queue", result.getEntries().get(0).getName());
        }
    }
}
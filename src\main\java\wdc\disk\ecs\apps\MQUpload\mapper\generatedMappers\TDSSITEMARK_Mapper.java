package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSSITEMARK;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSSITEMARK
 */
public interface TDSSITEMARK_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_SITEMARK_CLUS AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{channelId},",
        "    #{clusterId},",
        "    #{midRadiusMm},",
        "    #{midAngleDeg},",
        "    #{type}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    CLUSTER_ID,",
        "    MID_RADIUS_MM,",
        "    MID_ANGLE_DEG,",
        "    TYPE",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CHANNEL_ID = SOURCE.CHANNEL_ID AND TARGET.CLUSTER_ID = SOURCE.CLUSTER_ID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.MID_RADIUS_MM = SOURCE.MID_RADIUS_MM,",
        "    TARGET.MID_ANGLE_DEG = SOURCE.MID_ANGLE_DEG,",
        "    TARGET.TYPE = SOURCE.TYPE",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    CLUSTER_ID,",
        "    MID_RADIUS_MM,",
        "    MID_ANGLE_DEG,",
        "    TYPE",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CHANNEL_ID,",
        "    SOURCE.CLUSTER_ID,",
        "    SOURCE.MID_RADIUS_MM,",
        "    SOURCE.MID_ANGLE_DEG,",
        "    SOURCE.TYPE",
        "  )"
    })
    void insertOrUpdate(TDSSITEMARK record);
}











package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSENG;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSENG
 */
public interface TDSENG_Mapper {
    
    @Insert({
        "INSERT INTO DB2SYS.TDS_Eng (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    RAWDATA_FILE,",
        "    ENG_KEY1,",
        "    ENG_VAL1,",
        "    ENG_KEY2,",
        "    ENG_VAL2,",
        "    ENG_KEY3,",
        "    ENG_VAL3,",
        "    ENG_KEY4,",
        "    ENG_VAL4,",
        "    ENG_KEY5,",
        "    ENG_VAL5",
        ")",
        "VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{rawdataFile},",
        "    #{engKey1},",
        "    #{engVal1},",
        "    #{engKey2},",
        "    #{engVal2},",
        "    #{engKey3},",
        "    #{engVal3},",
        "    #{engKey4},",
        "    #{engVal4},",
        "    #{engKey5},",
        "    #{engVal5}",
        ")"
    })
    void insertOrUpdate(TDSENG record);
}











package wdc.disk.ecs.apps.MQUpload.model;

public class ProducerProperties {
    private String pdsDir;
    private boolean binaryFormat;
    private int maxFilesInSending;
    private long waitingForWriteEnded;
    private String toDir;
    private String dbMsgIdProfile;
	private int monitorInterval = 60; // Default 60 seconds
	private boolean housekeepingEnabled;
	private int housekeepingIntervalHours;
	private int housekeepingRetentionDays;
	private java.util.List<String> housekeepingDirectories;
	
	public String getPdsDir() {
		return pdsDir;
	}
	public void setPdsDir(String pdsDir) {
		this.pdsDir = pdsDir;
	}
	public boolean isBinaryFormat() {
		return binaryFormat;
	}
	public void setBinaryFormat(boolean binaryFormat) {
		this.binaryFormat = binaryFormat;
	}
	public int getMaxFilesInSending() {
		return maxFilesInSending;
	}
	public void setMaxFilesInSending(int maxFilesInSending) {
		this.maxFilesInSending = maxFilesInSending;
	}
	public long getWaitingForWriteEnded() {
		return waitingForWriteEnded;
	}
	public void setWaitingForWriteEnded(long waitingForWriteEnded) {
		this.waitingForWriteEnded = waitingForWriteEnded;
	}
	public String getToDir() {
		return toDir;
	}
	public void setToDir(String toDir) {
		this.toDir = toDir;
	}
	public String getDbMsgIdProfile() {
		return dbMsgIdProfile;
	}
	public void setDbMsgIdProfile(String dbMsgIdProfile) {
		this.dbMsgIdProfile = dbMsgIdProfile;
	}
    
    public int getMonitorInterval() {
        return monitorInterval;
    }

    public void setMonitorInterval(int monitorInterval) {
        this.monitorInterval = monitorInterval;
    }
	public boolean isHousekeepingEnabled() {
		return housekeepingEnabled;
	}
	public void setHousekeepingEnabled(boolean housekeepingEnabled) {
		this.housekeepingEnabled = housekeepingEnabled;
	}
	public int getHousekeepingIntervalHours() {
		return housekeepingIntervalHours;
	}
	public void setHousekeepingIntervalHours(int housekeepingIntervalHours) {
		this.housekeepingIntervalHours = housekeepingIntervalHours;
	}
	public int getHousekeepingRetentionDays() {
		return housekeepingRetentionDays;
	}
	public void setHousekeepingRetentionDays(int housekeepingRetentionDays) {
		this.housekeepingRetentionDays = housekeepingRetentionDays;
	}
	public java.util.List<String> getHousekeepingDirectories() {
		return housekeepingDirectories;
	}
	public void setHousekeepingDirectories(java.util.List<String> housekeepingDirectories) {
		this.housekeepingDirectories = housekeepingDirectories;
	}
    
    
}
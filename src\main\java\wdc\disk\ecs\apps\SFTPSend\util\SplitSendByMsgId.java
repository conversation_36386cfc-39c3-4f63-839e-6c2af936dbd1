package wdc.disk.ecs.apps.SFTPSend.util;

import java.util.Random;

import com.jcraft.jsch.SftpException;

import ibm.disk.utility.LogStream;

public class SplitSendByMsgId implements SendMethod{
	//Make dir at server if not exist
	//Split files if exceeding quantity
	//Rename the folder
	@Override
	public void process(SendPdsToServerService service,String serverDirName,String msgId, int fileIndex, String srcFile, LogStream log) throws SftpException {
		//This mode 2 is used mostly
		log.write("Enter SplitSendByMsgId.");
		String fileSeparater = "/";
		String serverDir_msgId_dir = serverDirName + fileSeparater + msgId;
		String serverDir_msgId_dir_current = serverDirName + fileSeparater + msgId + fileSeparater + msgId + "_current";
//		System.out.println("serverDir_msgId_dir:" + serverDir_msgId_dir_current);
		if(CreateShareFolderMode.uploading == service.getCreateShareFolderMode()) {
			service.makeDirFromChannel(serverDir_msgId_dir);
			service.makeDirFromChannel(serverDir_msgId_dir_current);
			log.write("MakeDirFromChannel completed.");
		}

		int retryCount = 0;
		while(retryCount < 10) {
			try {
				service.getChannel().put(srcFile, serverDir_msgId_dir_current);
				log.write("Put file completed." + srcFile);
				break;
			} catch (SftpException e) {
				e.printStackTrace();
				log.write("Put file failed.Retry :" + retryCount + ".File:" + srcFile);
				log.writeExceptionStack(e);
				retryCount++;
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e1) {
					e1.printStackTrace();
				}
			}			
		}
		if(retryCount == 10) {
			throw new SftpException(1,"Put 10 times failed.File:" +  srcFile);
		}
		
		if(fileIndex % SendPdsToServerService.RenameRate == 0) {
			boolean exceedFileQty = service.compareProfileQtyAndServerFileQty(msgId, serverDir_msgId_dir_current);
			log.write("Exceed file quantity:" + exceedFileQty);
			if(exceedFileQty) {
				Random ran = new Random();
				int seed = ran.nextInt(51);
				if(seed == 50) {
					service.renameMsgCurrentDir(serverDir_msgId_dir,serverDir_msgId_dir_current,fileSeparater, msgId);
					log.write("Rename dir completed.");
					service.makeDirFromChannel(serverDir_msgId_dir_current);
					log.write("Make Current dir completed.");					
				}
			}			
		}
	}

}

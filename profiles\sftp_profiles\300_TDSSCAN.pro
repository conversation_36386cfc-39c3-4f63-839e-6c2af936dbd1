*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  MEAS_TIME             |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |  Y      |  0      |  1      |  
|  RAWDATA_FILE          |  VarChar     |  255    |  System.String         |  255    |  2      |         |  0      |  2      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  DISKSEQUENCE          |  SmallInt    |  2      |  System.Int16          |  2      |  4      |         |  0      |  4      |  
|  SCAN_TIME_MSEC        |  Real        |  4      |  System.Double         |  4      |  5      |         |  0      |  5      |  
|  BIN_NUMBER            |  Real        |  4      |  System.Double         |  4      |  6      |         |  0      |  6      |  
|  VACUMM_PRESSURE       |  Real        |  4      |  System.Double         |  4      |  7      |         |  0      |  7      |  
|  SPINDLE               |  SmallInt    |  2      |  System.Int16          |  2      |  8      |         |  0      |  8      |  
|  MAP_FILENAME          |  VarChar     |  255    |  System.String         |  255    |  9      |         |  0      |  9      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  10     |         |  0      |  10     |  
|  CELL_ID               |  VarChar     |  20     |  System.String         |  20     |  11     |         |  0      |  11     |  
|  FINAL_GRADE           |  VarChar     |  20     |  System.String         |  20     |  12     |         |  0      |  12     |  
|  GLIDE_GRADE           |  VarChar     |  20     |  System.String         |  20     |  13     |         |  0      |  13     |  
|  CERT_GRADE            |  VarChar     |  20     |  System.String         |  20     |  14     |         |  0      |  14     |  
|  RECIPE_FILENAME       |  VarChar     |  255    |  System.String         |  255    |  15     |         |  0      |  15     |  
|  HSA1_ID               |  VarChar     |  255    |  System.String         |  255    |  16     |         |  0      |  16     |  
|  HSA2_ID               |  VarChar     |  255    |  System.String         |  255    |  17     |         |  0      |  17     |  
|  HSA3_ID               |  VarChar     |  255    |  System.String         |  255    |  18     |         |  0      |  18     |  
|  TOTAL_TOSC_COUNT      |  Real        |  4      |  System.Double         |  4      |  19     |         |  0      |  19     |  
|  EVAL_CODE             |  VarChar     |  255    |  System.String         |  255    |  20     |         |  0      |  20     |  
|  HSA1_CYCLE_COUNT      |  Real        |  4      |  System.Double         |  4      |  21     |         |  0      |  21     |  
|  HSA2_CYCLE_COUNT      |  Real        |  4      |  System.Double         |  4      |  22     |         |  0      |  22     |  
|  HSA3_CYCLE_COUNT      |  Real        |  4      |  System.Double         |  4      |  23     |         |  0      |  23     |  
|  TESTER_TYPE           |  VarChar     |  10     |  System.String         |  10     |  24     |         |  0      |  24     |  
|  SOFTWARE_VERSION      |  VarChar     |  25     |  System.String         |  25     |  25     |         |  0      |  25     |  
|  SCAN_START_RADIUS_MM  |  Real        |  4      |  System.Double         |  4      |  26     |         |  0      |  26     |  
|  SCAN_STOP_RADIUS_MM   |  Real        |  4      |  System.Double         |  4      |  27     |         |  0      |  27     |  
|  FORM_FACTOR           |  VarChar     |  10     |  System.String         |  10     |  28     |         |  0      |  28     |  
|  SCAN_OPTION           |  Real        |  4      |  System.Double         |  4      |  29     |         |  0      |  29     |  
|  HBO                   |  Real        |  4      |  System.Double         |  4      |  30     |         |  0      |  30     |  
|  AUTO_SITEMARK_BY_GRADE  |  Char        |  1      |  System.String         |  1      |  31     |         |  0      |  31     |  
|  SUPPLY_CASSETTE       |  VarChar     |  11     |  System.String         |  11     |  32     |         |  0      |  32     |  
|  OUTPUT_CASSETTE       |  VarChar     |  11     |  System.String         |  11     |  33     |         |  0      |  33     |  
|  OUTPUT_LOT            |  VarChar     |  10     |  System.String         |  10     |  34     |         |  0      |  34     |  
|  OUTPUT_SLOT           |  SmallInt    |  2      |  System.Int16          |  2      |  35     |         |  0      |  35     |  
|  UNITID                |  Char        |  12     |  System.String         |  12     |  36     |         |  0      |  36     |  
|  RULE_NUMBER           |  Integer     |  4      |  System.Int32          |  4      |  37     |         |  0      |  37     |  
|  EXPID                 |  Char        |  10     |  System.String         |  10     |  38     |         |  0      |  38     |  
|  INPUT_VARIETY         |  Char        |  14     |  System.String         |  14     |  39     |         |  0      |  39     |  
|  ROBOT_CLASS           |  Char        |  1      |  System.String         |  1      |  40     |         |  0      |  40     |  
|  RETESTED              |  Char        |  1      |  System.String         |  1      |  41     |         |  0      |  41     |  
|  AC_ERASED             |  Char        |  1      |  System.String         |  1      |  42     |         |  0      |  42     |  
|  CERTIFY_SCANNED       |  Char        |  1      |  System.String         |  1      |  43     |         |  0      |  43     |  
|  TDS_SCANNED           |  Char        |  1      |  System.String         |  1      |  44     |         |  0      |  44     |  
|  TDES_DFA_ACTIVE       |  Char        |  1      |  System.String         |  1      |  45     |         |  0      |  45     |  
|  TDES_DFA_MARKED       |  Char        |  1      |  System.String         |  1      |  46     |         |  0      |  46     |  
|  TEST_SEQUENCE         |  Char        |  2      |  System.String         |  2      |  47     |         |  0      |  47     |  
|  FINALGRADE_PASS       |  VarChar     |  10     |  System.String         |  10     |  48     |         |  0      |  48     |  
|  TOPGRADE_PASS         |  VarChar     |  10     |  System.String         |  10     |  49     |         |  0      |  49     |  
|  BOTGRADE_PASS         |  VarChar     |  10     |  System.String         |  10     |  50     |         |  0      |  50     |  
|  TOPGRADE              |  VarChar     |  20     |  System.String         |  20     |  51     |         |  0      |  51     |  
|  BOTGRADE              |  VarChar     |  20     |  System.String         |  20     |  52     |         |  0      |  52     |  
|  CHECKSUM_GENERAL      |  Double      |  8      |  System.Double         |  8      |  53     |         |  0      |  53     |  
|  CHECKSUM_TDS          |  Double      |  8      |  System.Double         |  8      |  54     |         |  0      |  54     |  
|  CHECKSUM_CERT         |  Double      |  8      |  System.Double         |  8      |  55     |         |  0      |  55     |  
|  CHECKSUM_GRADING      |  Double      |  8      |  System.Double         |  8      |  56     |         |  0      |  56     |  

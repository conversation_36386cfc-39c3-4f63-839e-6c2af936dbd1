*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  3      |         |  0      |  3      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  4      |         |  0      |  4      |  
|  PLC_VERSION           |  Char        |  8      |  System.String         |  8      |  5      |         |  0      |  5      |  
|  ITANK1_TEMP           |  Decimal     |  15     |  System.Decimal        |  15     |  6      |         |  0      |  6      |  
|  ITANK1_USPOWER        |  SmallInt    |  2      |  System.Int16          |  2      |  7      |         |  0      |  7      |  
|  ITANK1_SOAKTIME       |  SmallInt    |  2      |  System.Int16          |  2      |  8      |         |  0      |  8      |  
|  ITANK1_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  9      |         |  0      |  9      |  
|  ITANK2_TEMP           |  Decimal     |  15     |  System.Decimal        |  15     |  10     |         |  0      |  10     |  
|  ITANK2_USPOWER        |  SmallInt    |  2      |  System.Int16          |  2      |  11     |         |  0      |  11     |  
|  ITANK2_SOAKTIME       |  SmallInt    |  2      |  System.Int16          |  2      |  12     |         |  0      |  12     |  
|  ITANK2_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  13     |         |  0      |  13     |  
|  ITANK3_TEMP           |  Decimal     |  15     |  System.Decimal        |  15     |  14     |         |  0      |  14     |  
|  ITANK3_USPOWER        |  SmallInt    |  2      |  System.Int16          |  2      |  15     |         |  0      |  15     |  
|  ITANK3_SOAKTIME       |  SmallInt    |  2      |  System.Int16          |  2      |  16     |         |  0      |  16     |  
|  ITANK3_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  17     |         |  0      |  17     |  
|  ITANK_PH              |  Decimal     |  15     |  System.Decimal        |  15     |  18     |         |  0      |  18     |  
|  ITANK_UV              |  Decimal     |  15     |  System.Decimal        |  15     |  19     |         |  0      |  19     |  
|  SCRUB1_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  20     |         |  0      |  20     |  
|  SCRUB2_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  21     |         |  0      |  21     |  
|  CTANK1_SOAKTIME       |  SmallInt    |  2      |  System.Int16          |  2      |  22     |         |  0      |  22     |  
|  CTANK1_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  23     |         |  0      |  23     |  
|  CTANK2_TIME           |  SmallInt    |  2      |  System.Int16          |  2      |  24     |         |  0      |  24     |  
|  CTANK2_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  25     |         |  0      |  25     |  
|  CTANK3_SOAKTIME       |  SmallInt    |  2      |  System.Int16          |  2      |  26     |         |  0      |  26     |  
|  CTANK3_TOTALTIME      |  SmallInt    |  2      |  System.Int16          |  2      |  27     |         |  0      |  27     |  
|  DRYER_BLANKETTEMP     |  Decimal     |  15     |  System.Decimal        |  15     |  28     |         |  0      |  28     |  
|  DRYER_N2TEMP          |  Decimal     |  15     |  System.Decimal        |  15     |  29     |         |  0      |  29     |  
|  DRYER_TOTALTIME       |  SmallInt    |  2      |  System.Int16          |  2      |  30     |         |  0      |  30     |  
|  RAIL                  |  Char        |  1      |  System.String         |  1      |  31     |         |  0      |  31     |  
|  DRYER                 |  Char        |  1      |  System.String         |  1      |  32     |         |  0      |  32     |  
|  POWERUPCOUNTER        |  SmallInt    |  2      |  System.Int16          |  2      |  33     |         |  0      |  33     |  
|  CTANK1_USPOWER        |  SmallInt    |  2      |  System.Int16          |  2      |  34     |         |  0      |  34     |  
|  CTANK2_USPOWER        |  SmallInt    |  2      |  System.Int16          |  2      |  35     |         |  0      |  35     |  
|  CTANK3_USPOWER        |  SmallInt    |  2      |  System.Int16          |  2      |  36     |         |  0      |  36     |  
|  DI_FILTER_HOURS       |  SmallInt    |  2      |  System.Int16          |  2      |  37     |         |  0      |  37     |  
|  IT_FILTER_HOURS       |  SmallInt    |  2      |  System.Int16          |  2      |  38     |         |  0      |  38     |  
|  ST_FILTER_HOURS       |  SmallInt    |  2      |  System.Int16          |  2      |  39     |         |  0      |  39     |  
|  CASC_FILTER_HOURS     |  SmallInt    |  2      |  System.Int16          |  2      |  40     |         |  0      |  40     |  
|  SCRUB1_PAD_COUNTER    |  Integer     |  4      |  System.Int32          |  4      |  41     |         |  0      |  41     |  
|  SCRUB2_PAD_COUNTER    |  Integer     |  4      |  System.Int32          |  4      |  42     |         |  0      |  42     |  
|  PASSIVATION_HOURS     |  SmallInt    |  2      |  System.Int16          |  2      |  43     |         |  0      |  43     |  
|  PEROXIDE_HOURS        |  SmallInt    |  2      |  System.Int16          |  2      |  44     |         |  0      |  44     |  
|  DRAIN_AND_FILL_HOURS  |  SmallInt    |  2      |  System.Int16          |  2      |  45     |         |  0      |  45     |  
|  IN_LINE_IT_PH         |  Decimal     |  6      |  System.Decimal        |  6      |  46     |         |  0      |  46     |  
|  HAND_HELD_PH          |  Decimal     |  6      |  System.Decimal        |  6      |  47     |         |  0      |  47     |  

* Table: DB2SYS.WASH_BATH_LIFE (ASCII format)
*| ColumnName       | DBColType |DBColLen|  SysType        |BinLength|ColNo|NULLS |StartIdx  |ValueIdx| UniIdx | 
|  DTINSERT         | TIMESTAMP | 10     | System.DateTime |  0      | 0   | N    |  0       |  0     |        |
|  DT_ENTRY         | TIMESTAMP | 10     | System.DateTime |  0      | 1   | N    |  0       |  1     | Y      |
|  RESOURCE         | CHARACTER | 10     | System.String   |  0      | 2   | N    |  0       |  2     | Y      |
|  INTERVAL         | CHARACTER | 10     | System.String   |  0      | 3   | Y    |  0       |  3     | Y      |
|  PREOXIDE_PRIORITY_LOW | BIGINT    | 8      | System.String   |  0      | 4   | Y    |  0       |  4     |        |
|  PREOXIDE_PRIORITY_HIGH | BIGINT    | 8      | System.String   |  0      | 5   | Y    |  0       |  5     |        |
|  LOADER_DIW_LIFE  | BIGINT    | 8      | System.String   |  0      | 6   | Y    |  0       |  6     |        |
|  LOADER_US_LIFE   | BIGINT    | 8      | System.String   |  0      | 7   | Y    |  0       |  7     |        |
|  US_BATHLIFE      | BIGINT    | 8      | System.String   |  0      | 8   | Y    |  0       |  8     |        |
|  MS_BATHLIFE      | BIGINT    | 8      | System.String   |  0      | 9   | Y    |  0       |  9     |        |
|  LOADER_FILTERS   | BIGINT    | 8      | System.String   |  0      | 10  | Y    |  0       |  10    |        |
|  SCRUB_BRUSHES_LIFE | BIGINT    | 8      | System.String   |  0      | 11  | Y    |  0       |  11    |        |
|  SCRUB_FILTERS    | BIGINT    | 8      | System.String   |  0      | 12  | Y    |  0       |  12    |        |
|  US_FILTERS       | BIGINT    | 8      | System.String   |  0      | 13  | Y    |  0       |  13    |        |
|  QDR1_FILTERS     | BIGINT    | 8      | System.String   |  0      | 14  | Y    |  0       |  14    |        |
|  MS_FILTERS       | BIGINT    | 8      | System.String   |  0      | 15  | Y    |  0       |  15    |        |
|  RINSE_FILTERS    | BIGINT    | 8      | System.String   |  0      | 16  | Y    |  0       |  16    |        |
|  SCRUB_BRUSHES_DISK | BIGINT    | 8      | System.String   |  0      | 17  | Y    |  0       |  17    |        |
|  PREOXIDE_PRIORITY_LOW_LIMIT | BIGINT    | 8      | System.String   |  0      | 18  | Y    |  0       |  18    |        |
|  PREOXIDE_PRIORITY_HIGH_LIMIT | BIGINT    | 8      | System.String   |  0      | 19  | Y    |  0       |  19    |        |
|  LOADER_DIW_LIFE_LIMIT | BIGINT    | 8      | System.String   |  0      | 20  | Y    |  0       |  20    |        |
|  LOADER_US_LIFE_LIMIT | BIGINT    | 8      | System.String   |  0      | 21  | Y    |  0       |  21    |        |
|  US_BATHLIFE_LIMIT | BIGINT    | 8      | System.String   |  0      | 22  | Y    |  0       |  22    |        |
|  MS_BATHLIFE_LIMIT | BIGINT    | 8      | System.String   |  0      | 23  | Y    |  0       |  23    |        |
|  LOADER_FILTERS_LIMIT | BIGINT    | 8      | System.String   |  0      | 24  | Y    |  0       |  24    |        |
|  SCRUB_BRUSHES_LIFE_LIMIT | BIGINT    | 8      | System.String   |  0      | 25  | Y    |  0       |  25    |        |
|  SCRUB_FILTERS_LIMIT | BIGINT    | 8      | System.String   |  0      | 26  | Y    |  0       |  26    |        |
|  US_FILTERS_LIMIT | BIGINT    | 8      | System.String   |  0      | 27  | Y    |  0       |  27    |        |
|  QDR1_FILTERS_LIMIT | BIGINT    | 8      | System.String   |  0      | 28  | Y    |  0       |  28    |        |
|  MS_FILTERS_LIMIT | BIGINT    | 8      | System.String   |  0      | 29  | Y    |  0       |  29    |        |
|  RINSE_FILTERS_LIMIT | BIGINT    | 8      | System.String   |  0      | 30  | Y    |  0       |  30    |        |
|  SCRUB_BRUSHES_DISK_LIMIT | BIGINT    | 8      | System.String   |  0      | 31  | Y    |  0       |  31    |        |
|  PREOXIDE_PRIORITY_LOW_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 32  | Y    |  0       |  32    |        |
|  PREOXIDE_PRIORITY_HIGH_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 33  | Y    |  0       |  33    |        |
|  LOADER_DIW_LIFE_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 34  | Y    |  0       |  34    |        |
|  LOADER_US_LIFE_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 35  | Y    |  0       |  35    |        |
|  US_BATHLIFE_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 36  | Y    |  0       |  36    |        |
|  MS_BATHLIFE_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 37  | Y    |  0       |  37    |        |
|  LOADER_FILTERS_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 38  | Y    |  0       |  38    |        |
|  SCRUB_BRUSHES_LIFE_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 39  | Y    |  0       |  39    |        |
|  SCRUB_FILTERS_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 40  | Y    |  0       |  40    |        |
|  US_FILTERS_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 41  | Y    |  0       |  41    |        |
|  QDR1_FILTERS_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 42  | Y    |  0       |  42    |        |
|  MS_FILTERS_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 43  | Y    |  0       |  43    |        |
|  RINSE_FILTERS_LASTUPDATE_TS | TIMESTAMP | 10     | System.DateTime |  0      | 44  | Y    |  0       |  44    |        |
|  COMMENTS         | VARCHAR   | 50     | System.String   |  0      | 45  | Y    |  0       |  45    |        |

*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |  Y      |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |  Y      |  0      |  2      |  
|  CASSETTE              |  Char        |  11     |  System.String         |  11     |  3      |         |  0      |  3      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  4      |         |  0      |  4      |  
|  ZEBRAHOSTNAME         |  Char        |  10     |  System.String         |  10     |  5      |         |  0      |  5      |  
|  ZEBRASVCNAME          |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  ZEBRAPRINTERID        |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  8      |         |  0      |  8      |  
|  FORM_NAME             |  Char        |  10     |  System.String         |  10     |  9      |         |  0      |  9      |  
|  LONG_BARCODE          |  Char        |  30     |  System.String         |  30     |  10     |         |  0      |  10     |  

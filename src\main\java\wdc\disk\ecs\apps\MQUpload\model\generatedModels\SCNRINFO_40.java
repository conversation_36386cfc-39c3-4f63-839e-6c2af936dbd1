package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for SCNRINFO_40
 */
public class SCNRINFO_40  {
    private String scnrname;
    private Short boardnum;
    private String axis;
    private Double calibvalue;
    private Short maxposerr;
    private Double idlimit;
    private Double odlimit;
    private String scnrtype;
    private String hgaloader;
    private String serialnum;
    private String parms;
    private Short spindle;
    private java.time.LocalDateTime calibtime;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public SCNRINFO_40() {
    }

    public String getScnrname() {
        return scnrname;
    }

    public void setScnrname(String scnrname) {
        this.scnrname = scnrname;
    }

    public Short getBoardnum() {
        return boardnum;
    }

    public void setBoardnum(Short boardnum) {
        this.boardnum = boardnum;
    }

    public String getAxis() {
        return axis;
    }

    public void setAxis(String axis) {
        this.axis = axis;
    }

    public Double getCalibvalue() {
        return calibvalue;
    }

    public void setCalibvalue(Double calibvalue) {
        this.calibvalue = calibvalue;
    }

    public Short getMaxposerr() {
        return maxposerr;
    }

    public void setMaxposerr(Short maxposerr) {
        this.maxposerr = maxposerr;
    }

    public Double getIdlimit() {
        return idlimit;
    }

    public void setIdlimit(Double idlimit) {
        this.idlimit = idlimit;
    }

    public Double getOdlimit() {
        return odlimit;
    }

    public void setOdlimit(Double odlimit) {
        this.odlimit = odlimit;
    }

    public String getScnrtype() {
        return scnrtype;
    }

    public void setScnrtype(String scnrtype) {
        this.scnrtype = scnrtype;
    }

    public String getHgaloader() {
        return hgaloader;
    }

    public void setHgaloader(String hgaloader) {
        this.hgaloader = hgaloader;
    }

    public String getSerialnum() {
        return serialnum;
    }

    public void setSerialnum(String serialnum) {
        this.serialnum = serialnum;
    }

    public String getParms() {
        return parms;
    }

    public void setParms(String parms) {
        this.parms = parms;
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public java.time.LocalDateTime getCalibtime() {
        return calibtime;
    }

    public void setCalibtime(java.time.LocalDateTime calibtime) {
        this.calibtime = calibtime;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



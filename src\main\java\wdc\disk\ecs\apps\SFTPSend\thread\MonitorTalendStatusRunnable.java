package wdc.disk.ecs.apps.SFTPSend.thread;

import java.io.File;
import java.io.FileFilter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Vector;

import ibm.disk.cfgreader.ConfigReader;
import ibm.disk.cfgreader.ParameterRecord;
import ibm.disk.extensions.StringMethods;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;
import wdc.disk.ecs.apps.SFTPSend.util.SendMailForTalendWarning;
import wdc.disk.ecs.apps.SFTPSend.util.SendWarningMsgEntity;

/**
 * If padFileDir is pending too many files to process and 
 * the last modify time of sentFileDir remains the same, 
 * then will sent a warning email. 
 * <AUTHOR>
 *
 */
public class MonitorTalendStatusRunnable implements Runnable{
	private String profilePath;
	public boolean isDebug;
	public int unprocessLimit = 1;
	public boolean isUnprocessPdsQtyExceeded = false;
	public boolean isTimeout = false;
	public String pdsFileDirString;
	public String sentDirString;
	public int timeoutSeconds = 10;
	public LogStream log;
	public int monitorInterval;
	public int singleFolderCountLimit;
	
	public MonitorTalendStatusRunnable() {
		super();
	}
	
	public MonitorTalendStatusRunnable(String profilePath) {
		super();
		this.profilePath = profilePath;
		init(profilePath);
	}

	public void init(String profilePath) {
		System.out.println("run.....");
		readProfile(profilePath, "MonitorTalendAlive");
		String version = "Monitor Talend version. Lihao 07/27 15:30.";
		System.out.println(version);
		log.write(version);
	}

	@Override
	public void run() {
		System.out.println("Enter Run method.");
		if(!isDebug) {
			int sleepTime = monitorInterval * 1000;
			showAtTerminalAndLog("MonitorSftpSend Thread start to sleep " + monitorInterval + " seconds.");
					
			try {
				Thread.sleep(sleepTime);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
		//Set condition
		while(true) {
			try {
				List<SendWarningMsgEntity> problemList = runMonitorFolder(this.pdsFileDirString,this.timeoutSeconds, this.unprocessLimit);
				if(problemList.size() > 0) {
					SendMailForTalendWarning sendMail = new SendMailForTalendWarning(profilePath);
					sendMail.sendTalendWarningMail(problemList);
					showAtTerminalAndLog("mail is sent.");
				}
				
				int sleepTime = monitorInterval * 1000;
				showAtTerminalAndLog("MonitorSftpSend Thread start to sleep " + monitorInterval + " seconds.");					
				Thread.sleep(sleepTime);
			} catch (Exception e) {
				log.writeExceptionStack(e);
				continue;
			}
		}
	}

	
	public long getDirLastModifyTime(String sentDirString) throws SFTPSendException {
		File file = new File(sentDirString);
		if(!file.exists()) {
			throw new SFTPSendException(sentDirString + " is not exist.");
		}
		
		File resultFile = null;
		if(!file.isDirectory()) {
			throw new SFTPSendException("getFileDate(): is not a directory." + sentDirString );
		}else {
			resultFile = findLastestLastModifyFile(file);
		}
		if(resultFile != null) {
			return resultFile.lastModified();
		}else {
			return file.lastModified();
		}
	}

	public File findLastestLastModifyFile(File dir) {
		File resultFile = null;
		if(dir.isDirectory()) {
			long highest = 0;
			File[] listFiles = dir.listFiles();
			for(int i=0; i < listFiles.length; i++) {
//				System.out.println("i:" + i + " file:" + listFiles[i].getName() + " last:" + listFiles[i].lastModified());
				long fileLastModify = listFiles[i].lastModified();
				if(highest < fileLastModify){
					highest = fileLastModify;
					resultFile = listFiles[i];
				}
			}
		}
//		if(resultFile != null) {
//			log.write(LogStream.INFORMATION_MESSAGE,"Result file:" + resultFile.getAbsolutePath() );			
//		}
		return resultFile;
	}

//	/**
//	 * Scan monitor folder and compare with the unprocess limit.
//	 * Since the list number may be less than 996.So try not to set over it.
//	 * Return true: file quantity exceed the control limit
//	 * @param unprocessLimit
//	 * @param monitorFolderNameString
//	 * @return
//	 * @throws SFTPSendException
//	 */
//	public boolean isUnprocessPdsQtyExceeded(int unprocessLimit, String monitorFolderNameString) throws SFTPSendException {
//		File monitorFolder = new File(monitorFolderNameString);
//		if(!monitorFolder.isDirectory()) {
//			String warningString = "isUnprocessPdsQtyExceeded.Not a folder:" + monitorFolderNameString;
//			System.out.println(warningString);
//			throw new SFTPSendException(warningString);
//		}
//		String[] list = monitorFolder.list();
//		int fileQtyOfFolder = list.length;
//		if(fileQtyOfFolder <= unprocessLimit) {
//			System.out.println("Quantity no exceed. Good to go");
//			return false;
//		}else {
//			String msg =  "qty exceeded." + fileQtyOfFolder + " qty:" + fileQtyOfFolder;
//			System.out.println(msg);	
//			log.write(LogStream.INFORMATION_MESSAGE, msg);
//			return true;
//		}
//	}
//
	/*
	 * Returns if isTimeOut
	 * Return true :   lastModifyTime + seconds < now
	 */
	public boolean isTimeout(int seconds,long lastModifyTime) {
		Date nowDate = new Date();
		long now = nowDate.getTime();

		showAtTerminalAndLog("Current:" + new Date().toString() +".Oldest file time:" + new Date(lastModifyTime).toString());
		if(lastModifyTime + seconds * 1000 < now) {
			return true;
		}else {
			return false;
		}
	}

//	public boolean needToSendWarning(boolean isTimeout, boolean isUnprocessPdsQtyExceeded) {
//		System.out.println("isTimeOut:" + isTimeout);
//		System.out.println("isUnprocessPdsQtyExceeded: " + isUnprocessPdsQtyExceeded );
//		return (isTimeout && isUnprocessPdsQtyExceeded); 
//	}
	


	public boolean readProfile(String profile, String blockname) {
		System.out.println("MonitorSftpSendThread trying to read config file:" + profile);
		ConfigReader cr = null;
		try {
			cr = new ConfigReader(profile);
		} catch (Exception e) {
			System.out.println("Read config failed.");
			e.printStackTrace();
			return false;
		}
		
		System.out.println("Read group:" + blockname);
		// Get the general configurations for SFTPSend
		cr.startGroup(blockname);
		Vector mqAttrib = null;
		if (cr.hasMoreGroups()) {
			mqAttrib = cr.nextGroup();
			for (int i = 0; i < mqAttrib.size();i++) {
				ParameterRecord rec = (ParameterRecord)mqAttrib.get(i);
				if (rec.getKey().equals ("FOLDER_COUNT_LIMIT")) {
					try {
						unprocessLimit = Integer.parseInt(rec.getValue().trim());
					} catch (Exception e) {}   // defaults to log level information
				}else if (rec.getKey().equals ("TIMEOUT")) {
					String timeout = rec.getValue();
					if (timeout != null) {
						timeoutSeconds = Integer.parseInt(rec.getValue().trim());
					}
				}else if (rec.getKey().equals ("PDS_DIR")) {
					String pdsDirString = rec.getValue().trim();
					if(pdsDirString != null && !"".equals(pdsDirString)) {
						pdsFileDirString = pdsDirString;						
					}
				}else if (rec.getKey().equals ("SENT_DIR")) {
					String sentDirString = rec.getValue().trim();
					if(sentDirString != null && !"".equals(sentDirString)) {
						this.sentDirString = sentDirString;						
					}
				}else if (rec.getKey().equals ("MONITOR_INTERVAL")) {
					String intervalString = rec.getValue();
					if (intervalString != null) {
						this.monitorInterval = Integer.parseInt(intervalString.trim());
					}
				}else if (rec.getKey().equals ("LOG_FILE")) {
					String logFile = rec.getValue();
					if (logFile != null) {
						this.log = new LogStream(logFile.trim());
					}
				}else if (rec.getKey().equals ("IS_DEBUG")) {
					String val = rec.getValue();
					if(val != null && !"".equals(val.trim())) {
						this.isDebug = StringMethods.getBoolean(val);
					}
				}else if(rec.getKey().equals("SINGLE_FOLDER_COUNT_LIMIT")) {
					String limitStr = rec.getValue();
					if(limitStr != null && !"".equals(limitStr.trim())) {
						this.singleFolderCountLimit = Integer.parseInt(limitStr);
					}
				}
				
			}
		}
		return(true);
	}
	
	public boolean isExceedFolderQuantity(String folderPath, int controlLimit) {
		boolean result = false;
		
		return result;
	}

	public boolean isExceedFileLastModifyTime(String folderPath, int controlSeconds) {
		boolean result = false;
		
		return result;	
	}

	/**
	 * If return null, then the dir maybe isn't a directory. 
	 * Maybe there is no file.
	 * @param dir
	 * @return
	 */
	public File findOldestLastModifyFile(File dir) {
		File resultFile = null;
		if(dir.isDirectory()) {
			long oldest = 0;
			File[] listFiles = dir.listFiles(new FileFilterForSkippingEmpty());
			for(int i=0; i < listFiles.length; i++) {
//				System.out.println("i:" + i + " file:" + listFiles[i].getName() + " last:" + listFiles[i].lastModified());
				long fileLastModify = listFiles[i].lastModified();
				if(i == 0) {
					oldest = fileLastModify;
					resultFile = listFiles[0];
					continue;
				}
				if(oldest > fileLastModify){
					oldest = fileLastModify;
					resultFile = listFiles[i];
				}
			}
		}
		return resultFile;
	}

	public File findOldestLastModifyFile(File[] listFiles) {
		File resultFile = null;
		long oldest = 0;
		if(listFiles.length ==0) {
			return null;
		}
		
		for(int i=0; i < listFiles.length; i++) {
			long fileLastModify = listFiles[i].lastModified();
			
			// If file not exist or IO exception
			if(fileLastModify == 0) {
				continue;
			}else {
				if(oldest == 0) {
					oldest = fileLastModify;
					resultFile = listFiles[i];
					continue;
				}
				if(oldest > fileLastModify){
					oldest = fileLastModify;
					resultFile = listFiles[i];
				}
			}
		}
		return resultFile;
	}
	
	public File[] getFolderFileList(File dir) {
		File[] listFiles = null;
		if(!dir.isDirectory()) {
			return null;
		}else {
			listFiles = dir.listFiles(new FileFilterForSkippingEmpty());
		}
		return listFiles;
	}

	public int getFolderFileSize(File[] fileList) {
		int size = 0;
		if(fileList != null) {
			size = fileList.length;
		}
		return size;
	}
	
	public List<SendWarningMsgEntity> runMonitorFolder(String folderPath , int controlSeconds, int controlFolderQty) {
		List<SendWarningMsgEntity> problemList = new ArrayList<SendWarningMsgEntity>();
		File folder = new File(folderPath);
		if(folder.isDirectory()) {
			//Walk through all the message folders need to be monitored.
			File[] listMsgFolder = folder.listFiles(new FileFilterForSkippingEmptyAndTest());
			for(int i=0; i<listMsgFolder.length; i++ ) {
				File file = listMsgFolder[i];
							
				SendWarningMsgEntity entity = processMsgFolder(file.getAbsolutePath(), controlSeconds, controlFolderQty, singleFolderCountLimit);
				if(entity != null) {
					showAtTerminalAndLog(file.getName() + " will be added to warning list.");
					problemList.add(entity);
				}else {
					showAtTerminalAndLog("Folder:" + file.getName() + " is good.");
				}
			}
		}
		return problemList;
	}
	
	/*
	 * msgFoldrName is one of the msg folder. Then will look into each folder to see if timeout.
	 * If folder quantity equals 1, then find oldest time to compare if timeout.
	 * If folder quantity more than controlFolderQty, then return true.
	 */
//	public boolean processForMsgFolder(String msgFolderName, int controlSeconds, int controlFolderQty, int singleFileCountLimit) {
//		showAtTerminalAndLog("Processing folder:" + msgFolderName);
//		File msgfolder = new File(msgFolderName);
//		if(msgfolder.exists()){
//			File[] listFiles = msgfolder.listFiles(new FileFilterForSkippingEmpty());
//			int length = listFiles.length;
//			showAtTerminalAndLog("File List length is:" + length);
//			
//			if(length == 0) {
//				return false;
//			}else if(length == 1) {
//				File currentFolder = listFiles[0];
//				if(currentFolder != null && currentFolder.exists()) {	
//					File[] currentFolderFileList = getFolderFileList(currentFolder);
//					int size = getFolderFileSize(currentFolderFileList);
//					boolean isQtyExceed = isQtyExceed(size, singleFolderCountLimit);
//					showAtTerminalAndLog("isQtyExceed:" + isQtyExceed);		
////					File oldestFile = findOldestLastModifyFile(currentFolder);
//					File oldestFile = findOldestLastModifyFile(currentFolderFileList);
//					if(oldestFile != null) {
//						long oldestLastModified = oldestFile.lastModified();
//						showAtTerminalAndLog("Oldest:" + oldestFile.getAbsolutePath());
//						isTimeout = isTimeout(controlSeconds, oldestLastModified);
//						showAtTerminalAndLog("isTimeout:" + isTimeout);									
//					}else {
//						showAtTerminalAndLog("Can't find oldest file. Might have been processed.");
//					}
//					
//					if(isQtyExceed && isTimeout) {
//						return true;
//					}
//				}else {
//					showAtTerminalAndLog("If see this,this folder may has been processed by Talend job.");
//				}
//				return false;
//			}else if(length > 1) {
//				if(listFiles.length >= controlFolderQty) {
//					return true;
//				}else {
//					return false;					
//				}
//			}
//		}
//		showAtTerminalAndLog("Folder maynot exist." + msgfolder.getAbsolutePath());
//		return false;
//	}

	/*
	 * Process one message folder.
	 * Then will look into each folder to see if timeout.
	 * If folder quantity equals 1, then find oldest time to compare if timeout.
	 * If folder quantity more than controlFolderQty, then return true.
	 * 
	 * Return: null means there is no error.
	 * 		   If return an SendWarningMsgEntity, then this contains some warning details 
	 */
	public SendWarningMsgEntity processMsgFolder(String msgFolderName, int controlSeconds, int controlFolderQty, int singleFileCountLimit) {
		showAtTerminalAndLog("Processing folder:" + msgFolderName);
		File msgfolder = new File(msgFolderName);
		if(msgfolder.exists()){
			File[] listFiles = msgfolder.listFiles(new FileFilterForSkippingEmpty());
			int length = listFiles.length;
			showAtTerminalAndLog("File List length is:" + length);
			
			if(length == 0) {
				return null;
			}else if(length == 1) {
				File currentFolder = listFiles[0];
				if(currentFolder != null && currentFolder.exists()) {	
					File[] currentFolderFileList = getFolderFileList(currentFolder);
					int size = getFolderFileSize(currentFolderFileList);
					boolean isQtyExceed = isQtyExceed(size, singleFolderCountLimit);
					showAtTerminalAndLog("isQtyExceed:" + isQtyExceed + " size:" + size + " control limit:" + singleFileCountLimit);		
					if(!isQtyExceed) {
						showAtTerminalAndLog("Msg folder " + currentFolder.getName() + " quantity is not exceeded.Continue next msg folder. ");
						return null;
					}
					
					
					File oldestFile = findOldestLastModifyFile(currentFolderFileList);
					if(oldestFile != null) {
						long oldestLastModified = oldestFile.lastModified();
						showAtTerminalAndLog("Oldest:" + oldestFile.getAbsolutePath());
						isTimeout = isTimeout(controlSeconds, oldestLastModified);
						showAtTerminalAndLog("isTimeout:" + isTimeout + " File time:" + new Date(oldestLastModified).toString() + " current:" + new Date().toString());									
					}else {
						showAtTerminalAndLog("Can't find oldest file. Might have been processed.");
						isTimeout = false;
					}
					
					if(isQtyExceed && isTimeout) {
						SendWarningMsgEntity entity = new SendWarningMsgEntity(currentFolder.getName(), 
								SendWarningMsgEntity.Type.fileTimeout,
								"File:" + oldestFile.getName() + ".Date:" + new Date(oldestFile.lastModified()).toString() +
								".File quantity:" + size + ".Control limit:" + singleFolderCountLimit
								);
						return entity;
					}
				}else {
					showAtTerminalAndLog("If see this,this folder may has been processed by Talend job.");
				}
				return null;
			}else if(length > 1) {
				if(listFiles.length >= controlFolderQty) {
					SendWarningMsgEntity entity = new SendWarningMsgEntity(msgfolder.getName(), SendWarningMsgEntity.Type.folderQtyExceed,
							"Folder quantity is " + listFiles.length + " exceeds control qty " + controlFolderQty); 
					return entity;
				}else {
					return null;					
				}
			}
		}
		showAtTerminalAndLog("Folder maynot exist." + msgfolder.getAbsolutePath());
		return null;
	}
	
	public boolean isQtyExceed(int inputData, int controlLimit) {
		if(inputData < controlLimit) {
			return false;
		}else {
			return true;
		}
	}
	
	public void showAtTerminalAndLog(String msg) {
		System.out.println(msg);
		log.write(LogStream.INFORMATION_MESSAGE, msg);
	}
	

	//Skip null and TEST
	class FileFilterForSkippingEmptyAndTest implements FileFilter{
		@Override
		public boolean accept(File pathname) {

			if(pathname == null || !pathname.exists()){
				return false;				
			}else {
				String name = pathname.getName();
				if("TEST".equals(name.toUpperCase())) {
					return false;
				}
				return true;
			}
		}		
	}
	
}

package wdc.disk.ecs.apps.SFTPSend.thread;

import java.io.File;
import java.util.Date;

public class MonitorRunnableUtil {
	/*
	 * Returns if isTimeOut
	 * Return true :   lastModifyTime + seconds < now
	 */
	public static boolean isTimeout(int seconds,long lastModifyTime) {
		Date nowDate = new Date();
		long now = nowDate.getTime();

		if(lastModifyTime + seconds * 1000 < now) {
			return true;
		}else {
			return false;
		}
	}

	/**
	 * If return null, then the dir maybe isn't a directory. 
	 * Maybe there is no file.
	 * @param dir
	 * @return
	 */
	public static File findOldestLastModifyFile(File dir) {
		File resultFile = null;
		if(dir.isDirectory()) {
			long oldest = 0;
			File[] listFiles = dir.listFiles(new FileFilterForSkippingEmpty());
			for(int i=0; i < listFiles.length; i++) {
//				System.out.println("i:" + i + " file:" + listFiles[i].getName() + " last:" + listFiles[i].lastModified());
				long fileLastModify = listFiles[i].lastModified();
				if(i == 0) {
					oldest = fileLastModify;
					resultFile = listFiles[0];
					continue;
				}
				if(oldest > fileLastModify){
					oldest = fileLastModify;
					resultFile = listFiles[i];
				}
			}
		}
		return resultFile;
	}
}

package wdc.disk.ecs.apps.MQUpload.model;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.Arrays;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class ConsumerPropertiesTest {
    private ConsumerProperties consumerProperties;

    @BeforeEach
    void setUp() {
        consumerProperties = new ConsumerProperties();
    }

    @Test
    void testDefaultConstructor() {
        assertNotNull(consumerProperties.getQueues());
        assertTrue(consumerProperties.getQueues().isEmpty());
    }

    @Test
    void testSetAndGetQueues() {
        ConsumerProperties.QueueConfig queue1 = createQueueConfig("309", "Queue for 309 processing", true, 3, 1000);
        ConsumerProperties.QueueConfig queue2 = createQueueConfig("318", "Queue for 318 processing", true, 3, 1000);
        
        List<ConsumerProperties.QueueConfig> queueConfigs = Arrays.asList(queue1, queue2);
        consumerProperties.setQueues(queueConfigs);

        assertEquals(2, consumerProperties.getQueues().size());
        assertEquals("309", consumerProperties.getQueues().get(0).getName());
        assertEquals("318", consumerProperties.getQueues().get(1).getName());
    }

    @Test
    void testQueueConfigProperties() {
        ConsumerProperties.QueueConfig queueConfig = createQueueConfig(
            "309", 
            "Test Description", 
            true, 
            3, 
            1000
        );

        assertEquals("309", queueConfig.getName());
        assertEquals("Test Description", queueConfig.getDescription());
        assertTrue(queueConfig.isEnabled());
        assertEquals(3, queueConfig.getRetryCount());
        assertEquals(1000, queueConfig.getRetryDelay());
    }

    @Test
    void testQueueConfigSetters() {
        ConsumerProperties.QueueConfig queueConfig = new ConsumerProperties.QueueConfig();
        
        queueConfig.setName("309");
        queueConfig.setDescription("Updated Description");
        queueConfig.setEnabled(false);
        queueConfig.setRetryCount(5);
        queueConfig.setRetryDelay(2000);

        assertEquals("309", queueConfig.getName());
        assertEquals("Updated Description", queueConfig.getDescription());
        assertFalse(queueConfig.isEnabled());
        assertEquals(5, queueConfig.getRetryCount());
        assertEquals(2000, queueConfig.getRetryDelay());
    }

    private ConsumerProperties.QueueConfig createQueueConfig(
            String name, 
            String description, 
            boolean enabled, 
            int retryCount, 
            long retryDelay) {
        ConsumerProperties.QueueConfig config = new ConsumerProperties.QueueConfig();
        config.setName(name);
        config.setDescription(description);
        config.setEnabled(enabled);
        config.setRetryCount(retryCount);
        config.setRetryDelay(retryDelay);
        return config;
    }
}
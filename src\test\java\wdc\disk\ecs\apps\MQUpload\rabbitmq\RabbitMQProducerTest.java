package wdc.disk.ecs.apps.MQUpload.rabbitmq;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Properties;
import java.util.concurrent.TimeoutException;

import org.junit.jupiter.api.Test;

import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;
import com.rabbitmq.client.GetResponse;

public class RabbitMQProducerTest {

    private Properties properties;

    public RabbitMQProducerTest() {
        loadProperties();
    }

    private void loadProperties() {
        properties = new Properties();
        InputStream input = null;
        try {
            input = getClass().getClassLoader().getResourceAsStream("test/rabbitmq.properties");
            if (input == null) {
                System.out.println("Sorry, unable to find rabbitmq.properties");
                return;
            }
            properties.load(input);
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            if (input != null) {
                try {
                    input.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Test
    public void testSendMessageToRabbitMQ() throws IOException, TimeoutException {
        String host = properties.getProperty("rabbitmq.host");
        int port = Integer.parseInt(properties.getProperty("rabbitmq.port"));
        String username = properties.getProperty("rabbitmq.username");
        String password = properties.getProperty("rabbitmq.password");
        String virtualHost = properties.getProperty("rabbitmq.virtualHost");
        String exchangeName = properties.getProperty("rabbitmq.exchangeName");
        String routingKey = properties.getProperty("rabbitmq.routingKey");

        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setPort(port);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);

        Connection connection = null;
        Channel channel = null;
        try {
            connection = factory.newConnection();
            channel = connection.createChannel();


            // Define the message to be sent
            String message = "Hello, Cathy&Guang!";

            // Declare the exchange if it doesn't exist.  This is important for ensuring
            // that the message has a place to go.  Declare it as a direct exchange.
            // The parameters are:
            // - exchange: the name of the exchange
            // - type: the type of the exchange (e.g., "direct", "fanout", "topic")
            // - durable: whether the exchange should survive server restarts
            channel.exchangeDeclare(exchangeName, "direct", true);


            // Publish the message to the exchange with the specified routing key.
            // The parameters are:
            // - exchange: the name of the exchange
            // - routingKey: the routing key to use
            // - props: message properties (null for default)
            // - body: the message body as a byte array
            channel.basicPublish(exchangeName, routingKey, null, message.getBytes(StandardCharsets.UTF_8));
            System.out.println(" [x] Sent '" + message + "'");

        } catch (IOException e) {
        	e.printStackTrace();
        	throw e;
        } catch (TimeoutException e) {
        	e.printStackTrace();
        	throw e;
        } finally {
        	try {
        		if (channel != null && channel.isOpen()) {
        			channel.close();
        		}
        		if (connection != null && connection.isOpen()) {
        			connection.close();
        		}
        	} catch (IOException e) {
        		e.printStackTrace();
        	} catch (TimeoutException e) {
        		e.printStackTrace();
        	}
        }
    }



    @Test
    public void testReceiveMessageFromRabbitMQ() throws IOException, TimeoutException, InterruptedException {
        properties.setProperty("rabbitmq.host", "*************");
        properties.setProperty("rabbitmq.port", "5672");
        properties.setProperty("rabbitmq.username", "guest");
        properties.setProperty("rabbitmq.password", "guest");
        properties.setProperty("rabbitmq.virtualHost", "/");
        properties.setProperty("rabbitmq.exchangeName", "ecs.direct");
        properties.setProperty("rabbitmq.queueName", "dev_demo");
        properties.setProperty("rabbitmq.routingKey", "demo");


        String host = properties.getProperty("rabbitmq.host");
        int port = Integer.parseInt(properties.getProperty("rabbitmq.port"));
        String username = properties.getProperty("rabbitmq.username");
        String password = properties.getProperty("rabbitmq.password");
        String virtualHost = properties.getProperty("rabbitmq.virtualHost");
        String exchangeName = properties.getProperty("rabbitmq.exchangeName");
        String queueName = properties.getProperty("rabbitmq.queueName");
        String routingKey = properties.getProperty("rabbitmq.routingKey");


        ConnectionFactory factory = new ConnectionFactory();
        factory.setHost(host);
        factory.setPort(port);
        factory.setUsername(username);
        factory.setPassword(password);
        factory.setVirtualHost(virtualHost);

        Connection connection = null;
        Channel channel = null; 

        final String[] receivedMessage = {null}; 
        final long[] deliveryTagHolder = new long[1]; 

        try {
            connection = factory.newConnection();
            channel = connection.createChannel(); 
            
            channel.queueDeclare(queueName, true, false, false, null); // durable, non-exclusive, non-autodelete

            channel.queueBind(queueName, exchangeName, routingKey);



            GetResponse response = channel.basicGet(queueName, false); // autoAck = false

            if (response != null) {
                String message = new String(response.getBody(), StandardCharsets.UTF_8);
                System.out.println(" [x] Received '" + message + "'");
                receivedMessage[0] = message;
                deliveryTagHolder[0] = response.getEnvelope().getDeliveryTag();

                channel.basicAck(deliveryTagHolder[0], false);
                System.out.println("Received and acknowledged message: " + receivedMessage[0]);
            }
            else {
                System.out.println("No message received."); 
                throw new TimeoutException("No Message received, queue is empty!");
            }


        } finally {
            try {
                if (channel != null && channel.isOpen()) {
                    channel.close();
                }
                if (connection != null && connection.isOpen()) {
                    connection.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            } catch (TimeoutException e) {
                e.printStackTrace();
            }
        }
    }
} 

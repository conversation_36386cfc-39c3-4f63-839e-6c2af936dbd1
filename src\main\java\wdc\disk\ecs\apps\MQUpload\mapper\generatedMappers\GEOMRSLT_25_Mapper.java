package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.GEOMRSLT_25;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for GEOMRSLT_25
 */
public interface GEOMRSLT_25_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.GEOMRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{glidenum},",
        "    #{bandnum},",
        "    #{tohvelTop},",
        "    #{tohvelBot},",
        "    #{tohnmTop},",
        "    #{tohnmBot},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    BANDNUM,",
        "    TOHVEL_TOP,",
        "    TOHVEL_BOT,",
        "    TOHNM_TOP,",
        "    TOHNM_BOT,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.GLIDENUM = SOURCE.GLIDENUM AND TARGET.BANDNUM = SOURCE.BANDNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.TOHVEL_TOP = SOURCE.TOHVEL_TOP,",
        "    TARGET.TOHVEL_BOT = SOURCE.TOHVEL_BOT,",
        "    TARGET.TOHNM_TOP = SOURCE.TOHNM_TOP,",
        "    TARGET.TOHNM_BOT = SOURCE.TOHNM_BOT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    BANDNUM,",
        "    TOHVEL_TOP,",
        "    TOHVEL_BOT,",
        "    TOHNM_TOP,",
        "    TOHNM_BOT,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.GLIDENUM,",
        "    SOURCE.BANDNUM,",
        "    SOURCE.TOHVEL_TOP,",
        "    SOURCE.TOHVEL_BOT,",
        "    SOURCE.TOHNM_TOP,",
        "    SOURCE.TOHNM_BOT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(GEOMRSLT_25 record);
}











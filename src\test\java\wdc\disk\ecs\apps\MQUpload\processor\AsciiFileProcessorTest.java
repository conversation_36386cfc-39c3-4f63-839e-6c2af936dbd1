package wdc.disk.ecs.apps.MQUpload.processor;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Path;
import java.util.List;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

@ExtendWith(MockitoExtension.class)
public class AsciiFileProcessorTest {
	@Mock    
	private LogStream logStream;
    
    @Mock
    private DiskProfile dbMsgIDProfile;
    
    @Mock
    private DBMsgIDRecord dbMsgIDRecord;
    
    private AsciiFileProcessor processor;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() throws ProfileException {
        
        
        processor = new AsciiFileProcessor(logStream, dbMsgIDProfile, "test.pro");
    }

    @Test
    void testProcessValidFile() throws Exception {
    	// Setup mock behavior
        when(dbMsgIDProfile.getRecord(anyInt(), any())).thenReturn(dbMsgIDRecord);
        when(dbMsgIDRecord.getDBMsgID()).thenReturn("TEST_MSG_ID");
        when(dbMsgIDRecord.getTableName()).thenReturn("TEST_TABLE");
        // Create a test file with valid content
        File testFile = tempDir.resolve("test.txt").toFile();
        String content = "2023-12-25-10.30.00.000000 LID PROD 9 * /test/path/data\n" +
                        "2023-12-25-10.30.01.000000 LID PROD 9 * /test/path/data2";
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write(content);
        }

        // Process the file
        List<RabbitMessage> messages = processor.processFile(testFile);

        // Verify the result
        assertNotNull(messages);
        assertEquals(2, messages.size());
        assertTrue(new String(messages.get(0).getData(), StandardCharsets.UTF_8)
            .contains("/test/path/data"));
    }

    @Test
    void testProcessEmptyFile() throws Exception {
        File emptyFile = tempDir.resolve("empty.txt").toFile();
        assertTrue(emptyFile.createNewFile());

        List<RabbitMessage> messages = processor.processFile(emptyFile);
        assertTrue(messages.isEmpty());
    }

    @Test
    void testProcessFileWithInvalidLines() throws Exception {
        File testFile = tempDir.resolve("invalid_lines.txt").toFile();
        // Create a line that exceeds MAX_LINE_LENGTH (2000)
        StringBuilder longLine = new StringBuilder();
        for (int i = 0; i < 2001; i++) {
            longLine.append('a');
        }
        
        try (FileWriter writer = new FileWriter(testFile)) {
            writer.write(longLine.toString());
        }

        List<RabbitMessage> messages = processor.processFile(testFile);
        assertTrue(messages.isEmpty());
    }

    @Test
    void testProcessNonExistentFile() {
        File nonExistentFile = tempDir.resolve("nonexistent.txt").toFile();
        assertThrows(IOException.class, () -> processor.processFile(nonExistentFile));
    }

}
package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;

import java.util.List;

import java.util.StringTokenizer;
import java.util.Vector;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

/**
 * Implementation of FileProcessor for handling ASCII text files.
 * This class reads and processes ASCII-encoded files, performing necessary validations
 * and transformations.
 */
public class AsciiFileProcessor implements FileProcessor {
    private static final int MAX_LINE_LENGTH = 2000;
    private final LogStream log;
    private final DiskProfile dbMsgIDProfile;
    private final String dbMsgIDProfileName;

    public AsciiFileProcessor(LogStream log, 
            DiskProfile dbMsgIDProfile, String dbMsgIDProfileName) {
        this.log = log;
        this.dbMsgIDProfile = dbMsgIDProfile;
        this.dbMsgIDProfileName = dbMsgIDProfileName;
    }

    @Override
    public List<RabbitMessage> processFile(File file) throws IOException {
        try (RandomAccessFile in = new RandomAccessFile(file, "r")) {
            return readFileContent(in, file);
        }
    }

    /**
     * Reads and processes the content of an ASCII file.
     * 
     * This method performs the following operations:
     * 1. Reads the file line by line
     * 2. Validates each line's length
     * 3. Processes each valid line to extract its message ID using pdsMsgId.pro profile
     * 4. Creates a RabbitMessage for each valid line
     * 
     * @param in RandomAccessFile to read from
     * @param file File object for logging purposes
     * @return List of RabbitMessage objects, one per valid line
     * @throws IOException if file reading errors occur
     */
    private List<RabbitMessage> readFileContent(RandomAccessFile in, File file) throws IOException {
        List<RabbitMessage> messages = new ArrayList<>();
        String line;
        int lineNum = 0;

        while ((line = in.readLine()) != null) {
            lineNum++;
            if (!validateLine(line, file, lineNum)) {
                continue;
            }

            String msgId = getDBMsgID(line);
            if (msgId != null && !msgId.isEmpty()) {
                RabbitMessage message = new RabbitMessage(
                    msgId,
                    //Todo
                    //how to validate?
                    (line + "\n").getBytes(StandardCharsets.UTF_8)
                );
                messages.add(message);
            }
        }

        return messages;
    }

    /**
     * Validates a line from the ASCII file.
     * 
     * Validation rules:
     * - Line length must not exceed MAX_LINE_LENGTH (2000 characters)
     * - Empty lines are considered valid
     * 
     * @param line The line to validate
     * @param file The source file (for error logging)
     * @param lineNum The line number in the file (for error logging)
     * @return true if the line is valid, false otherwise
     */
    private boolean validateLine(String line, File file, int lineNum) {
        if (line.length() > MAX_LINE_LENGTH) {
            log.write(LogStream.ERROR_MESSAGE, 
                String.format("Line %d exceeds maximum length in file: %s", lineNum, file.getName()));
            log.write(LogStream.INFORMATION_MESSAGE,String.format("Exceed line:%s",line));
            return false;
        }
        return true;
    }

    /**
     * Get DBMsgID of ascii-encoded data by looking up the pdsMsgId.pro profile.
     * 
     * Profile Format (pdsMsgId.pro):
     * 			|TabCode	 |MQ messageId|MSG Type  |Table Name|Split Qty|Splitter|SplitMode|Folder|
     * Example: |CASWASHEVENT|209         |ASCII     |COMPONENT | 10000|||
     * 
     * Comparison Types:
     * 1. TABCODE (DBMsgIDRecord.COMPARE_BY_TABCODE):
     *    - Matches the first column "TabCode" in pdsMsgId.pro
     *    - Used in this method to look up MQ messageId by TabCode
     *    - Example: If input line contains "CASWASHEVENT", it matches first column and returns "209"
     * 
     * 2. MSGID (DBMsgIDRecord.COMPARE_BY_MSGID):
     *    - Matches the second column "MQ messageId" in pdsMsgId.pro
     *    - Used when you need to look up configuration by known message ID
     *    - Example: Looking up configuration for messageId "209" would match multiple types
     *      (CASWASHEVENT, PRINTER, etc.) that all use COMPONENT table
     * 
     * Example data line:
     * 2021-07-31-15.01.19.000000 LID PROD 9 * /B883C526236/PJ02715299/PCDJ/DOUBLE/BY48Z/LID4001/0.4/1.1/1.3
     * Where:
     * - First token: Timestamp (skipped)
     * - Second token: PDS Type (used for lookup)
     * 
     * @param line The line of data to process
     * @return The database message ID (second column from profile), 
     *         null if invalid format, 
     *         or empty string if no profile match
     */
    private String getDBMsgID(String line) {
        String dbMsgID = "";
        if (line != null) {
            StringTokenizer st = new StringTokenizer(line, " ");
            
            if (st.countTokens() < 3) {
                return null;
            }
            
            // ignore the first token, should be iso timestamp
            st.nextToken();
            
            String pdsType = st.nextToken();
            
            Vector<String> keys = new Vector<>();
            keys.add(DBMsgIDRecord.COMPARE_BY_TYPE);
            keys.add(pdsType);

            try {
                DBMsgIDRecord dbMsgIDRec = (DBMsgIDRecord) dbMsgIDProfile.getRecord(0, keys);
                if (dbMsgIDRec == null) {
                    log.write(LogStream.ERROR_MESSAGE, 
                        "[PDS Type=" + pdsType + "] is not configured in " + dbMsgIDProfileName);
                } else {
                    dbMsgID = dbMsgIDRec.getDBMsgID();
                    log.write(LogStream.DEBUG_MESSAGE, 
                        "[Get from profile: " + dbMsgIDProfileName + "] DB Message ID=" + 
                        dbMsgIDRec.getDBMsgID() + ", Table Name=" + dbMsgIDRec.getTableName());
                }
            } catch (ProfileException e) {
                log.write(LogStream.ERROR_MESSAGE, "Error getting DB Message ID: " + e.getMessage());
            }
        }
        return dbMsgID;
    }
}

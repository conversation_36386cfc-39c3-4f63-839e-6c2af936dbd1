* Type            | MQ messageId | MSG Type | Table Name                 | Split Qty | Splitter        | SplitMode | Folder                    |
*----------------+-------------+----------+------------------------- +-----------+----------------+-----------+---------------------------|
| 2              | 2           | Binary   | GLDRSLT                  | 10000     | [$]            |           |                           |
| 3              | 3           | Binary   | CLIPRSLT                 | 10000     | [$]            |           |                           |
| 6              | 6           | Binary   | SECTRSLT                 | 10000     | [$]            |           |                           |
| 7              | 7           | Binary   | TRCKRSLT                 | 10000     | [$]            |           |                           |
| 8              | 8           | Binary   | BANDRSLT                 | 10000     | [$]            |           |                           |
| 10             | 10          | Binary   | HEADLOG                  | 10000     | [$]            |           |                           |
| 11             | 11          | Binary   | BATCHINFO                | 10000     | [$]            |           |                           |
| 12             | 12          | Binary   | TLOG                     | 10000     | [$]            |           |                           |
| 15             | 15          | Binary   | MONDATA                  | 10000     | [$]            |           |                           |
| 18             | 18          | Binary   | GMONDATA                 | 10000     | [$]            |           |                           |
| 19             | 19          | Binary   | MONRSLT                  | 10000     | [$]            |           |                           |
| 20             | 20          | Binary   | GAUGERUN                 | 10000     | [$]            |           |                           |
| 21             | 21          | Binary   | SH_GMONDATA              | 10000     | [$]            |           |                           |
| 24             | 24          | Binary   | MISCDATA                 | 10000     | [$]            |           |                           |
| 25             | 25          | Binary   | GEOMRSLT                 | 10000     | [$]            |           |                           |
| 26             | 26          | Binary   | DECOGAUGE                | 10000     | [$]            |           |                           |
| 27             | 27          | Binary   | BANDRSLT_STRING          | 10000     | [$]            |           |                           |
| 29             | 29          | Binary   | GLDLOG                   | 10000     | [$]            |           |                           |
| 30             | 30          | Binary   | TESTLOG                  | 10000     | [$]            |           |                           |
| 31             | 31          | Binary   | SOFTRSLT                 | 10000     | [$]            |           |                           |
| 32             | 32          | Binary   | MONRSLT                  | 10000     | [$]            |           |                           |
| 33             | 33          | Binary   | GAUGERUN                 | 10000     | [$]            |           |                           |
| 34             | 34          | Binary   | GLDLOG                   | 10000     | [$]            |           |                           |
| 35             | 35          | Binary   | TESTLOG                  | 10000     | [$]            |           |                           |
| 36             | 36          | Binary   | TRCKRSLT                 | 10000     | [$]            |           |                           |
| 37             | 37          | Binary   | GLDPARM                  | 10000     | [$]            |           |                           |
| 38             | 38          | Binary   | MAGCBPRM                 | 10000     | [$]            |           |                           |
| 39             | 39          | Binary   | MAGMBPRM                 | 10000     | [$]            |           |                           |
| 40             | 40          | Binary   | SCNRINFO                 | 10000     | [$]            |           |                           |
| 41             | 41          | Binary   | GLDLOG                   | 10000     | [$]            |           |                           |
| 42             | 42          | Binary   | TESTLOG                  | 10000     | [$]            |           |                           |
| 43             | 43          | Binary   | HEADLOG                  | 10000     | [$]            |           |                           |
| 44             | 44          | Binary   | GMONDATA                 | 10000     | [$]            |           |                           |
| 45             | 45          | Binary   | SH_GMONDATA              | 10000     | [$]            |           |                           |
| ORM            | 200         | ASCII    | ORM_MON                  | 10000     |                |           |                           |
| DMS_ONLY       | 201         | ASCII    | DMS_MON                  | 10000     |                |           |                           |
| DMS_REF        | 202         | ASCII    | DMS_REF                  | 10000     |                |           |                           |
| ELL_ONLY       | 203         | ASCII    | ELLIPS_MON               | 10000     |                |           |                           |
| NK_ONLYA       | 204         | ASCII    | NK_MON                   | 10000     |                |           |                           |
| NK_ONLYB       | 205         | ASCII    | NK_MON                   | 10000     |                |           |                           |
| REF_ELLIPS     | 206         | ASCII    | ELLIPS_REF               | 10000     |                |           |                           |
| HRAVIS         | 207         | ASCII    | HRAVIS                   | 10000     |                |           |                           |
| AVISREF        | 208         | ASCII    | AVISREF                  | 10000     |                |           |                           |
| DECOEVENT      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| OLIVERPAD      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| OLIVERFILT     | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| OLIVEREVENT    | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| LUBEFILTER     | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| LUBECLEAN      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| LUBEMANDRIL    | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| LUBEBATH       | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| LUBELOG        | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| LUBEEVENT      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| FTIRSLOPE      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| FTPTAPE        | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| FTPEVENT       | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| SPUTEVENT      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| VDCSEVENT      | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| EDCEVENT       | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| CASWASHEVENT   | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| PRINTER        | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| GUZIK_MON      | 800         | ASCII    | GUZIK_MON                | 10000     |                |           |                           |
| FTIR           | 210         | ASCII    | FTIR_LUBE_MON            | 10000     |                |           |                           |
| FTIR_REF       | 211         | ASCII    | FTIR_REF                 | 10000     |                |           |                           |
| PCANDELA       | 212         | ASCII    | WASHCANDELA              | 10000     |                |           |                           |
| PCANDELARAW    | 213         | ASCII    | PCANDELARAW              | 10000     |                |           |                           |
| FTP_CAND       | 214         | ASCII    | CANDELA                  | 10000     |                |           |                           |
| FTP_CAND2      | 215         | ASCII    | FTPOODCANDELA            | 10000     |                |           |                           |
| SPECTRO        | 216         | ASCII    | SPECTRONIC               | 10000     |                |           |                           |
| RIGAKU_MON     | 217         | ASCII    | RIGAKU_MON               | 10000     |                |           |                           |
| RIGAKU_MON2    | 247         | ASCII    | RIGAKU_MON               | 10000     |                |           |                           |
| RIGAKU_MON3    | 266         | ASCII    | RIGAKU_MON               | 10000     |                |           |                           |
| RIGAKU_REF     | 218         | ASCII    | RIGAKU_REF               | 10000     |                |           |                           |
| RIGAKU_REF2    | 248         | ASCII    | RIGAKU_REF               | 10000     |                |           |                           |
| RIGAKU_REF3    | 267         | ASCII    | RIGAKU_REF               | 10000     |                |           |                           |
| FTPDRRAW       | 219         | ASCII    | FTP_DR_RAW               | 10000     |                |           |                           |
| FTPDRSUM       | 220         | ASCII    | FTP_DR_SUM               | 10000     |                |           |                           |
| ATSUVCURE      | 221         | ASCII    | ATSUVCURE                | 10000     |                |           |                           |
| FTPRAW         | 222         | ASCII    | FTP_RAW                  | 10000     |                |           |                           |
| FTPSUM         | 223         | ASCII    | FTP_SUM                  | 10000     |                |           |                           |
| FLYHITE        | 224         | ASCII    | FH_FORM                  | 10000     |                |           |                           |
| CURE_MONITOR   | 225         | ASCII    | CURE_MON                 | 10000     |                |           |                           |
| OLIVER         | 227         | ASCII    | OLIVER                   | 10000     |                |           |                           |
| OLIVERPARM     | 228         | ASCII    | OLIVERPARM               | 10000     |                |           |                           |
| DECORAW        | 229         | ASCII    | DECORAW                  | 10000     |                |           |                           |
| SPUTCANDELA    | 230         | ASCII    | SPUTCANDELA              | 10000     |                |           |                           |
| FTIR_LUBE      | 231         | ASCII    | FTIR_LUBE                | 10000     |                |           |                           |
| NKC_MON        | 232         | ASCII    | NKC_MON                  | 10000     |                |           |                           |
| NKC_MON2       | 245         | ASCII    | NKC_MON                  | 10000     |                |           |                           |
| NKC_REF        | 233         | ASCII    | NKC_REF                  | 10000     |                |           |                           |
| NKC_REF2       | 246         | ASCII    | NKC_REF                  | 10000     |                |           |                           |
| PPSENDAHEAD    | 234         | ASCII    | PPSENDAHEAD              | 10000     |                |           |                           |
| TMS_MON        | 235         | ASCII    | TMS_MON                  | 10000     |                |           |                           |
| CAND6120       | 236         | ASCII    | CANDELA_6120             | 10000     |                |           |                           |
| CAND6120BIN    | 237         | ASCII    | CANDELA_6120_BINS        | 10000     |                |           |                           |
| POKE_MON       | 238         | ASCII    | POKE_MON                 | 10000     |                |           |                           |
| POKE_MON2      | 244         | ASCII    | POKE_MON                 | 10000     |                |           |                           |
| POKE_REF       | 239         | ASCII    | POKE_REF                 | 10000     |                |           |                           |
| POKE_TDECMON   | 240         | ASCII    | POKE_TDECOMN             | 10000     |                |           |                           |
| POKE_TDECREF   | 241         | ASCII    | POKE_TDECREF             | 10000     |                |           |                           |
| LUBE_CANDELA   | 249         | ASCII    | CANDELA_LUBE             | 10000     |                |           |                           |
| SPUTTER_ALARM  | 250         | ASCII    | SPUTTER_ALARM            | 10000     |                |           |                           |
| ATSUVCURE2     | 253         | ASCII    | ATSUVCURE                | 10000     |                |           |                           |
| CASSWASH       | 258         | ASCII    | CASSWASH                 | 10000     |                | 3         | 258_283/258_283_current   |
| DECORAW2       | 254         | ASCII    | DECORAW2                 | 10000     |                |           |                           |
| DECORAW3       | 255         | ASCII    | DECORAW3                 | 10000     |                |           |                           |
| DECORAW4       | 269         | ASCII    | DECORAW4                 | 10000     |                |           |                           |
| MFB_RAW        | 259         | ASCII    | MFB_RAW                  | 10000     |                |           |                           |
| MFB_SUM        | 260         | ASCII    | MFB_SUM                  | 10000     |                |           |                           |
| MVI            | 261         | ASCII    | MVI                      | 10000     |                |           |                           |
| LUBE_CANDELA_DEF| 263        | ASCII    | CANDELA_LUBEDEFECT       | 10000     |                |           |                           |
| MVIEVENT       | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| MFBEVENT       | 209         | ASCII    | COMPONENT                | 10000     |                |           |                           |
| ZEBRAPRTID        |  262     |   ASCII  |   ZEBRAPRTID             | 10000     |               |              |              |
| OQA_LOT           |  264     |   ASCII  |   OQA_LOT                | 10000     |               |              |              |
| YACEVENT          |  209     |   ASCII  |   COMPONENT              | 10000     |               |              |              |
| NKC_OFFSETS       |  265     |   ASCII  |   NKC_OFFSETS            | 10000     |               |              |              |
| GUZIK2_MON        |  252     |   ASCII  |   GUZIK2_MON             | 10000     |               |              |              |
| ECSCLASSES        |  268     |   ASCII  |   ECSCLASSES             | 10000     |               |              |              |
| UVCUREEVENT       |  209     |   ASCII  |   COMPONENT              | 10000     |               |              |              |
| ATSUVCURE         |  273     |   ASCII  |   ATSUVCURE_V2           | 10000     |               |              |              |
| RQ9DISKS          |  274     |   ASCII  |   RQ9000_DISKS           | 10000     |               |               |              |    
| RQ9DEFECTS        |  275     |   ASCII  |   RQ9000_DEFECTS         | 10000     |               |               |              |
| CASSWASH_U        |  283     |   ASCII  |   CASSWASH               | 10000     |               |  3            | 258_283/258_283_current         |
| PCCACTION         |  284     |   ASCII  |   PCC_ACTION             | 10000     |               |               |              |
| PCCCASSETTE       |  285     |   ASCII  |   PCC_CASSETTE           | 10000     |               |               |              |
| POKE_SETUP        |  278     |   ASCII  |   POKE_SETUP             | 10000     |               |               |              |
| RIGAKU_SETUP      |  279     |   ASCII  |   RIGAKU_SETUP           | 10000     |               |               |              |
| NKC_SETUP         |  280     |   ASCII  |   NKC_SETUP              | 10000     |               |               |              |
| NKC_SETUP_OFFSETS |  281     |   ASCII  |   NKC_SETUP_OFFSETS      | 10000     |               |               |              |
| AOI_DISKS         |  282     |   ASCII  |   AOI_DISKS              | 10000     |               |               |              |
| GUZIK_KSC_MON     |  286     |   ASCII  |   GUZIK_KSC_MON          | 10000     |               |               |              |
| GUZIK_MODU        |  287     |   ASCII  |   GUZIK_MODU             | 10000     |               |               |              |
| GUZIK_XTI         |  288     |   ASCII  |   GUZIK_XTI              | 10000     |               |               |              |
| BAG_TRACE         |  289     |   ASCII  |   BAG_TRACE              | 10000     |               |               |              |
| WASH_STEP         |  290     |   ASCII  |   CASSWASH_STEP_INFO     | 10000     |               |               |              |
| MACHINE_STATUS    |  291     |   ASCII  |   CASSWASH_MACHINE_STATUS| 10000     |               |               |              |
| GUZIKKSDPARAM     |  292     |   ASCII  |   GUZIK_KSD_PARAM	     | 10000     |               |               |              |
| GUZIKKSDCHANNEL   |  293     |   ASCII  |   GUZIK_KSD_CHANNEL	     | 10000     |               |               |              |
| GUZIKKSDINFREQ    |  294     |   ASCII  |   GUZIK_KSD_INFREQ	     | 10000     |               |               |              |
| GUZIKKSDSPINMAT   |  295     |   ASCII  |   GUZIK_KSD_SPINMAT	     | 10000     |               |               |              |
| GUZIKKSDMAXAD     |  296     |   ASCII  |   GUZIK_KSD_MAX_AD	     | 10000     |               |               |              |
| GUZIKKSDTNOISE    |  297     |   ASCII  |   GUZIK_KSD_TNOISE	     | 10000     |               |               |              |
| GUZIKKSDPYTHON    |  298     |   ASCII  |   GUZIK_KSD_PYTHON	     | 10000     |               |               |              |
| PSCWASH           |  299     |   ASCII  |   PSCWASH 		         | 10000     |               |               |              |
| TDSSCAN           |  300     |   ASCII  |   TDS_SCAN		         | 10000     |               |               |              |
| TDSCHANNEL        |  301     |   ASCII  |   TDS_CHANNEL_STAT	     | 10000     |               |               |              |
| TDSHEADSTAT       |  302     |   ASCII  |   TDS_HEAD_STAT	         | 10000     |               |               |              |
| TDSGRADE          |  303     |   ASCII  |   TDS_GRADE		         | 10000     |               |               |              |
| TDSCLUSTER        |  304     |   ASCII  |   TDS_CLUSTER		     | 10000     |               |               |              |
| TDSCLUSDATA       |  305     |   ASCII  |   TDS_CLUSTER_DATA	     | 10000     |               |               |              |
| TDSSITEMARK       |  306     |   ASCII  |   TDS_SITEMARK_CLUS	     | 10000     |               |               |              |
| SPUTTERLEAN       |  307     |   ASCII  |   SPUTTER_LEAN	         | 10000     |               |               |              |
| DECORAW_NEO       |  308     |   ASCII  |   DECORAW_NEO 	         | 10000     |               |               |              |
| LID               |  309     |   ASCII  |   LID			         | 10000     |               |               |              |
| TDSENG            |  310     |   ASCII  |   TDS_Eng		         | 10000     |               |               |              |
| DECO_H2O2         |  209     |   ASCII  |   COMPONENT              | 10000     |               |               |              |
| DECOALARM         |  209     |   ASCII  |   COMPONENT              | 10000     |               |               |              |
| LIDALARM          |  209     |   ASCII  |   COMPONENT              | 10000     |               |               |              |    
| LuBatalarm        |  209     |   ASCII  |   COMPONENT              | 10000     |               |               |              |    
| AOISCAN           |  311     |   ASCII  |   AOI_SCAN		         | 10000     |               |               |              |
| AOICHANNEL        |  312     |   ASCII  |   AOI_CHANNEL_STAT	     | 10000     |               |               |              |
| AOIKPIV           |  313     |   ASCII  |   AOI_KPIV		         | 10000     |               |               |              |
| AOICLUSTER        |  314     |   ASCII  |   AOI_CLUSTER		     | 10000     |               |               |              |
| AOIENG            |  315     |   ASCII  |   AOI_ENG     	         | 10000     |               |               |              |
| POKE_MON_EOD      |  318     |   ASCII  |   POKE_MON_EOD  	     | 10000     |               |               |              |


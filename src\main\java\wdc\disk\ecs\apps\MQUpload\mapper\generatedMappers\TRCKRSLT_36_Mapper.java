package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TRCKRSLT_36;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TRCKRSLT_36
 */
public interface TRCKRSLT_36_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TRCKRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{testnum},",
        "    #{bandnum},",
        "    #{track},",
        "    #{reqopernum},",
        "    #{topresult},",
        "    #{bottomresult},",
        "    #{realtrack},",
        "    #{dtinsert},",
        "    #{correlid},",
        "    #{avalclip}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    TRACK,",
        "    REQOPERNUM,",
        "    TOPRESULT,",
        "    BOTTOMRESULT,",
        "    REALTRACK,",
        "    DTINSERT,",
        "    CORRELID,",
        "    AVALCLIP",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TESTNUM = SOURCE.TESTNUM AND TARGET.BANDNUM = SOURCE.BANDNUM AND TARGET.TRACK = SOURCE.TRACK AND TARGET.REQOPERNUM = SOURCE.REQOPERNUM AND TARGET.AVALCLIP = SOURCE.AVALCLIP",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.TOPRESULT = SOURCE.TOPRESULT,",
        "    TARGET.BOTTOMRESULT = SOURCE.BOTTOMRESULT,",
        "    TARGET.REALTRACK = SOURCE.REALTRACK,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    TRACK,",
        "    REQOPERNUM,",
        "    TOPRESULT,",
        "    BOTTOMRESULT,",
        "    REALTRACK,",
        "    DTINSERT,",
        "    CORRELID,",
        "    AVALCLIP",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.BANDNUM,",
        "    SOURCE.TRACK,",
        "    SOURCE.REQOPERNUM,",
        "    SOURCE.TOPRESULT,",
        "    SOURCE.BOTTOMRESULT,",
        "    SOURCE.REALTRACK,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.AVALCLIP",
        "  )"
    })
    void insertOrUpdate(TRCKRSLT_36 record);
}











package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.GLDLOG_29;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for GLDLOG_29
 */
public interface GLDLOG_29_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.GLDLOG AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{glidenum},",
        "    #{batchid},",
        "    #{productcode},",
        "    #{diskcode},",
        "    #{disksequence},",
        "    #{gliderule},",
        "    #{glidecode},",
        "    #{qcode},",
        "    #{toplevel},",
        "    #{bottomlevel},",
        "    #{startglide},",
        "    #{stopglide},",
        "    #{offset},",
        "    #{dtinsert},",
        "    #{correlid},",
        "    #{lot},",
        "    #{unitid},",
        "    #{botFull},",
        "    #{lulSoftBot},",
        "    #{lulCrashBot},",
        "    #{lulHardBot},",
        "    #{lulZonalBot},",
        "    #{dataSoftBot},",
        "    #{dataCrashBot},",
        "    #{dataHardBot},",
        "    #{dataZonalBot},",
        "    #{idcSoftBot},",
        "    #{idcCrashBot},",
        "    #{idcHardBot},",
        "    #{idcZonalBot},",
        "    #{topFull},",
        "    #{lulSoftTop},",
        "    #{lulCrashTop},",
        "    #{lulHardTop},",
        "    #{lulZonalTop},",
        "    #{dataSoftTop},",
        "    #{dataCrashTop},",
        "    #{dataHardTop},",
        "    #{dataZonalTop},",
        "    #{idcSoftTop},",
        "    #{idcCrashTop},",
        "    #{idcHardTop},",
        "    #{idcZonalTop},",
        "    #{padwipeCount},",
        "    #{gldrulever},",
        "    #{gldchecksum}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    BATCHID,",
        "    PRODUCTCODE,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    GLIDERULE,",
        "    GLIDECODE,",
        "    QCODE,",
        "    TOPLEVEL,",
        "    BOTTOMLEVEL,",
        "    STARTGLIDE,",
        "    STOPGLIDE,",
        "    OFFSET,",
        "    DTINSERT,",
        "    CORRELID,",
        "    LOT,",
        "    UNITID,",
        "    BOT_FULL,",
        "    LUL_SOFT_BOT,",
        "    LUL_CRASH_BOT,",
        "    LUL_HARD_BOT,",
        "    LUL_ZONAL_BOT,",
        "    DATA_SOFT_BOT,",
        "    DATA_CRASH_BOT,",
        "    DATA_HARD_BOT,",
        "    DATA_ZONAL_BOT,",
        "    IDC_SOFT_BOT,",
        "    IDC_CRASH_BOT,",
        "    IDC_HARD_BOT,",
        "    IDC_ZONAL_BOT,",
        "    TOP_FULL,",
        "    LUL_SOFT_TOP,",
        "    LUL_CRASH_TOP,",
        "    LUL_HARD_TOP,",
        "    LUL_ZONAL_TOP,",
        "    DATA_SOFT_TOP,",
        "    DATA_CRASH_TOP,",
        "    DATA_HARD_TOP,",
        "    DATA_ZONAL_TOP,",
        "    IDC_SOFT_TOP,",
        "    IDC_CRASH_TOP,",
        "    IDC_HARD_TOP,",
        "    IDC_ZONAL_TOP,",
        "    PADWIPE_COUNT,",
        "    GLDRULEVER,",
        "    GLDCHECKSUM",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.GLIDENUM = SOURCE.GLIDENUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.BATCHID = SOURCE.BATCHID,",
        "    TARGET.PRODUCTCODE = SOURCE.PRODUCTCODE,",
        "    TARGET.DISKCODE = SOURCE.DISKCODE,",
        "    TARGET.DISKSEQUENCE = SOURCE.DISKSEQUENCE,",
        "    TARGET.GLIDERULE = SOURCE.GLIDERULE,",
        "    TARGET.GLIDECODE = SOURCE.GLIDECODE,",
        "    TARGET.QCODE = SOURCE.QCODE,",
        "    TARGET.TOPLEVEL = SOURCE.TOPLEVEL,",
        "    TARGET.BOTTOMLEVEL = SOURCE.BOTTOMLEVEL,",
        "    TARGET.STARTGLIDE = SOURCE.STARTGLIDE,",
        "    TARGET.STOPGLIDE = SOURCE.STOPGLIDE,",
        "    TARGET.OFFSET = SOURCE.OFFSET,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID,",
        "    TARGET.LOT = SOURCE.LOT,",
        "    TARGET.UNITID = SOURCE.UNITID,",
        "    TARGET.BOT_FULL = SOURCE.BOT_FULL,",
        "    TARGET.LUL_SOFT_BOT = SOURCE.LUL_SOFT_BOT,",
        "    TARGET.LUL_CRASH_BOT = SOURCE.LUL_CRASH_BOT,",
        "    TARGET.LUL_HARD_BOT = SOURCE.LUL_HARD_BOT,",
        "    TARGET.LUL_ZONAL_BOT = SOURCE.LUL_ZONAL_BOT,",
        "    TARGET.DATA_SOFT_BOT = SOURCE.DATA_SOFT_BOT,",
        "    TARGET.DATA_CRASH_BOT = SOURCE.DATA_CRASH_BOT,",
        "    TARGET.DATA_HARD_BOT = SOURCE.DATA_HARD_BOT,",
        "    TARGET.DATA_ZONAL_BOT = SOURCE.DATA_ZONAL_BOT,",
        "    TARGET.IDC_SOFT_BOT = SOURCE.IDC_SOFT_BOT,",
        "    TARGET.IDC_CRASH_BOT = SOURCE.IDC_CRASH_BOT,",
        "    TARGET.IDC_HARD_BOT = SOURCE.IDC_HARD_BOT,",
        "    TARGET.IDC_ZONAL_BOT = SOURCE.IDC_ZONAL_BOT,",
        "    TARGET.TOP_FULL = SOURCE.TOP_FULL,",
        "    TARGET.LUL_SOFT_TOP = SOURCE.LUL_SOFT_TOP,",
        "    TARGET.LUL_CRASH_TOP = SOURCE.LUL_CRASH_TOP,",
        "    TARGET.LUL_HARD_TOP = SOURCE.LUL_HARD_TOP,",
        "    TARGET.LUL_ZONAL_TOP = SOURCE.LUL_ZONAL_TOP,",
        "    TARGET.DATA_SOFT_TOP = SOURCE.DATA_SOFT_TOP,",
        "    TARGET.DATA_CRASH_TOP = SOURCE.DATA_CRASH_TOP,",
        "    TARGET.DATA_HARD_TOP = SOURCE.DATA_HARD_TOP,",
        "    TARGET.DATA_ZONAL_TOP = SOURCE.DATA_ZONAL_TOP,",
        "    TARGET.IDC_SOFT_TOP = SOURCE.IDC_SOFT_TOP,",
        "    TARGET.IDC_CRASH_TOP = SOURCE.IDC_CRASH_TOP,",
        "    TARGET.IDC_HARD_TOP = SOURCE.IDC_HARD_TOP,",
        "    TARGET.IDC_ZONAL_TOP = SOURCE.IDC_ZONAL_TOP,",
        "    TARGET.PADWIPE_COUNT = SOURCE.PADWIPE_COUNT,",
        "    TARGET.GLDRULEVER = SOURCE.GLDRULEVER,",
        "    TARGET.GLDCHECKSUM = SOURCE.GLDCHECKSUM",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    BATCHID,",
        "    PRODUCTCODE,",
        "    DISKCODE,",
        "    DISKSEQUENCE,",
        "    GLIDERULE,",
        "    GLIDECODE,",
        "    QCODE,",
        "    TOPLEVEL,",
        "    BOTTOMLEVEL,",
        "    STARTGLIDE,",
        "    STOPGLIDE,",
        "    OFFSET,",
        "    DTINSERT,",
        "    CORRELID,",
        "    LOT,",
        "    UNITID,",
        "    BOT_FULL,",
        "    LUL_SOFT_BOT,",
        "    LUL_CRASH_BOT,",
        "    LUL_HARD_BOT,",
        "    LUL_ZONAL_BOT,",
        "    DATA_SOFT_BOT,",
        "    DATA_CRASH_BOT,",
        "    DATA_HARD_BOT,",
        "    DATA_ZONAL_BOT,",
        "    IDC_SOFT_BOT,",
        "    IDC_CRASH_BOT,",
        "    IDC_HARD_BOT,",
        "    IDC_ZONAL_BOT,",
        "    TOP_FULL,",
        "    LUL_SOFT_TOP,",
        "    LUL_CRASH_TOP,",
        "    LUL_HARD_TOP,",
        "    LUL_ZONAL_TOP,",
        "    DATA_SOFT_TOP,",
        "    DATA_CRASH_TOP,",
        "    DATA_HARD_TOP,",
        "    DATA_ZONAL_TOP,",
        "    IDC_SOFT_TOP,",
        "    IDC_CRASH_TOP,",
        "    IDC_HARD_TOP,",
        "    IDC_ZONAL_TOP,",
        "    PADWIPE_COUNT,",
        "    GLDRULEVER,",
        "    GLDCHECKSUM",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.GLIDENUM,",
        "    SOURCE.BATCHID,",
        "    SOURCE.PRODUCTCODE,",
        "    SOURCE.DISKCODE,",
        "    SOURCE.DISKSEQUENCE,",
        "    SOURCE.GLIDERULE,",
        "    SOURCE.GLIDECODE,",
        "    SOURCE.QCODE,",
        "    SOURCE.TOPLEVEL,",
        "    SOURCE.BOTTOMLEVEL,",
        "    SOURCE.STARTGLIDE,",
        "    SOURCE.STOPGLIDE,",
        "    SOURCE.OFFSET,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.LOT,",
        "    SOURCE.UNITID,",
        "    SOURCE.BOT_FULL,",
        "    SOURCE.LUL_SOFT_BOT,",
        "    SOURCE.LUL_CRASH_BOT,",
        "    SOURCE.LUL_HARD_BOT,",
        "    SOURCE.LUL_ZONAL_BOT,",
        "    SOURCE.DATA_SOFT_BOT,",
        "    SOURCE.DATA_CRASH_BOT,",
        "    SOURCE.DATA_HARD_BOT,",
        "    SOURCE.DATA_ZONAL_BOT,",
        "    SOURCE.IDC_SOFT_BOT,",
        "    SOURCE.IDC_CRASH_BOT,",
        "    SOURCE.IDC_HARD_BOT,",
        "    SOURCE.IDC_ZONAL_BOT,",
        "    SOURCE.TOP_FULL,",
        "    SOURCE.LUL_SOFT_TOP,",
        "    SOURCE.LUL_CRASH_TOP,",
        "    SOURCE.LUL_HARD_TOP,",
        "    SOURCE.LUL_ZONAL_TOP,",
        "    SOURCE.DATA_SOFT_TOP,",
        "    SOURCE.DATA_CRASH_TOP,",
        "    SOURCE.DATA_HARD_TOP,",
        "    SOURCE.DATA_ZONAL_TOP,",
        "    SOURCE.IDC_SOFT_TOP,",
        "    SOURCE.IDC_CRASH_TOP,",
        "    SOURCE.IDC_HARD_TOP,",
        "    SOURCE.IDC_ZONAL_TOP,",
        "    SOURCE.PADWIPE_COUNT,",
        "    SOURCE.GLDRULEVER,",
        "    SOURCE.GLDCHECKSUM",
        "  )"
    })
    void insertOrUpdate(GLDLOG_29 record);
}











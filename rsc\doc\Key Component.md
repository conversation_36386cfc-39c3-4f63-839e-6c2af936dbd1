# Key Components Documentation

## message_mappings.json Guide

### Location
- **File Path**: `src/test/resources/mapper/message_mappings.json`
- **Purpose**: Central configuration file that maps message types to database tables and defines message processing metadata

### Structure
```json
{
  "messageProfiles": [
    {
      "tabCode": "LID",
      "mqMessageId": "309",
      "msgType": "ASCII",
      "tableName": "LID"
    },
    {
      "tabCode": "FTPRAW",
      "mqMessageId": "222",
      "msgType": "ASCII",
      "tableName": "FTP_RAW"
    }
  ]
}
```

### Usage Across Components

#### 1. **Generator Components**
- **ProfileAndMapperGenerator**: Uses this file to identify which tables need code generation
- **PojoAndMapperGenerator**: Reads tabCode and tableName to generate Java POJOs and MyBatis mappers
- **Process Flow**:
  1. Read `message_mappings.json` to get table metadata (tabCode, tableName, mqMessageId)
  2. Use `tabCode` to locate corresponding profile file (e.g., `309_LID.pro`)
  3. Use `tableName` to generate appropriate class names and database mappings
  4. Generate POJO classes and MyBatis mapper interfaces based on table schema

#### 2. **Producer Components**
- **BinaryFileProcessor**: Uses mqMessageId to determine message routing
- **RabbitMQProducerService**: Maps message types to appropriate RabbitMQ queues
- **Process Flow**:
  1. Read file data and determine message type
  2. Look up mqMessageId from message_mappings.json
  3. Route message to correct RabbitMQ queue based on mqMessageId

#### 3. **Consumer Components**
- **RabbitMQConsumer**: Uses mappings to process incoming messages
- **BatchMessageProcessor**: Maps received messages to correct database tables
- **MessageDataMapper**: Uses tabCode and tableName for database operations
- **Process Flow**:
  1. Receive message from RabbitMQ queue
  2. Identify message type using mqMessageId or tabCode
  3. Look up corresponding tableName for database insertion
  4. Use TableSchemaManager to get column definitions for data mapping

### Key Fields Explanation
- **tabCode**: Unique identifier used in ASCII message processing and profile file naming
- **mqMessageId**: Message identifier used in binary message processing and queue routing
- **msgType**: Defines processing type (ASCII or Binary)
- **tableName**: Target database table name for data storage

---

## TableSchemaManager

### Purpose
`TableSchemaManager` is a singleton class that serves as the central schema management system for the MQUpload application. It provides unified access to table definitions, column schemas, and handles lazy loading of profile data.

### Key Responsibilities

#### 1. **Schema Loading and Caching**
- Loads table definitions from `message_mappings.json` during initialization
- Implements lazy loading for column definitions from profile files (.pro files)
- Maintains in-memory cache to avoid repeated file I/O operations
- Thread-safe singleton pattern ensures consistent schema access across the application

#### 2. **Table Definition Management**
- **Primary Access Methods**:
  - `getTableDefinitionFromTabCode(String tabCode)`: For ASCII message processing
  - `getTableDefinitionFromMessageId(String mqMessageId)`: For binary message processing
  - `getTableDefinition(String identifier, boolean isBinary)`: Unified access method

#### 3. **Column Schema Resolution**
- Automatically loads column definitions from profile files when first accessed
- Profile files follow naming convention: `{mqMessageId}_{tabCode}.pro`
- Parses profile files to extract column metadata (name, type, length, nullability, etc.)

### Usage in Application Components

#### **Consumer Components**
```java
// Initialize with configuration paths
TableSchemaManager schemaManager = TableSchemaManager.getInstance(
    consumerProps.getMappingsPath(),
    consumerProps.getProfilesPath()
);

// Get table definition for message processing
TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode("LID");
```

#### **Producer Components**
```java
// Used in BinaryFileProcessor for message type resolution
TableSchemaManager schemaManager = TableSchemaManager.getInstance(
    mappingsPath,
    profilesPath
);
```

#### **Generator Components**
```java
// Used in code generation to get table schemas
TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode(className);
List<ColumnDefinition> columns = tableDef.getColumns();
```

### Performance Features
- **Lazy Loading**: Column definitions loaded only when first accessed
- **Caching**: Once loaded, schemas remain in memory for fast access
- **Thread Safety**: Synchronized access prevents concurrent loading issues
- **Singleton Pattern**: Single instance across application reduces memory usage

---

## MQ_Upload.yml Configuration

### Purpose
`MQ_Upload.yml` is the main configuration file that defines all operational parameters for the MQUpload application, including producer settings, consumer configurations, RabbitMQ connections, and file processing parameters.

### Configuration Sections

#### 1. **Producer Configuration**
```yaml
producer:
  pds_dir: "C:\\opt\\ecs\\storage\\mq\\"           # Source directory for file monitoring
  binary_format: true                              # Message format type
  max_files_in_sending: 10                        # Concurrent file processing limit
  waiting_for_write_ended: 100                    # File stability check delay (ms)
  pack_same_msg_id: false                         # Message batching setting
  to_dir: "C:\\opt\\ecs\\storage\\mq\\sent\\"    # Processed files destination
  db_msg_id_profile: "profiles\\pdsMsgId.pro"    # Message ID mapping profile
  monitor_interval: 10                            # Directory monitoring interval (seconds)
```

#### 2. **RabbitMQ Configuration**
```yaml
rabbitmq:
  host: "csf-md-rabitmq1.ad.shared"              # RabbitMQ server hostname
  port: 5672                                      # Connection port
  username: "mqupload"                            # Authentication username
  password: "upload66"                            # Authentication password
  exchange: "ecs.direct"                          # Default exchange name
  queue_config_path: "src\\test\\resources\\test-queue-config.csv"  # Queue definitions
```

#### 3. **Table Schema Configuration**
```yaml
table_schema:
  message_mappings_path: "src\\test\\resources\\mapper\\message_mappings.json"
  profile_base_path: "profiles\\generatedProfiles"
```

#### 4. **Consumer Configuration**
```yaml
consumer:
  mybatis_config_path: "src\\test\\resources\\mybatis-config.xml"  # Database config
  parser_class_name: "wdc.disk.ecs.apps.MQUpload.parser.AsciiMessageParser"  # Message parser
  mappings_path: "src\\test\\resources\\mapper\\message_mappings.json"
  profiles_path: "profiles\\generatedProfiles"
  queues:                                         # Queue-specific configurations
    - name: "309"                                # Queue identifier
      description: "Queue for 309 processing"   # Human-readable description
      enabled: true                             # Queue processing status
      retry_count: 3                            # Failed message retry attempts
      retry_delay: 1000                         # Delay between retries (ms)
      processing_interval: 1000                 # Processing cycle interval (ms)
      prefetch_count: 100                       # Message prefetch limit
```

### Consumer Queue Functions

#### **Queue Configuration Purpose**
Each queue in the consumer configuration serves specific functions:

1. **Message Type Processing**: Each queue corresponds to a specific message type (identified by mqMessageId)
2. **Processing Control**: Individual enable/disable control for different message types
3. **Error Handling**: Configurable retry mechanisms for failed message processing
4. **Performance Tuning**: Adjustable processing intervals and prefetch counts
5. **Monitoring**: Descriptive names and settings for operational visibility

#### **Queue Processing Flow**
1. **Message Reception**: Consumer listens to configured queues
2. **Type Identification**: Uses queue name to identify message type
3. **Schema Resolution**: Looks up table definition using TableSchemaManager
4. **Data Processing**: Parses message content and maps to database columns
5. **Database Insertion**: Executes SQL operations using MyBatis mappers
6. **Error Handling**: Implements retry logic based on queue configuration

---

## test-queue-config.csv

### Purpose
`test-queue-config.csv` defines RabbitMQ queue metadata and routing configurations used by both producer and consumer components for message routing and queue management.

### File Location
- **Path**: `src/test/resources/test-queue-config.csv`
- **Usage**: Referenced in `MQ_Upload.yml` under `rabbitmq.queue_config_path`

### Structure
```csv
Name,Type,Exchange,ExchangeType,RoutingKey,Durable,Exclusive,AutoDelete,QuorumSize
audit_logs,quorum,ecs.direct,direct,dev_demo,true,false,false,3
error_logs,classic,ecs.topic,topic,error.*,true,false,false,0
```

### Column Definitions
- **Name**: Queue name identifier used in RabbitMQ
- **Type**: Queue type (quorum, classic) determining durability and replication
- **Exchange**: RabbitMQ exchange name for message routing
- **ExchangeType**: Exchange routing algorithm (direct, topic, fanout)
- **RoutingKey**: Message routing pattern for exchange-to-queue binding
- **Durable**: Queue persistence across RabbitMQ server restarts
- **Exclusive**: Queue accessibility (single connection vs shared)
- **AutoDelete**: Automatic queue deletion when no consumers
- **QuorumSize**: Replication factor for quorum queues (0 for classic queues)

### Usage in Application

#### **Producer Components**
- **RabbitMQProducerService**: Loads queue configurations for message routing
- **Message Publishing**: Uses routing keys to direct messages to appropriate queues
- **Queue Declaration**: Ensures queues exist with correct properties before publishing

#### **Consumer Components**
- **RabbitMQConsumer**: Uses queue metadata for consumer setup
- **Queue Binding**: Establishes queue-to-exchange bindings based on routing keys
- **Consumer Configuration**: Sets up message consumption parameters

#### **Configuration Loading**
```java
// Load queue configuration from CSV
RabbitMQMetadataLoader queueConfig = RabbitMQMetadataLoader.loadFromCsv(queueConfigPath);

// Build routing map for message publishing
for (QueueMetadataEntry entry : queueConfig.getEntries()) {
    routingMap.put(entry.getRoutingKey(), entry);
}
```

### Integration with Message Processing
1. **Message Production**: Producer uses routing keys to publish messages to correct queues
2. **Queue Management**: Ensures queues are created with proper durability and replication settings
3. **Consumer Setup**: Consumer uses queue names to establish message consumption
4. **Error Handling**: Error queues configured for failed message processing
5. **Monitoring**: Queue metadata supports operational monitoring and management
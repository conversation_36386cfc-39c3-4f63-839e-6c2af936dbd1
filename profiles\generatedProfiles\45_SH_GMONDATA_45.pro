* Table: DB2SYS.SH_GMONDATA(BINARY format)
*| ColumnName       | DBColType | DBColLen | SysType       | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
| SPINDLE          | SmallInt  | 2        | System.Int16  | 2         | 0     | Y     | 0        | 0        | Y      |
| DISKID           | Integer   | 4        | System.Int32  | 4         | 1     | Y     | 8        | 1        | Y      |
| BATCHTIME        | Timestamp | 4        | System.DateTime| 4        | 2     | Y     | 16       | 2        | Y      |
| SURFACE          | Char      | 1        | System.String | 1         | 3     | Y     | 24       | 3        | Y      |
| RULE             | SmallInt  | 2        | System.Int16  | 2         | 4     | Y     | 28       | 4        | Y      |
| GLIDEHEADID      | Char      | 10       | System.String | 10        | 5     | Y     | 32       | 5        | Y      |
| HCF              | Double    | 8        | System.Double | 8         | 6     |       | 56       | 6        |        |
| METHOD           | Char      | 20       | System.String | 20        | 7     |       | 72       | 7        |        |
| AMP              | Double    | 8        | System.Double | 8         | 8     |       | 112      | 8        |        |
| DEV              | Double    | 8        | System.Double | 8         | 9     |       | 128      | 9        |        |
| GLIDEHITS        | Integer   | 4        | System.Int32  | 4         | 10    |       | 144      | 10       |        |
| BUMPHCF          | Double    | 8        | System.Double | 8         | 11    |       | 152      | 11       |        |
| QCODE            | Integer   | 4        | System.Int32  | 4         | 12    |       | 168      | 12       |        |
| RECSENT          | SmallInt  | 2        | System.Int16  | 2         | 13    |       | 176      | 13       |        |
| DTINSERT         | Timestamp | 4        | System.DateTime| 4        | 14    | Y     | 0        | 14       |        |
| CORRELID         | Integer   | 4        | System.Int32  | 4         | 15    |       | 0        | 15       |        |
| ARM_IDENT        | VarChar   | 8        | System.String | 8         | 16    |       | 180      | 16       |        |
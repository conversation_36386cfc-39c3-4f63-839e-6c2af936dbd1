package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for MISCDATA_24
 */
public class MISCDATA_24  {
    private Short spindle;
    private Integer glidenum;
    private String datatype;
    private Double data1;
    private Double data2;
    private Double data3;
    private Double data4;
    private Integer data5;
    private Integer data6;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public MISCDATA_24() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getGlidenum() {
        return glidenum;
    }

    public void setGlidenum(Integer glidenum) {
        this.glidenum = glidenum;
    }

    public String getDatatype() {
        return datatype;
    }

    public void setDatatype(String datatype) {
        this.datatype = datatype;
    }

    public Double getData1() {
        return data1;
    }

    public void setData1(Double data1) {
        this.data1 = data1;
    }

    public Double getData2() {
        return data2;
    }

    public void setData2(Double data2) {
        this.data2 = data2;
    }

    public Double getData3() {
        return data3;
    }

    public void setData3(Double data3) {
        this.data3 = data3;
    }

    public Double getData4() {
        return data4;
    }

    public void setData4(Double data4) {
        this.data4 = data4;
    }

    public Integer getData5() {
        return data5;
    }

    public void setData5(Integer data5) {
        this.data5 = data5;
    }

    public Integer getData6() {
        return data6;
    }

    public void setData6(Integer data6) {
        this.data6 = data6;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



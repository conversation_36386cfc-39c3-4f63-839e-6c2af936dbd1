package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.DECOGAUGE_26;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for DECOGAUGE_26
 */
public interface DECOGAUGE_26_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.DECOGAUGE AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{spindle},",
        "    #{unitid},",
        "    #{starttime},",
        "    #{stoptime},",
        "    #{datapoint},",
        "    #{surface},",
        "    #{headid},",
        "    #{finaljudgement},",
        "    #{lfMaster},",
        "    #{lfSubmaster},",
        "    #{lfCorrection},",
        "    #{lfLowlimit},",
        "    #{lfHighlimit},",
        "    #{lfJudgement},",
        "    #{hfMaster},",
        "    #{hfSubmaster},",
        "    #{hfCorrection},",
        "    #{hfLowlimit},",
        "    #{hfHighlimit},",
        "    #{hfJudgement},",
        "    #{resMaster},",
        "    #{resSubmaster},",
        "    #{resCorrection},",
        "    #{resLowlimit},",
        "    #{resHighlimit},",
        "    #{resJudgement},",
        "    #{owMaster},",
        "    #{owSubmaster},",
        "    #{owCorrection},",
        "    #{owLowlimit},",
        "    #{owHighlimit},",
        "    #{owJudgement},",
        "    #{cnMaster},",
        "    #{cnSubmaster},",
        "    #{cnCorrection},",
        "    #{cnLowlimit},",
        "    #{cnHighlimit},",
        "    #{cnJudgement},",
        "    #{snMaster},",
        "    #{snSubmaster},",
        "    #{snCorrection},",
        "    #{snLowlimit},",
        "    #{snHighlimit},",
        "    #{snJudgement},",
        "    #{devMaster},",
        "    #{devSubmaster},",
        "    #{devCorrection},",
        "    #{devLowlimit},",
        "    #{devHighlimit},",
        "    #{devJudgement},",
        "    #{pwMaster},",
        "    #{pwSubmaster},",
        "    #{pwCorrection},",
        "    #{pwLowlimit},",
        "    #{pwHighlimit},",
        "    #{pwJudgement},",
        "    #{vasymMaster},",
        "    #{vasymSubmaster},",
        "    #{vasymCorrection},",
        "    #{vasymLowlimit},",
        "    #{vasymHighlimit},",
        "    #{vasymJudgement},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    SPINDLE,",
        "    UNITID,",
        "    STARTTIME,",
        "    STOPTIME,",
        "    DATAPOINT,",
        "    SURFACE,",
        "    HEADID,",
        "    FINALJUDGEMENT,",
        "    LF_MASTER,",
        "    LF_SUBMASTER,",
        "    LF_CORRECTION,",
        "    LF_LOWLIMIT,",
        "    LF_HIGHLIMIT,",
        "    LF_JUDGEMENT,",
        "    HF_MASTER,",
        "    HF_SUBMASTER,",
        "    HF_CORRECTION,",
        "    HF_LOWLIMIT,",
        "    HF_HIGHLIMIT,",
        "    HF_JUDGEMENT,",
        "    RES_MASTER,",
        "    RES_SUBMASTER,",
        "    RES_CORRECTION,",
        "    RES_LOWLIMIT,",
        "    RES_HIGHLIMIT,",
        "    RES_JUDGEMENT,",
        "    OW_MASTER,",
        "    OW_SUBMASTER,",
        "    OW_CORRECTION,",
        "    OW_LOWLIMIT,",
        "    OW_HIGHLIMIT,",
        "    OW_JUDGEMENT,",
        "    CN_MASTER,",
        "    CN_SUBMASTER,",
        "    CN_CORRECTION,",
        "    CN_LOWLIMIT,",
        "    CN_HIGHLIMIT,",
        "    CN_JUDGEMENT,",
        "    SN_MASTER,",
        "    SN_SUBMASTER,",
        "    SN_CORRECTION,",
        "    SN_LOWLIMIT,",
        "    SN_HIGHLIMIT,",
        "    SN_JUDGEMENT,",
        "    DEV_MASTER,",
        "    DEV_SUBMASTER,",
        "    DEV_CORRECTION,",
        "    DEV_LOWLIMIT,",
        "    DEV_HIGHLIMIT,",
        "    DEV_JUDGEMENT,",
        "    PW_MASTER,",
        "    PW_SUBMASTER,",
        "    PW_CORRECTION,",
        "    PW_LOWLIMIT,",
        "    PW_HIGHLIMIT,",
        "    PW_JUDGEMENT,",
        "    VASYM_MASTER,",
        "    VASYM_SUBMASTER,",
        "    VASYM_CORRECTION,",
        "    VASYM_LOWLIMIT,",
        "    VASYM_HIGHLIMIT,",
        "    VASYM_JUDGEMENT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.UNITID = SOURCE.UNITID AND TARGET.STARTTIME = SOURCE.STARTTIME AND TARGET.STOPTIME = SOURCE.STOPTIME AND TARGET.DATAPOINT = SOURCE.DATAPOINT AND TARGET.SURFACE = SOURCE.SURFACE",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.HEADID = SOURCE.HEADID,",
        "    TARGET.FINALJUDGEMENT = SOURCE.FINALJUDGEMENT,",
        "    TARGET.LF_MASTER = SOURCE.LF_MASTER,",
        "    TARGET.LF_SUBMASTER = SOURCE.LF_SUBMASTER,",
        "    TARGET.LF_CORRECTION = SOURCE.LF_CORRECTION,",
        "    TARGET.LF_LOWLIMIT = SOURCE.LF_LOWLIMIT,",
        "    TARGET.LF_HIGHLIMIT = SOURCE.LF_HIGHLIMIT,",
        "    TARGET.LF_JUDGEMENT = SOURCE.LF_JUDGEMENT,",
        "    TARGET.HF_MASTER = SOURCE.HF_MASTER,",
        "    TARGET.HF_SUBMASTER = SOURCE.HF_SUBMASTER,",
        "    TARGET.HF_CORRECTION = SOURCE.HF_CORRECTION,",
        "    TARGET.HF_LOWLIMIT = SOURCE.HF_LOWLIMIT,",
        "    TARGET.HF_HIGHLIMIT = SOURCE.HF_HIGHLIMIT,",
        "    TARGET.HF_JUDGEMENT = SOURCE.HF_JUDGEMENT,",
        "    TARGET.RES_MASTER = SOURCE.RES_MASTER,",
        "    TARGET.RES_SUBMASTER = SOURCE.RES_SUBMASTER,",
        "    TARGET.RES_CORRECTION = SOURCE.RES_CORRECTION,",
        "    TARGET.RES_LOWLIMIT = SOURCE.RES_LOWLIMIT,",
        "    TARGET.RES_HIGHLIMIT = SOURCE.RES_HIGHLIMIT,",
        "    TARGET.RES_JUDGEMENT = SOURCE.RES_JUDGEMENT,",
        "    TARGET.OW_MASTER = SOURCE.OW_MASTER,",
        "    TARGET.OW_SUBMASTER = SOURCE.OW_SUBMASTER,",
        "    TARGET.OW_CORRECTION = SOURCE.OW_CORRECTION,",
        "    TARGET.OW_LOWLIMIT = SOURCE.OW_LOWLIMIT,",
        "    TARGET.OW_HIGHLIMIT = SOURCE.OW_HIGHLIMIT,",
        "    TARGET.OW_JUDGEMENT = SOURCE.OW_JUDGEMENT,",
        "    TARGET.CN_MASTER = SOURCE.CN_MASTER,",
        "    TARGET.CN_SUBMASTER = SOURCE.CN_SUBMASTER,",
        "    TARGET.CN_CORRECTION = SOURCE.CN_CORRECTION,",
        "    TARGET.CN_LOWLIMIT = SOURCE.CN_LOWLIMIT,",
        "    TARGET.CN_HIGHLIMIT = SOURCE.CN_HIGHLIMIT,",
        "    TARGET.CN_JUDGEMENT = SOURCE.CN_JUDGEMENT,",
        "    TARGET.SN_MASTER = SOURCE.SN_MASTER,",
        "    TARGET.SN_SUBMASTER = SOURCE.SN_SUBMASTER,",
        "    TARGET.SN_CORRECTION = SOURCE.SN_CORRECTION,",
        "    TARGET.SN_LOWLIMIT = SOURCE.SN_LOWLIMIT,",
        "    TARGET.SN_HIGHLIMIT = SOURCE.SN_HIGHLIMIT,",
        "    TARGET.SN_JUDGEMENT = SOURCE.SN_JUDGEMENT,",
        "    TARGET.DEV_MASTER = SOURCE.DEV_MASTER,",
        "    TARGET.DEV_SUBMASTER = SOURCE.DEV_SUBMASTER,",
        "    TARGET.DEV_CORRECTION = SOURCE.DEV_CORRECTION,",
        "    TARGET.DEV_LOWLIMIT = SOURCE.DEV_LOWLIMIT,",
        "    TARGET.DEV_HIGHLIMIT = SOURCE.DEV_HIGHLIMIT,",
        "    TARGET.DEV_JUDGEMENT = SOURCE.DEV_JUDGEMENT,",
        "    TARGET.PW_MASTER = SOURCE.PW_MASTER,",
        "    TARGET.PW_SUBMASTER = SOURCE.PW_SUBMASTER,",
        "    TARGET.PW_CORRECTION = SOURCE.PW_CORRECTION,",
        "    TARGET.PW_LOWLIMIT = SOURCE.PW_LOWLIMIT,",
        "    TARGET.PW_HIGHLIMIT = SOURCE.PW_HIGHLIMIT,",
        "    TARGET.PW_JUDGEMENT = SOURCE.PW_JUDGEMENT,",
        "    TARGET.VASYM_MASTER = SOURCE.VASYM_MASTER,",
        "    TARGET.VASYM_SUBMASTER = SOURCE.VASYM_SUBMASTER,",
        "    TARGET.VASYM_CORRECTION = SOURCE.VASYM_CORRECTION,",
        "    TARGET.VASYM_LOWLIMIT = SOURCE.VASYM_LOWLIMIT,",
        "    TARGET.VASYM_HIGHLIMIT = SOURCE.VASYM_HIGHLIMIT,",
        "    TARGET.VASYM_JUDGEMENT = SOURCE.VASYM_JUDGEMENT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    SPINDLE,",
        "    UNITID,",
        "    STARTTIME,",
        "    STOPTIME,",
        "    DATAPOINT,",
        "    SURFACE,",
        "    HEADID,",
        "    FINALJUDGEMENT,",
        "    LF_MASTER,",
        "    LF_SUBMASTER,",
        "    LF_CORRECTION,",
        "    LF_LOWLIMIT,",
        "    LF_HIGHLIMIT,",
        "    LF_JUDGEMENT,",
        "    HF_MASTER,",
        "    HF_SUBMASTER,",
        "    HF_CORRECTION,",
        "    HF_LOWLIMIT,",
        "    HF_HIGHLIMIT,",
        "    HF_JUDGEMENT,",
        "    RES_MASTER,",
        "    RES_SUBMASTER,",
        "    RES_CORRECTION,",
        "    RES_LOWLIMIT,",
        "    RES_HIGHLIMIT,",
        "    RES_JUDGEMENT,",
        "    OW_MASTER,",
        "    OW_SUBMASTER,",
        "    OW_CORRECTION,",
        "    OW_LOWLIMIT,",
        "    OW_HIGHLIMIT,",
        "    OW_JUDGEMENT,",
        "    CN_MASTER,",
        "    CN_SUBMASTER,",
        "    CN_CORRECTION,",
        "    CN_LOWLIMIT,",
        "    CN_HIGHLIMIT,",
        "    CN_JUDGEMENT,",
        "    SN_MASTER,",
        "    SN_SUBMASTER,",
        "    SN_CORRECTION,",
        "    SN_LOWLIMIT,",
        "    SN_HIGHLIMIT,",
        "    SN_JUDGEMENT,",
        "    DEV_MASTER,",
        "    DEV_SUBMASTER,",
        "    DEV_CORRECTION,",
        "    DEV_LOWLIMIT,",
        "    DEV_HIGHLIMIT,",
        "    DEV_JUDGEMENT,",
        "    PW_MASTER,",
        "    PW_SUBMASTER,",
        "    PW_CORRECTION,",
        "    PW_LOWLIMIT,",
        "    PW_HIGHLIMIT,",
        "    PW_JUDGEMENT,",
        "    VASYM_MASTER,",
        "    VASYM_SUBMASTER,",
        "    VASYM_CORRECTION,",
        "    VASYM_LOWLIMIT,",
        "    VASYM_HIGHLIMIT,",
        "    VASYM_JUDGEMENT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.UNITID,",
        "    SOURCE.STARTTIME,",
        "    SOURCE.STOPTIME,",
        "    SOURCE.DATAPOINT,",
        "    SOURCE.SURFACE,",
        "    SOURCE.HEADID,",
        "    SOURCE.FINALJUDGEMENT,",
        "    SOURCE.LF_MASTER,",
        "    SOURCE.LF_SUBMASTER,",
        "    SOURCE.LF_CORRECTION,",
        "    SOURCE.LF_LOWLIMIT,",
        "    SOURCE.LF_HIGHLIMIT,",
        "    SOURCE.LF_JUDGEMENT,",
        "    SOURCE.HF_MASTER,",
        "    SOURCE.HF_SUBMASTER,",
        "    SOURCE.HF_CORRECTION,",
        "    SOURCE.HF_LOWLIMIT,",
        "    SOURCE.HF_HIGHLIMIT,",
        "    SOURCE.HF_JUDGEMENT,",
        "    SOURCE.RES_MASTER,",
        "    SOURCE.RES_SUBMASTER,",
        "    SOURCE.RES_CORRECTION,",
        "    SOURCE.RES_LOWLIMIT,",
        "    SOURCE.RES_HIGHLIMIT,",
        "    SOURCE.RES_JUDGEMENT,",
        "    SOURCE.OW_MASTER,",
        "    SOURCE.OW_SUBMASTER,",
        "    SOURCE.OW_CORRECTION,",
        "    SOURCE.OW_LOWLIMIT,",
        "    SOURCE.OW_HIGHLIMIT,",
        "    SOURCE.OW_JUDGEMENT,",
        "    SOURCE.CN_MASTER,",
        "    SOURCE.CN_SUBMASTER,",
        "    SOURCE.CN_CORRECTION,",
        "    SOURCE.CN_LOWLIMIT,",
        "    SOURCE.CN_HIGHLIMIT,",
        "    SOURCE.CN_JUDGEMENT,",
        "    SOURCE.SN_MASTER,",
        "    SOURCE.SN_SUBMASTER,",
        "    SOURCE.SN_CORRECTION,",
        "    SOURCE.SN_LOWLIMIT,",
        "    SOURCE.SN_HIGHLIMIT,",
        "    SOURCE.SN_JUDGEMENT,",
        "    SOURCE.DEV_MASTER,",
        "    SOURCE.DEV_SUBMASTER,",
        "    SOURCE.DEV_CORRECTION,",
        "    SOURCE.DEV_LOWLIMIT,",
        "    SOURCE.DEV_HIGHLIMIT,",
        "    SOURCE.DEV_JUDGEMENT,",
        "    SOURCE.PW_MASTER,",
        "    SOURCE.PW_SUBMASTER,",
        "    SOURCE.PW_CORRECTION,",
        "    SOURCE.PW_LOWLIMIT,",
        "    SOURCE.PW_HIGHLIMIT,",
        "    SOURCE.PW_JUDGEMENT,",
        "    SOURCE.VASYM_MASTER,",
        "    SOURCE.VASYM_SUBMASTER,",
        "    SOURCE.VASYM_CORRECTION,",
        "    SOURCE.VASYM_LOWLIMIT,",
        "    SOURCE.VASYM_HIGHLIMIT,",
        "    SOURCE.VASYM_JUDGEMENT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(DECOGAUGE_26 record);
}











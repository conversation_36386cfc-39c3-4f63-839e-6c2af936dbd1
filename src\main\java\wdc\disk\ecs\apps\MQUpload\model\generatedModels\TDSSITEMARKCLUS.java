package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSSITEMARKCLUS
 */
public class TDSSITEMARKCLUS  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private Integer spindle;
    private String channelId;
    private Short clusterId;
    private Double midRadiusMm;
    private Double midAngleDeg;
    private String type;

    // Default constructor
    public TDSSITEMARKCLUS() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Short getClusterId() {
        return clusterId;
    }

    public void setClusterId(Short clusterId) {
        this.clusterId = clusterId;
    }

    public Double getMidRadiusMm() {
        return midRadiusMm;
    }

    public void setMidRadiusMm(Double midRadiusMm) {
        this.midRadiusMm = midRadiusMm;
    }

    public Double getMidAngleDeg() {
        return midAngleDeg;
    }

    public void setMidAngleDeg(Double midAngleDeg) {
        this.midAngleDeg = midAngleDeg;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

}



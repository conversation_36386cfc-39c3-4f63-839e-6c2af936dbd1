package wdc.disk.ecs.apps.MQUpload.thread;

import java.io.File;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.List;
import ibm.disk.utility.LogStream;

/**
 * A runnable task that performs housekeeping by deleting old files from specified directories.
 */
public class HousekeepingThread implements Runnable {

    private final LogStream log;
    private final List<String> directoriesToClean;
    private final int retentionDays;
    private final String baseDirectory;

    public HousekeepingThread(String baseDirectory, List<String> directoriesToClean, int retentionDays, LogStream log) {
        this.baseDirectory = baseDirectory;
        this.directoriesToClean = directoriesToClean;
        this.retentionDays = retentionDays;
        this.log = log;
    }

    @Override
    public void run() {
        log.write(LogStream.INFORMATION_MESSAGE, "Starting housekeeping task...");
        Instant retentionDate = Instant.now().minus(retentionDays, ChronoUnit.DAYS);

        for (String dirName : directoriesToClean) {
            File directory = new File(baseDirectory, dirName);
            if (!directory.exists() || !directory.isDirectory()) {
                log.write(LogStream.WARNING_MESSAGE, "Housekeeping directory does not exist or is not a directory: " + directory.getAbsolutePath());
                continue;
            }
            
            log.write(LogStream.INFORMATION_MESSAGE, "Cleaning up directory: " + directory.getAbsolutePath());
            File[] files = directory.listFiles();
            if (files == null) continue;

            for (File file : files) {
                if (file.isFile() && file.lastModified() < retentionDate.toEpochMilli() && file.delete()) {
                    log.write(LogStream.DEBUG_MESSAGE, "Deleted old file: " + file.getName());
                }
            }
        }
        log.write(LogStream.INFORMATION_MESSAGE, "Housekeeping task finished.");
    }

	public LogStream getLog() {
		return log;
	}

	public List<String> getDirectoriesToClean() {
		return directoriesToClean;
	}

	public int getRetentionDays() {
		return retentionDays;
	}

	public String getBaseDirectory() {
		return baseDirectory;
	}
    
    
}
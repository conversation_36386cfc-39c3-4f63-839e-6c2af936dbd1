# 动态环境配置指南

本文档解释了应用程序如何通过单一的 `MQ_Upload.yml` 配置文件来管理不同环境（如 `DEV` 和 `PROD`）的配置。

## 核心机制

系统的核心思想是：**在启动应用程序时，通过一个外部参数来指定当前环境，程序代码随后根据这个环境标识，从YAML文件中选择并加载相应的配置信息。**

这使得所有环境的配置可以集中管理，同时保持了部署的灵活性。

---

## 实现步骤

### 第一步：确定当前环境

这是整个机制的起点。`ConfigurationLoader.java` 类在初始化时会确定当前环境。它遵循以下优先级顺序：

1.  **Java 系统属性 (System Property)**：程序会首先检查是否在启动时通过 `-D` 参数传入了 `app.environment`。这是推荐的设置方式。
    ```shell
    # 示例：以 PROD 环境启动应用
    java -Dapp.environment=PROD -jar MQUpload.jar
    ```

2.  **操作系统环境变量 (Environment Variable)**：如果未设置上述系统属性，程序会尝试读取名为 `ENVIRONMENT` 的操作系统环境变量。

3.  **默认值 (Default)**：如果以上两种方式都未指定环境，程序将默认使用 `DEV` 环境。

这个最终确定的环境字符串（例如 "DEV" 或 "PROD"）将被用于后续所有配置的选择。

### 第二步：解析并应用 YAML 配置

程序使用 `SnakeYAML` 库将 `MQ_Upload.yml` 文件完整加载。然后，根据上一步获得的环境标识，通过以下两种策略来解析和应用配置：

#### 策略一：直接选择环境配置块

对于像 RabbitMQ 这样在不同环境间有显著差异的配置，YAML 文件中会定义完全独立的环境块。

**示例 (`MQ_Upload.yml`):**
```yaml
rabbitmq:
  DEV:
    host: "dev-rabbitmq.example.com"
    # ... 其他 DEV 配置
  PROD:
    host: "prod-rabbitmq.example.com"
    # ... 其他 PROD 配置
```

`ConfigurationLoader.java` 会检查 `rabbitmq` 配置下是否存在一个与当前环境同名的键（`DEV` 或 `PROD`），并直接加载该键下的所有配置项。

#### 策略二：动态路径拼接

这是更常用的一种策略，主要用于处理文件路径。它通过定义“基础路径变量”和“相对路径”来实现动态组合。

**示例 (`MQ_Upload.yml`):**
```yaml
# 1. 定义不同环境的基础路径
environments:
  DEV:
    mq_storage_root: "C:\\opt\\ecs\\storage\\mq"
    app_root: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao"
  PROD:
    mq_storage_root: "C:\\opt\\ecs\\storage\\mq\\prod"
    app_root: "C:\\opt\\ecs\\apps\\mqupload"

# 2. 在具体配置中引用基础路径
producer:
  to_dir:
    base: mq_storage_root  # 引用 'environments' 中定义的变量
    relative: "sent"
```

**处理流程:** 
1.  `ConfigurationLoader.java` 首先根据当前环境（例如 `PROD`），加载 `environments.PROD` 块下的所有键值对（`mq_storage_root` 和 `app_root`）。
2.  当解析 `producer.to_dir` 时，它看到 `base: mq_storage_root`。
3.  它使用 `mq_storage_root` 作为键，在已加载的环境变量中查找对应的值，得到 `C:\opt\ecs\storage\mq\prod`。
4.  最后，将这个基础路径与 `relative` 的值（`"sent"`）拼接起来，形成最终的绝对路径 `C:\opt\ecs\storage\mq\prod\sent`。

---

## 总结

1.  **启动时**通过 `-Dapp.environment=PROD` 指定环境。
2.  **程序**获取环境标识 "PROD"。
3.  **程序**加载 `MQ_Upload.yml`。
4.  **对于 `rabbitmq`**，程序直接选取 `rabbitmq.PROD` 下的配置。
5.  **对于文件路径**，程序先从 `environments.PROD` 获取基础路径，再与具体配置中的相对路径拼接，形成最终路径。

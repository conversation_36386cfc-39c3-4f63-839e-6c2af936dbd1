package wdc.disk.ecs.apps.MQUpload.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import org.junit.jupiter.api.Test;

import wdc.disk.ecs.apps.MQUpload.model.ConfigurationProperties;

public class ConfigurationPropertiesTest {

    @Test
    void testConfigurationPropertiesGettersAndSetters() {
        ConfigurationProperties config = new ConfigurationProperties();

        // Test default values
        assertEquals("./pds", config.getPdsDir());
        assertFalse(config.isBinaryFormat());
        assertEquals(60, config.getMonitorInterval());
        assertEquals("localhost", config.getRabbitMQHost());
        assertEquals(5672, config.getRabbitMQPort());
        assertEquals("guest", config.getRabbitMQUsername());
        assertEquals("guest", config.getRabbitMQPassword());
        assertEquals(10, config.getMaxFilesInSending());
        assertEquals(5000, config.getWaitingForWriteEnded());
        assertFalse(config.isPackSameMsgIdData());
        assertEquals("./sent", config.getToDir());
        assertNull(config.getDbMsgIDProfilePath());


        // Test setting new values
        config.setPdsDir("/path/to/pds");
        config.setBinaryFormat(true);
        config.setMonitorInterval(120);
        config.setRabbitMQHost("myhost");
        config.setRabbitMQPort(5673);
        config.setRabbitMQUsername("user");
        config.setRabbitMQPassword("password");
        config.setMaxFilesInSending(20);
        config.setWaitingForWriteEnded(10000);
        config.setPackSameMsgIdData(true);
        config.setToDir("/path/to/to");
        config.setDbMsgIDProfilePath("profilePath");


        assertEquals("/path/to/pds", config.getPdsDir());
        assertTrue(config.isBinaryFormat());
        assertEquals(120, config.getMonitorInterval());
        assertEquals("myhost", config.getRabbitMQHost());
        assertEquals(5673, config.getRabbitMQPort());
        assertEquals("user", config.getRabbitMQUsername());
        assertEquals("password", config.getRabbitMQPassword());
        assertEquals(20, config.getMaxFilesInSending());
        assertEquals(10000, config.getWaitingForWriteEnded());
        assertTrue(config.isPackSameMsgIdData());
        assertEquals("/path/to/to", config.getToDir());
        assertEquals("profilePath", config.getDbMsgIDProfilePath());
    }
}

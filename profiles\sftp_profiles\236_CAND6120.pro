*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  MEASURE_TYPE          |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  SUBSTRATE             |  VarChar     |  30     |  System.String         |  30     |  3      |         |  0      |  3      |  
|  CASSETTE              |  Char        |  11     |  System.String         |  11     |  4      |         |  0      |  4      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  5      |         |  0      |  5      |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  6      |         |  0      |  6      |  
|  SIDEIND               |  Char        |  1      |  System.String         |  1      |  7      |         |  0      |  7      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  8      |         |  0      |  8      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  9      |         |  0      |  9      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  10     |         |  0      |  10     |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  11     |         |  0      |  11     |  
|  MEAS_TIME             |  Timestamp   |  4      |  System.DateTime       |  4      |  12     |         |  0      |  12     |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  13     |         |  0      |  13     |  
|  RAIL                  |  Char        |  1      |  System.String         |  1      |  14     |         |  0      |  14     |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  15     |         |  0      |  15     |  
|  STATUS                |  Char        |  10     |  System.String         |  10     |  16     |         |  0      |  16     |  
|  PROGRAM_NAME          |  VarChar     |  40     |  System.String         |  40     |  17     |         |  0      |  17     |  
|  SEQUENCE_NAME         |  VarChar     |  40     |  System.String         |  40     |  18     |         |  0      |  18     |  
|  TEXT_URI              |  VarChar     |  255    |  System.String         |  255    |  19     |         |  0      |  19     |  
|  IMAGE_URI             |  VarChar     |  255    |  System.String         |  255    |  20     |         |  0      |  20     |  

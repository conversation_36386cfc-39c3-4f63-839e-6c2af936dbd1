package wdc.disk.ecs.apps.SFTPSend.thread;

import static org.junit.jupiter.api.Assertions.*;

import java.io.File;
import java.io.IOException;
import java.util.Date;

import org.junit.jupiter.api.Test;

import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.SftpException;

import wdc.disk.ecs.apps.SFTPSend.util.SendMail;
import wdc.disk.ecs.apps.SFTPSend.util.SendWarningMsgEntity;

public class TestMonitorStatusThread {
		@Test
		public void testThread() throws InterruptedException {
			Thread thread = new Thread(new MonitorSftpSendRunnable());
			thread.setName("Monitor Send Status Thread");
			thread.start();
			Thread.sleep(100*1000);
		}
		
		@Test
		public void testTimeOut(){
			MonitorSftpSendRunnable run = new MonitorSftpSendRunnable();
			Date now = new Date();
			long time = now.getTime();
			boolean result = run.isTimeout(100, time);
			assertFalse(result);
			System.out.println("time out:" + result);
		}
		
		
//		@Test
//		public void testReadConfigure() throws IOException {
//			SendMail sendMail = new SendMail("C:\\Users\\<USER>\\Desktop\\MonitorTalend.ini");
//			sendMail.writeToFile(sendMail.getToWho(), sendMail.getLocalHost());
//		}
		
		@Test
		public void testSent() throws IOException, JSchException, SftpException {
			SendMail sendMail = new SendMail("C:\\Users\\<USER>\\Desktop\\MonitorTalend.ini");
//			sendMail.sentFileToMailServer();
		}
		
		@Test
		public void testReadMoniProfile() {
			MonitorTalendStatusRunnable run = new MonitorTalendStatusRunnable("C:\\Users\\<USER>\\Desktop\\MonitorTalend.ini");
			System.out.println("pdsfile:" + run.pdsFileDirString);
			System.out.println("sent:" + run.sentDirString);
			System.out.println("limit:"+ run.unprocessLimit);
			System.out.println("timeout:" + run.timeoutSeconds);
			System.out.println("Log:" + run.log);
		}
		
		@Test
		public void testOldestLastMofify() {
			MonitorTalendStatusRunnable run = new MonitorTalendStatusRunnable();
			File dir = new File("C:\\ECS_FTP\\InsertedArchive\\POC\\TEST\\282");
			File findOldestLastModifyFile = run.findOldestLastModifyFile(dir);
			System.out.println("file :" + findOldestLastModifyFile.getAbsolutePath());
		}
		
		@Test
		public void testRunThread() {
			MonitorTalendStatusRunnable run = new MonitorTalendStatusRunnable();
			run.run();
		}
		
//		@Test
//		public void testOneFolder() {
//			MonitorTalendStatusRunnable run = new MonitorTalendStatusRunnable();
//			run.processForMsgFolder("C:\\Users\\<USER>\\Desktop\\test\\345", 3600,3);
//		}
//		
//		@Test
//		public void testMultiFolder() {
//			MonitorTalendStatusRunnable run = new MonitorTalendStatusRunnable();
//			run.processForMsgFolder("C:\\Users\\<USER>\\Desktop\\test\\567", 3600,3);
//		}

		@Test
		public void testMonitorFolder() {
			MonitorTalendStatusRunnable run = new MonitorTalendStatusRunnable("C:\\Users\\<USER>\\Desktop\\MonitorTalend.ini");
//			run.processForMsgFolder("C:\\Users\\<USER>\\Desktop\\test\\567", 3600,3);
			run.run();
		}
		
		@Test
		public void testSendWarnMsgEntity() {
			SendWarningMsgEntity entity = new SendWarningMsgEntity("123", SendWarningMsgEntity.Type.fileTimeout, "no comment");
			System.out.println(entity);
		}
		
		@Test
		public void testFolderPath() {
			File file  = new File("C:\\Users\\<USER>\\Desktop\\MonitorTalend.ini");
			System.out.println(file.getParent());
		}
		
}

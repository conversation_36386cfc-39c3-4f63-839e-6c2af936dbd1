package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for GLDRSLT_2
 */
public class GLDRSLT_2  {
    private Short spindle;
    private Integer glidenum;
    private Short detectortype;
    private Double radius;
    private Double angle;
    private Short hits;
    private Double pospeak;
    private Double negpeak;
    private Short band;
    private Integer rejcode;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public GLDRSLT_2() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getGlidenum() {
        return glidenum;
    }

    public void setGlidenum(Integer glidenum) {
        this.glidenum = glidenum;
    }

    public Short getDetectortype() {
        return detectortype;
    }

    public void setDetectortype(Short detectortype) {
        this.detectortype = detectortype;
    }

    public Double getRadius() {
        return radius;
    }

    public void setRadius(Double radius) {
        this.radius = radius;
    }

    public Double getAngle() {
        return angle;
    }

    public void setAngle(Double angle) {
        this.angle = angle;
    }

    public Short getHits() {
        return hits;
    }

    public void setHits(Short hits) {
        this.hits = hits;
    }

    public Double getPospeak() {
        return pospeak;
    }

    public void setPospeak(Double pospeak) {
        this.pospeak = pospeak;
    }

    public Double getNegpeak() {
        return negpeak;
    }

    public void setNegpeak(Double negpeak) {
        this.negpeak = negpeak;
    }

    public Short getBand() {
        return band;
    }

    public void setBand(Short band) {
        this.band = band;
    }

    public Integer getRejcode() {
        return rejcode;
    }

    public void setRejcode(Integer rejcode) {
        this.rejcode = rejcode;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



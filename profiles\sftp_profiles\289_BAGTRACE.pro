*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  CASSETTE              |  Char        |  11     |  System.String         |  11     |  2      |  Y      |  0      |  2      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  3      |  Y      |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |         |  0      |  4      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  5      |         |  0      |  5      |  
|  PARTS                 |  Char        |  4      |  System.String         |  4      |  6      |         |  0      |  6      |  
|  MATERIAL              |  Char        |  4      |  System.String         |  4      |  7      |         |  0      |  7      |  
|  SUPPLIER              |  Char        |  4      |  System.String         |  4      |  8      |         |  0      |  8      |  
|  SIZE                  |  Char        |  8      |  System.String         |  8      |  9      |         |  0      |  9      |  
|  PO                    |  Char        |  40     |  System.String         |  40     |  10     |         |  0      |  10     |  
|  SUPPLIER_LOT          |  Char        |  40     |  System.String         |  40     |  11     |  Y      |  0      |  11     |  
|  SUPPLIER_MACHINE      |  Char        |  4      |  System.String         |  4      |  12     |         |  0      |  12     |  
|  SUPPLIER_MFG_DATE     |  Char        |  10     |  System.String         |  10     |  13     |         |  0      |  13     |  
|  PACKAGE_NUMBER        |  Char        |  20     |  System.String         |  20     |  14     |         |  0      |  14     |  
|  BARCODE               |  Char        |  100    |  System.String         |  100    |  15     |  Y      |  0      |  15     |  
|  CUTTING_MACHINE       |  Char        |  10     |  System.String         |  10     |  16     |         |  0      |  16     |  

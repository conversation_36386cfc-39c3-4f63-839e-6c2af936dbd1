package wdc.disk.ecs.apps.SFTPSend.thread;

import java.io.File;

public class TestSmb {
	public static void main(String[] args) {
		String url;
		String userid = "<EMAIL>";
		String password = "CSF@2021send";
		String networkName ="csfsvmip01a-419-d01.wdc.com";
		String path = "CSF_Talend_Share";
		url = "smb://"+userid+":"+password+"@"+networkName+"/"+path;
		
		String str = "C:\\Users\\<USER>\\Desktop\\initSendMail";
		File file = new File(str);
		System.out.println("exist : "+file.exists());
		String dest = "\\\\csfsvmip01a-419-d01.wdc.com\\CSF_Talend_Share\\init";
		boolean renameTo = file.renameTo(new File(dest));
		System.out.println(renameTo);
	}
}

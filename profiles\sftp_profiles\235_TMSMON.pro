*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |         |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  3      |         |  0      |  3      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  4      |         |  0      |  4      |  
|  REFNO                 |  Char        |  15     |  System.String         |  15     |  5      |         |  0      |  5      |  
|  LOT_TYPE              |  Char        |  1      |  System.String         |  1      |  6      |         |  0      |  6      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  7      |         |  0      |  7      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  8      |         |  0      |  8      |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  9      |         |  0      |  9      |  
|  TMSNAME               |  Char        |  10     |  System.String         |  10     |  10     |         |  0      |  10     |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  11     |         |  0      |  11     |  
|  SEQ_NAME              |  VarChar     |  40     |  System.String         |  40     |  12     |         |  0      |  12     |  
|  DT_MSRMT              |  Timestamp   |  4      |  System.DateTime       |  4      |  13     |         |  0      |  13     |  
|  SIDE                  |  Char        |  1      |  System.String         |  1      |  14     |         |  0      |  14     |  
|  RMS_AVG_DELTA         |  Decimal     |  8      |  System.Decimal        |  8      |  15     |         |  0      |  15     |  
|  RMS_AVG               |  Decimal     |  8      |  System.Decimal        |  8      |  16     |         |  0      |  16     |  
|  RMS_RANGE             |  Decimal     |  8      |  System.Decimal        |  8      |  17     |         |  0      |  17     |  
|  RMS_STDDEV            |  Decimal     |  8      |  System.Decimal        |  8      |  18     |         |  0      |  18     |  
|  RMS_ID                |  Decimal     |  8      |  System.Decimal        |  8      |  19     |         |  0      |  19     |  
|  RMS_ID_RANGE          |  Decimal     |  8      |  System.Decimal        |  8      |  20     |         |  0      |  20     |  
|  RMS_ID_STDDEV         |  Decimal     |  8      |  System.Decimal        |  8      |  21     |         |  0      |  21     |  
|  RMS_MD                |  Decimal     |  8      |  System.Decimal        |  8      |  22     |         |  0      |  22     |  
|  RMS_MD_RANGE          |  Decimal     |  8      |  System.Decimal        |  8      |  23     |         |  0      |  23     |  
|  RMS_MD_STDDEV         |  Decimal     |  8      |  System.Decimal        |  8      |  24     |         |  0      |  24     |  
|  RMS_OD                |  Decimal     |  8      |  System.Decimal        |  8      |  25     |         |  0      |  25     |  
|  RMS_OD_RANGE          |  Decimal     |  8      |  System.Decimal        |  8      |  26     |         |  0      |  26     |  

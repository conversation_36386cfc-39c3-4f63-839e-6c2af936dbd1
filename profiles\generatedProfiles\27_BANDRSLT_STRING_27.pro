* Table: DB2SYS.BANDRSLT_STRING(BINARY format)
*| ColumnName       | DBColType | DBColLen | SysType       | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
| LOT              | Char      | 10       | System.String | 10        | 0     | Y     | 0        | 0        |        |
| UNITID           | Char      | 12       | System.String | 12        | 1     | Y     | 22       | 1        |        |
| SPINDLE          | SmallInt  | 2        | System.Int16  | 2         | 2     | Y     | 48       | 2        | Y      |
| TESTNUM          | Integer   | 4        | System.Int32  | 4         | 3     | Y     | 56       | 3        | Y      |
| RESULTS          | VarChar   | 1500     | System.String | 1500      | 4     | Y     | 64       | 4        |        |
| DTINSERT         | Timestamp | 4        | System.DateTime| 4        | 5     | Y     | 0        | 5        |        |
| CORRELID         | Integer   | 4        | System.Int32  | 4         | 6     | Y     | 0        | 6        |        |
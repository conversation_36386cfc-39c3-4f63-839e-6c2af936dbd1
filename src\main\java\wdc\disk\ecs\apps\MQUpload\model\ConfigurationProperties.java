package wdc.disk.ecs.apps.MQUpload.model;

public class ConfigurationProperties {
    // File monitoring configurations
    private String pdsDir = "./pds"; // Default PDS directory
    private boolean binaryFormat = false; // Default to ASCII format

    // RabbitMQ configurations
    private int monitorInterval = 60; // Default 60 seconds
    private String rabbitMQHost = "localhost"; // Default host
    private int rabbitMQPort = 5672; // Default AMQP port
    private String rabbitMQUsername = "guest"; // Default username
    private String rabbitMQPassword = "guest"; // Default password
    private String rabbitMQVirtualHost = "/";  // Default value
    
    public String getRabbitMQVirtualHost() {
        return rabbitMQVirtualHost;
    }
    
    public void setRabbitMQVirtualHost(String rabbitMQVirtualHost) {
        this.rabbitMQVirtualHost = rabbitMQVirtualHost != null ? rabbitMQVirtualHost : "/";
    }
    
    // File processor configurations
    private int maxFilesInSending = 10; // Default max files in sending directory
    private long waitingForWriteEnded = 5000; // Default waiting time in milliseconds
    private boolean packSameMsgIdData = false; // Default pack same message ID setting
    private String toDir = "./sent";      
    
 
    
    // Constructor
    public ConfigurationProperties() {
    }
    
    // Getters
    public String getPdsDir() { return pdsDir; }
    public boolean isBinaryFormat() { return binaryFormat; }
    public int getMonitorInterval() { return monitorInterval; }
    public String getRabbitMQHost() { return rabbitMQHost; }
    public int getRabbitMQPort() { return rabbitMQPort; }
    public String getRabbitMQUsername() { return rabbitMQUsername; }
    public String getRabbitMQPassword() { return rabbitMQPassword; }
    public int getMaxFilesInSending() { return maxFilesInSending; }
    public long getWaitingForWriteEnded() { return waitingForWriteEnded; }
    public boolean isPackSameMsgIdData() { return packSameMsgIdData; }
    
    // Setters
    public void setPdsDir(String pdsDir) { this.pdsDir = pdsDir; }
    public void setBinaryFormat(boolean binaryFormat) { this.binaryFormat = binaryFormat; }
    public void setMonitorInterval(int monitorInterval) { this.monitorInterval = monitorInterval; }
    public void setRabbitMQHost(String rabbitMQHost) { this.rabbitMQHost = rabbitMQHost; }
    public void setRabbitMQPort(int rabbitMQPort) { this.rabbitMQPort = rabbitMQPort; }
    public void setRabbitMQUsername(String rabbitMQUsername) { this.rabbitMQUsername = rabbitMQUsername; }
    public void setRabbitMQPassword(String rabbitMQPassword) { this.rabbitMQPassword = rabbitMQPassword; }
    public void setMaxFilesInSending(int maxFilesInSending) { this.maxFilesInSending = maxFilesInSending; }
    public void setWaitingForWriteEnded(long waitingForWriteEnded) { this.waitingForWriteEnded = waitingForWriteEnded; }
    public void setPackSameMsgIdData(boolean packSameMsgIdData) { this.packSameMsgIdData = packSameMsgIdData; }
    
    private String dbMsgIDProfilePath;
    public String getDbMsgIDProfilePath() { return dbMsgIDProfilePath; }
    public void setDbMsgIDProfilePath(String dbMsgIDProfilePath) { this.dbMsgIDProfilePath = dbMsgIDProfilePath; }
    private String queueConfigPath;
    
    public String getQueueConfigPath() {
        return queueConfigPath;
    }
    
    public void setQueueConfigPath(String queueConfigPath) {
        this.queueConfigPath = queueConfigPath;
    }
	public String getToDir() {
		return toDir;
	}

	public void setToDir(String toDir) {
		this.toDir = toDir;
	}
    
    
}
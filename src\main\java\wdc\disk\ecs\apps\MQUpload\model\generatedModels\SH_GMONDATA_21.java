package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for SH_GMONDATA_21
 */
public class SH_GMONDATA_21  {
    private Short spindle;
    private Integer diskid;
    private java.time.LocalDateTime batchtime;
    private String surface;
    private Short rule;
    private String glideheadid;
    private Double hcf;
    private String method;
    private Double amp;
    private Double dev;
    private Integer glidehits;
    private Double bumphcf;
    private Integer qcode;
    private Short recsent;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public SH_GMONDATA_21() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getDiskid() {
        return diskid;
    }

    public void setDiskid(Integer diskid) {
        this.diskid = diskid;
    }

    public java.time.LocalDateTime getBatchtime() {
        return batchtime;
    }

    public void setBatchtime(java.time.LocalDateTime batchtime) {
        this.batchtime = batchtime;
    }

    public String getSurface() {
        return surface;
    }

    public void setSurface(String surface) {
        this.surface = surface;
    }

    public Short getRule() {
        return rule;
    }

    public void setRule(Short rule) {
        this.rule = rule;
    }

    public String getGlideheadid() {
        return glideheadid;
    }

    public void setGlideheadid(String glideheadid) {
        this.glideheadid = glideheadid;
    }

    public Double getHcf() {
        return hcf;
    }

    public void setHcf(Double hcf) {
        this.hcf = hcf;
    }

    public String getMethod() {
        return method;
    }

    public void setMethod(String method) {
        this.method = method;
    }

    public Double getAmp() {
        return amp;
    }

    public void setAmp(Double amp) {
        this.amp = amp;
    }

    public Double getDev() {
        return dev;
    }

    public void setDev(Double dev) {
        this.dev = dev;
    }

    public Integer getGlidehits() {
        return glidehits;
    }

    public void setGlidehits(Integer glidehits) {
        this.glidehits = glidehits;
    }

    public Double getBumphcf() {
        return bumphcf;
    }

    public void setBumphcf(Double bumphcf) {
        this.bumphcf = bumphcf;
    }

    public Integer getQcode() {
        return qcode;
    }

    public void setQcode(Integer qcode) {
        this.qcode = qcode;
    }

    public Short getRecsent() {
        return recsent;
    }

    public void setRecsent(Short recsent) {
        this.recsent = recsent;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



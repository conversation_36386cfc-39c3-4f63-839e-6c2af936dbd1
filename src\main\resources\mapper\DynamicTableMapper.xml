<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="wdc.disk.ecs.apps.MQUpload.mapper.DynamicTableMapper">
    <insert id="insertOrUpdateData">
        MERGE INTO DB2SYS.${tableName} AS TARGET
        USING (VALUES (
            <foreach collection="data" index="key" item="value" separator=",">
                #{value}
            </foreach>
        )) AS SOURCE (
            <foreach collection="data" index="key" item="value" separator=",">
                ${key}
            </foreach>
        )
        ON 
        <foreach collection="primaryKeys" item="primaryKey" separator=" AND ">
            TARGET.${primaryKey} = SOURCE.${primaryKey}
        </foreach>
        WHEN MATCHED THEN
            UPDATE SET 
            <foreach collection="data" index="key" item="value" separator=",">
                <if test="key != 'LOT' and key != 'DT_ENTRY'">
                    TARGET.${key} = SOURCE.${key}
                </if>
            </foreach>
        WHEN NOT MATCHED THEN
            INSERT (
            <foreach collection="data" index="key" item="value" separator=",">
                ${key}
            </foreach>
            )
            VALUES (
            <foreach collection="data" index="key" item="value" separator=",">
                SOURCE.${key}
            </foreach>
            )
    </insert>
</mapper>




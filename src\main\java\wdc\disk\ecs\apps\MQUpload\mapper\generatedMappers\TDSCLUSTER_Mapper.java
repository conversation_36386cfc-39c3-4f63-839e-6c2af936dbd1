package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSCLUSTER;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSCLUSTER
 */
public interface TDSCLUSTER_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_CLUSTER AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{channelId},",
        "    #{clusterId},",
        "    #{clusterType},",
        "    #{clusterSize},",
        "    #{surface},",
        "    #{subType},",
        "    #{rlenUm},",
        "    #{clenUm},",
        "    #{midRadiusMm},",
        "    #{midAngleRad},",
        "    #{radius},",
        "    #{surfaceTds},",
        "    #{kind},",
        "    #{angle},",
        "    #{width},",
        "    #{length},",
        "    #{max},",
        "    #{min},",
        "    #{aMax},",
        "    #{rms},",
        "    #{advd},",
        "    #{totalarea},",
        "    #{oriChannel},",
        "    #{maxamp},",
        "    #{minamp}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    CLUSTER_ID,",
        "    CLUSTER_TYPE,",
        "    CLUSTER_SIZE,",
        "    SURFACE,",
        "    SUB_TYPE,",
        "    RLEN_UM,",
        "    CLEN_UM,",
        "    MID_RADIUS_MM,",
        "    MID_ANGLE_RAD,",
        "    RADIUS,",
        "    SURFACE_TDS,",
        "    KIND,",
        "    ANGLE,",
        "    WIDTH,",
        "    LENGTH,",
        "    MAX,",
        "    MIN,",
        "    A_MAX,",
        "    RMS,",
        "    ADVD,",
        "    TOTALAREA,",
        "    ORI_CHANNEL,",
        "    MAXAMP,",
        "    MINAMP",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CHANNEL_ID = SOURCE.CHANNEL_ID AND TARGET.CLUSTER_ID = SOURCE.CLUSTER_ID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CLUSTER_TYPE = SOURCE.CLUSTER_TYPE,",
        "    TARGET.CLUSTER_SIZE = SOURCE.CLUSTER_SIZE,",
        "    TARGET.SURFACE = SOURCE.SURFACE,",
        "    TARGET.SUB_TYPE = SOURCE.SUB_TYPE,",
        "    TARGET.RLEN_UM = SOURCE.RLEN_UM,",
        "    TARGET.CLEN_UM = SOURCE.CLEN_UM,",
        "    TARGET.MID_RADIUS_MM = SOURCE.MID_RADIUS_MM,",
        "    TARGET.MID_ANGLE_RAD = SOURCE.MID_ANGLE_RAD,",
        "    TARGET.RADIUS = SOURCE.RADIUS,",
        "    TARGET.SURFACE_TDS = SOURCE.SURFACE_TDS,",
        "    TARGET.KIND = SOURCE.KIND,",
        "    TARGET.ANGLE = SOURCE.ANGLE,",
        "    TARGET.WIDTH = SOURCE.WIDTH,",
        "    TARGET.LENGTH = SOURCE.LENGTH,",
        "    TARGET.MAX = SOURCE.MAX,",
        "    TARGET.MIN = SOURCE.MIN,",
        "    TARGET.A_MAX = SOURCE.A_MAX,",
        "    TARGET.RMS = SOURCE.RMS,",
        "    TARGET.ADVD = SOURCE.ADVD,",
        "    TARGET.TOTALAREA = SOURCE.TOTALAREA,",
        "    TARGET.ORI_CHANNEL = SOURCE.ORI_CHANNEL,",
        "    TARGET.MAXAMP = SOURCE.MAXAMP,",
        "    TARGET.MINAMP = SOURCE.MINAMP",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    CLUSTER_ID,",
        "    CLUSTER_TYPE,",
        "    CLUSTER_SIZE,",
        "    SURFACE,",
        "    SUB_TYPE,",
        "    RLEN_UM,",
        "    CLEN_UM,",
        "    MID_RADIUS_MM,",
        "    MID_ANGLE_RAD,",
        "    RADIUS,",
        "    SURFACE_TDS,",
        "    KIND,",
        "    ANGLE,",
        "    WIDTH,",
        "    LENGTH,",
        "    MAX,",
        "    MIN,",
        "    A_MAX,",
        "    RMS,",
        "    ADVD,",
        "    TOTALAREA,",
        "    ORI_CHANNEL,",
        "    MAXAMP,",
        "    MINAMP",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CHANNEL_ID,",
        "    SOURCE.CLUSTER_ID,",
        "    SOURCE.CLUSTER_TYPE,",
        "    SOURCE.CLUSTER_SIZE,",
        "    SOURCE.SURFACE,",
        "    SOURCE.SUB_TYPE,",
        "    SOURCE.RLEN_UM,",
        "    SOURCE.CLEN_UM,",
        "    SOURCE.MID_RADIUS_MM,",
        "    SOURCE.MID_ANGLE_RAD,",
        "    SOURCE.RADIUS,",
        "    SOURCE.SURFACE_TDS,",
        "    SOURCE.KIND,",
        "    SOURCE.ANGLE,",
        "    SOURCE.WIDTH,",
        "    SOURCE.LENGTH,",
        "    SOURCE.MAX,",
        "    SOURCE.MIN,",
        "    SOURCE.A_MAX,",
        "    SOURCE.RMS,",
        "    SOURCE.ADVD,",
        "    SOURCE.TOTALAREA,",
        "    SOURCE.ORI_CHANNEL,",
        "    SOURCE.MAXAMP,",
        "    SOURCE.MINAMP",
        "  )"
    })
    void insertOrUpdate(TDSCLUSTER record);
}











* Table: DB2SYS.TDS_SCAN (ASCII format)
*| ColumnName       | DBColType |DBColLen|  SysType        |BinLength|ColNo|NULLS |StartIdx  |ValueIdx| UniIdx | 
|  DTINSERT         | TIMESTAMP | 10     | System.DateTime |  0      | 0   | N    |  0       |  0     |        |
|  MEAS_TIME        | TIMESTAMP | 10     | System.DateTime |  0      | 1   | N    |  0       |  1     | Y      |
|  RAWDATA_FILE     | VARCHAR   | 255    | System.String   |  0      | 2   | Y    |  0       |  3     |        |
|  LOT              | CHARACTER | 10     | System.String   |  0      | 3   | Y    |  0       |  4     |        |
|  DISKSEQUENCE     | SMALLINT  | 2      | System.Int16    |  0      | 4   | Y    |  0       |  5     |        |
|  SCAN_TIME_MSEC   | REAL      | 4      | System.Double   |  0      | 5   | Y    |  0       |  6     |        |
|  BIN_NUMBER       | REAL      | 4      | System.Double   |  0      | 6   | Y    |  0       |  7     |        |
|  VACUMM_PRESSURE  | REAL      | 4      | System.Double   |  0      | 7   | Y    |  0       |  8     |        |
|  SPINDLE          | INTEGER   | 4      | System.Int32    |  0      | 8   | Y    |  0       |  2     | Y      |
|  MAP_FILENAME     | VARCHAR   | 255    | System.String   |  0      | 9   | Y    |  0       |  9     |        |
|  PRODUCT          | CHARACTER | 6      | System.String   |  0      | 10  | Y    |  0       |  10    |        |
|  CELL_ID          | VARCHAR   | 20     | System.String   |  0      | 11  | Y    |  0       |  11    |        |
|  FINAL_GRADE      | VARCHAR   | 20     | System.String   |  0      | 12  | Y    |  0       |  12    |        |
|  GLIDE_GRADE      | VARCHAR   | 20     | System.String   |  0      | 13  | Y    |  0       |  13    |        |
|  CERT_GRADE       | VARCHAR   | 20     | System.String   |  0      | 14  | Y    |  0       |  14    |        |
|  RECIPE_FILENAME  | VARCHAR   | 255    | System.String   |  0      | 15  | Y    |  0       |  15    |        |
|  HSA1_ID          | VARCHAR   | 255    | System.String   |  0      | 16  | Y    |  0       |  16    |        |
|  HSA2_ID          | VARCHAR   | 255    | System.String   |  0      | 17  | Y    |  0       |  17    |        |
|  HSA3_ID          | VARCHAR   | 255    | System.String   |  0      | 18  | Y    |  0       |  18    |        |
|  TOTAL_TOSC_COUNT | REAL      | 4      | System.Double   |  0      | 19  | Y    |  0       |  19    |        |
|  EVAL_CODE        | VARCHAR   | 255    | System.String   |  0      | 20  | Y    |  0       |  20    |        |
|  HSA1_CYCLE_COUNT | REAL      | 4      | System.Double   |  0      | 21  | Y    |  0       |  21    |        |
|  HSA2_CYCLE_COUNT | REAL      | 4      | System.Double   |  0      | 22  | Y    |  0       |  22    |        |
|  HSA3_CYCLE_COUNT | REAL      | 4      | System.Double   |  0      | 23  | Y    |  0       |  23    |        |
|  TESTER_TYPE      | VARCHAR   | 10     | System.String   |  0      | 24  | Y    |  0       |  24    |        |
|  SOFTWARE_VERSION | VARCHAR   | 25     | System.String   |  0      | 25  | Y    |  0       |  25    |        |
|SCAN_START_RADIUS_MM| REAL      | 4      | System.Double   |  0      | 26  | Y    |  0       |  26    |        |
|SCAN_STOP_RADIUS_MM | REAL      | 4      | System.Double   |  0      | 27  | Y    |  0       |  27    |        |
|  FORM_FACTOR      | VARCHAR   | 10     | System.String   |  0      | 28  | Y    |  0       |  28    |        |
|  SCAN_OPTION      | REAL      | 4      | System.Double   |  0      | 29  | Y    |  0       |  29    |        |
|  HBO              | REAL      | 4      | System.Double   |  0      | 30  | Y    |  0       |  30    |        |
|  AUTO_SITEMARK_BY_GRADE | CHARACTER | 1      | System.String   |  0      | 31  | Y    |  0       |  31    |        |
|  SUPPLY_CASSETTE  | VARCHAR   | 11     | System.String   |  0      | 32  | Y    |  0       |  32    |        |
|  OUTPUT_CASSETTE  | VARCHAR   | 11     | System.String   |  0      | 33  | Y    |  0       |  33    |        |
|  OUTPUT_LOT       | VARCHAR   | 10     | System.String   |  0      | 34  | Y    |  0       |  34    |        |
|  OUTPUT_SLOT      | SMALLINT  | 2      | System.Int16    |  0      | 35  | Y    |  0       |  35    |        |
|  UNITID           | CHARACTER | 12     | System.String   |  0      | 36  | Y    |  0       |  36    |        |
|  RULE_NUMBER      | INTEGER   | 4      | System.Int32    |  0      | 37  | Y    |  0       |  37    |        |
|  EXPID            | CHARACTER | 10     | System.String   |  0      | 38  | Y    |  0       |  38    |        |
|  INPUT_VARIETY    | CHARACTER | 14     | System.String   |  0      | 39  | Y    |  0       |  39    |        |
|  ROBOT_CLASS      | CHARACTER | 1      | System.String   |  0      | 40  | Y    |  0       |  40    |        |
|  RETESTED         | CHARACTER | 1      | System.String   |  0      | 41  | Y    |  0       |  41    |        |
|  AC_ERASED        | CHARACTER | 1      | System.String   |  0      | 42  | Y    |  0       |  42    |        |
|  CERTIFY_SCANNED  | CHARACTER | 1      | System.String   |  0      | 43  | Y    |  0       |  43    |        |
|  TDS_SCANNED      | CHARACTER | 1      | System.String   |  0      | 44  | Y    |  0       |  44    |        |
|  TDES_DFA_ACTIVE  | CHARACTER | 1      | System.String   |  0      | 45  | Y    |  0       |  45    |        |
|  TDES_DFA_MARKED  | CHARACTER | 1      | System.String   |  0      | 46  | Y    |  0       |  46    |        |
|  TEST_SEQUENCE    | CHARACTER | 2      | System.String   |  0      | 47  | Y    |  0       |  47    |        |
|  FINALGRADE_PASS  | VARCHAR   | 10     | System.String   |  0      | 48  | Y    |  0       |  48    |        |
|  TOPGRADE_PASS    | VARCHAR   | 10     | System.String   |  0      | 49  | Y    |  0       |  49    |        |
|  BOTGRADE_PASS    | VARCHAR   | 10     | System.String   |  0      | 50  | Y    |  0       |  50    |        |
|  TOPGRADE         | VARCHAR   | 20     | System.String   |  0      | 51  | Y    |  0       |  51    |        |
|  BOTGRADE         | VARCHAR   | 20     | System.String   |  0      | 52  | Y    |  0       |  52    |        |
|  CHECKSUM_GENERAL | DOUBLE    | 8      | System.Double   |  0      | 53  | Y    |  0       |  53    |        |
|  CHECKSUM_TDS     | DOUBLE    | 8      | System.Double   |  0      | 54  | Y    |  0       |  54    |        |
|  CHECKSUM_CERT    | DOUBLE    | 8      | System.Double   |  0      | 55  | Y    |  0       |  55    |        |
|  CHECKSUM_GRADING | DOUBLE    | 8      | System.Double   |  0      | 56  | Y    |  0       |  56    |        |
|  RTTC_COUNTS      | VARCHAR   | 255    | System.String   |  0      | 57  | Y    |  0       |  57    |        |
|  DC_ERASED        | CHARACTER | 1      | System.String   |  0      | 58  | Y    |  0       |  58    |        |
|  TESTED_TONESCAN  | SMALLINT  | 2      | System.Int16    |  0      | 59  | Y    |  0       |  59    |        |

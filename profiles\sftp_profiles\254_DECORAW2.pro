*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PROCESSTIME           |  Double      |  8      |  System.Double         |  8      |  3      |         |  0      |  3      |  
|  ULTRASONICTIME        |  Double      |  8      |  System.Double         |  8      |  4      |         |  0      |  4      |  
|  FLOWRATE              |  Double      |  8      |  System.Double         |  8      |  5      |         |  0      |  5      |  
|  DIWFLOWRATE           |  Double      |  8      |  System.Double         |  8      |  6      |         |  0      |  6      |  
|  LOADERMODE            |  Double      |  8      |  System.Double         |  8      |  7      |         |  0      |  7      |  
|  FREQUENCY             |  Double      |  8      |  System.Double         |  8      |  8      |         |  0      |  8      |  
|  POWER                 |  Double      |  8      |  System.Double         |  8      |  9      |         |  0      |  9      |  
|  TEMPERATURE           |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  

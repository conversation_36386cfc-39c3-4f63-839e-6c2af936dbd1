package wdc.disk.ecs.apps.SFTPSend.thread;


import java.io.File;
import java.util.Date;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import wdc.disk.ecs.apps.SFTPSend.util.SendMail;

public class TestMonitorSftpThread {
		@Test
		public void testThread() throws InterruptedException {
			Thread thread = new Thread(new MonitorSftpSendRunnable());
			thread.setName("Monitor Send Status Thread");
			thread.start();
			Thread.sleep(100*1000);
		}
		
		@Test
		public void testTimeOut(){
			MonitorSftpSendRunnable run = new MonitorSftpSendRunnable();
			Date now = new Date();
			long time = now.getTime();
			boolean result = run.isTimeout(100, time);
			assertFalse(result);
			System.out.println("time out:" + result);
		}
		
		@Test
		public void testSendMail() {
			SendMail send = new SendMail();
		}
		
		
		@Test
		public void testReadConfigure() {
			MonitorSftpSendRunnable runnable = new MonitorSftpSendRunnable("C:\\opt\\ecs\\profiles\\SFTPSend.ini");
			runnable.readProfile("C:\\opt\\ecs\\profiles\\SFTPSend.ini", "MonitorSftpAlive");
			System.out.println("runnable.getTimeoutSeconds():"+ runnable.getTimeoutSeconds());
			System.out.println("runnable.getUnprocessLimit():" + runnable.getUnprocessLimit());
			System.out.println("runnable.getPdsFileDirString():" + runnable.getPdsFileDirString());
			System.out.println("runnable.getSentDirString():"+ runnable.getSentDirString());
			System.out.println("runnable.getMonitorInterval():" + runnable.getMonitorInterval());
		}
		
		@Test
		public void testReadLastModify() {
			File file = new File("C:\\opt\\ecs\\storage\\mq\\sent");
			boolean exists = file.exists();
			if(exists) {
				System.out.println("Path:" + file.getAbsolutePath());
			}	
			findLastestLastModifyFile(file);
			
		}

		private File findLastestLastModifyFile(File file) {
			File resultFile = null;
			if(file.isDirectory()) {
				long highest = 0;
				File[] listFiles = file.listFiles();
				for(int i=0; i < listFiles.length; i++) {
//					System.out.println("i:" + i + " file:" + listFiles[i].getName() + " last:" + listFiles[i].lastModified());
					long fileLastModify = listFiles[i].lastModified();
					if(highest < fileLastModify){
						highest = fileLastModify;
						resultFile = listFiles[i];
					}
				}
			}
//			System.out.println("Result:" + resultFile.getAbsolutePath());
			return resultFile;
		}
		
		
		
		
		
}

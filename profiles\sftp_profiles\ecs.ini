SFTPSend {
        PDS_DIR=/opt/ecs/storage/mq/sent/, // File directory where PDS files are saved
        SFTP_SERVER_DIR=/D:/ECS_FTP/InputFiles/POC/DECO/, // The file directory in SFTP server where PDS files will be sent
        PRIMARY_SFTP_SERVER=csf-mp-talend02.ad.shared:22:sftpadm:sftp@123456,
        BACKUP_SFTP_SERVER=csf-mp-talend01.ad.shared:22:sftpadm:sftp@123456,
       DB_MSG_ID_PROFILE=/opt/ecs/profiles/mqmsgid.pro,
        BINARY_FORMAT, //optional, default is N. N--PDS file is ascii-encoded data, Y--PDS file is binary data
        MAX_READ_FILES, // optional, default is 1000. Limit the number of files JV<PERSON> read one time to avoid OOM
        MAX_LINE_LENGTH, // optional, default is 4096. If the length of data line is more than specified length, not send to SFTP, but write to problem file
        IDLE_SEND_INTERVAL, // optional, default is 60, units-seconds. Interval time after last send in idle mode
        WAITING_FOR_WRITE_ENDED=5, // optional, default is 10, units-seconds. The time of waiting for other ECS application writes PDS file ended
        CHANGE_TO_IDLE, // optional, default is 120, units-seconds. No data to send for specified time, the app will change to idle mode
        RETENTION_HOURS=144, //optional, default is 144, units-hours. The time of retaining the sent data files, default is 7 days
}

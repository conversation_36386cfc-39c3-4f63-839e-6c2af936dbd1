package wdc.disk.ecs.apps.MQUpload.processor;

import ibm.disk.utility.LogStream;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BinaryFileProcessorTest {

    @Mock
    private LogStream logStream;
    
    @Mock
    private TableSchemaManager tableSchemaManager;
    
    @Mock
    private TableConfigParser.TableDefinition tableDefinition;
    
    @Mock
    private TableConfigParser.ColumnDefinition columnDefinition;

    private BinaryFileProcessor processor;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        processor = new BinaryFileProcessor(logStream, tableSchemaManager);
    }

    @Test
    void testProcessValidIndividualBinaryMessage() throws Exception {
        // Setup mocks for individual message processing
        when(tableSchemaManager.getTableDefinitionFromMessageId("1")).thenReturn(tableDefinition);
        when(tableDefinition.getTabCode()).thenReturn("TEST_TABLE");
        when(tableDefinition.getColumns()).thenReturn(Arrays.asList(columnDefinition));
        
        when(columnDefinition.getColumnName()).thenReturn("TEST_COLUMN");
        when(columnDefinition.getStartIndex()).thenReturn(0);
        when(columnDefinition.getBinaryLength()).thenReturn(4);
        when(columnDefinition.getDbColumnType()).thenReturn("INTEGER");
        
        byte[] content = {
            0x01, 0x00, 0x00, 0x00,  // Message ID = 1 (little endian)
            0x02, 0x00, 0x00, 0x00,  // Correl ID = 2 (little endian)
            0x48, 0x65, 0x6C, 0x6C, 0x6F  // "Hello"
        };
        
        File testFile = createBinaryFile("individual.bin", content);
        
        List<RabbitMessage> result = processor.processFile(testFile);
        
        assertNotNull(result);
        assertFalse(result.isEmpty());
        verify(logStream, never()).write(eq(LogStream.ERROR_MESSAGE), anyString());
    }

    @Test
    void testProcessValidPackedBinaryMessage() throws Exception {
        // Setup mocks for packed message processing
        // Only mock what's actually called for packed messages
        when(tableSchemaManager.getTableDefinitionFromMessageId("1")).thenReturn(tableDefinition);
        when(tableDefinition.getColumns()).thenReturn(Arrays.asList(columnDefinition));

        when(columnDefinition.getColumnName()).thenReturn("TEST_COLUMN");
        when(columnDefinition.getStartIndex()).thenReturn(0);
        when(columnDefinition.getBinaryLength()).thenReturn(4);
        when(columnDefinition.getDbColumnType()).thenReturn("INTEGER");

        byte[] content = {
            (byte)0xe6, 0x03, 0x00, 0x00,  // Packed message header
            0x00, 0x00, 0x00, 0x00,        // Correl ID placeholder
            0x01, 0x00, 0x00, 0x00,        // First message ID = 1
            0x02, 0x00, 0x00, 0x00,        // First correl ID = 2
            0x05, 0x00, 0x00, 0x00,        // Message length = 5
            0x48, 0x65, 0x6C, 0x6C, 0x6F   // "Hello"
        };

        File testFile = createBinaryFile("packed.bin", content);

        List<RabbitMessage> result = processor.processFile(testFile);

        assertNotNull(result);
        verify(logStream, never()).write(eq(LogStream.ERROR_MESSAGE), anyString());
    }

    @Test
    void testProcessEmptyFile() throws Exception {
        File emptyFile = tempDir.resolve("empty.bin").toFile();
        assertTrue(emptyFile.createNewFile());

        List<RabbitMessage> result = processor.processFile(emptyFile);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(logStream).write(eq(LogStream.ERROR_MESSAGE), contains("Binary file too short"));
    }

    @Test
    void testProcessFileTooShort() throws Exception {
        byte[] content = {0x01, 0x02, 0x03};
        File shortFile = createBinaryFile("short.bin", content);

        List<RabbitMessage> result = processor.processFile(shortFile);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(logStream).write(eq(LogStream.ERROR_MESSAGE), contains("Binary file too short"));
    }

    @Test
    void testProcessNonExistentFile() {
        File nonExistentFile = tempDir.resolve("nonexistent.bin").toFile();
        
        assertThrows(IOException.class, () -> processor.processFile(nonExistentFile));
    }

    @Test
    void testProcessFileWithException() throws Exception {
        when(tableSchemaManager.getTableDefinitionFromMessageId(anyString()))
            .thenThrow(new RuntimeException("Schema error"));
        
        byte[] content = {
            0x01, 0x00, 0x00, 0x00,  // Message ID = 1
            0x02, 0x00, 0x00, 0x00,  // Correl ID = 2
            0x48, 0x65, 0x6C, 0x6C, 0x6F  // "Hello"
        };
        
        File testFile = createBinaryFile("error.bin", content);
        
        List<RabbitMessage> result = processor.processFile(testFile);
        
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(logStream).write(eq(LogStream.ERROR_MESSAGE), contains("Error processing binary file"));
    }

    @Test
    void testProcessRealDataFile() throws Exception {
        // Mock a single column to ensure the parsing logic is minimally exercised.
        when(columnDefinition.getColumnName()).thenReturn("A_COLUMN");
        when(columnDefinition.getStartIndex()).thenReturn(0);
        when(columnDefinition.getBinaryLength()).thenReturn(1); // Read 1 byte
        when(columnDefinition.getDbColumnType()).thenReturn("CHAR");

        // Setup mocks to be more flexible and not depend on a hardcoded message ID.
        // This makes the test robust against changes in the data file.
        when(tableSchemaManager.getTableDefinitionFromMessageId(anyString())).thenReturn(tableDefinition);
        when(tableDefinition.getTabCode()).thenReturn("SPUTTERALARM");
        // Return one mocked column.
        when(tableDefinition.getColumns()).thenReturn(Arrays.asList(columnDefinition));

        // Path to the real data file
        Path dataFilePath = Paths.get("rsc", "bin_data", "250708000019.89894");
        byte[] fileContent = Files.readAllBytes(dataFilePath);

        File testFile = createBinaryFile("realdata.bin", fileContent);

        // Process the file
        List<RabbitMessage> result = processor.processFile(testFile);

        // Assertions
        assertNotNull(result);
        assertFalse(result.isEmpty(), "The result should not be empty");
        assertEquals(1, result.size(), "Should produce one RabbitMessage");

        RabbitMessage message = result.get(0);
        // The routing key should be the message ID from the file, which we don't know,
        // so we just check that it's not null or empty.
        assertNotNull(message.getRoutingKey());
        assertFalse(message.getRoutingKey().isEmpty());
        
        String pdsString = new String(message.getData(), "UTF-8");
        assertTrue(pdsString.contains("SPUTTERALARM"), "PDS string should contain the table code");
        // Check that the parsed column is present.
        assertTrue(pdsString.contains("/"), "PDS string should contain the column separator");


        // Verify no errors were logged
        verify(logStream, never()).write(eq(LogStream.ERROR_MESSAGE), anyString());
    }

    private File createBinaryFile(String filename, byte[] content) throws IOException {
        File testFile = tempDir.resolve(filename).toFile();
        try (FileOutputStream fos = new FileOutputStream(testFile)) {
            fos.write(content);
        }
        return testFile;
    }
}

package wdc.testBinary;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Random;

import org.junit.jupiter.api.Test;

import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryService;
import wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility;

public class TestParse {
	@Test
	public void testparseFile() throws IOException, ProfileException {
		LogStream log = new LogStream("C:\\Users\\<USER>\\Desktop\\testparse.log");
		DiskProfile profile = new DiskProfile("C:\\opt\\ecs\\profiles\\pdsMsgId.pro", "wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord", DiskProfile.KEEP_ONE, log);
		BinaryService binaryService = new BinaryService(log, profile);
		
		
		String path = "C:\\Users\\<USER>\\Desktop\\220804171733.782";
		File file = new File(path);
		if(!file.exists()) {
			System.out.println("File not exist");
		}
		FileInputStream fis = new FileInputStream(file);
		fis = new FileInputStream(file);
		int tmp;
		StringBuffer sBuffer = new StringBuffer();
		while((tmp = fis.read()) != -1) {
			sBuffer.append(StringMethods.pad(Integer.toHexString(tmp), 2, '0', true));
		}
		System.out.println("raw data:" + sBuffer.toString());
		try {
			if(fis != null) {
				fis.close();						
				fis = null;
			}
		} catch (IOException e) {
			e.printStackTrace();
		} 
		String str = binaryService.generatePdsStringForIndividualMsg(sBuffer.toString());
		System.out.println(str);
	}
	
	@Test
	public void testConvert() {
		String str = "0095073535363433316e300099641a770094f4e8";
		
		String str2 = "00340000";
		parse(str2);
		
		String str3 = "34300300";
		parse(str3);
		
		String str4 = "34300000";
		parse(str4);
//		String str3 = str.substring(0, 8);
//		String convertHexBytesToString3 = ConvertorUtility.ConvertHexBytesToString(str3);
//		System.out.println("Str3:" + str3);
//		System.out.println("Result3:" + convertHexBytesToString3 + "\n");
	}

	private void parse(String str) {
		String convertHexBytesToString = ConvertorUtility.ConvertHexBytesToString(str);
		System.out.println("Str:" + str);
		System.out.println("Result:" + convertHexBytesToString);
		
		while(str.indexOf("00") == 0) {
			str = str.substring(2);
		}
		System.out.println("Trim str:" + str);
		
		
//		int index = str.indexOf("00");
//		if(index % 2 != 0 ) {
//			str.
//		}
//		
		int index2 = 0;
		while((index2 = str.indexOf("00",index2)) % 2!= 0) {
			index2 = index2 +1;
		}
		String str2 = str.substring(0, index2);
//		System.out.println(index2);
//		System.out.println(str2);
		
//		System.out.println("index:" + index);
//		String str2 = str.substring(0, index);
		String convertHexBytesToString2 = ConvertorUtility.ConvertHexBytesToString( "00" + str2);
		System.out.println("Str2:" + str2);
		System.out.println("Result2:" + convertHexBytesToString2 + "\n");
	}
	
	@Test
	public void testParse2() {
//		String data = "0095073535363433316e300099641a770094f4e8";
		String data = "4b4b";
		String strResultString = data;
		int iStart= 0;
		int iEnd= 0;
		boolean bTruncate = false;
		for(int i=0; i< data.length() - 2; i=i+2) {
			String tmpStr = data.substring(i, i+2);
			if(tmpStr.toUpperCase().compareTo("1F") < 0 
					||  tmpStr.toUpperCase().compareTo("7F") > 0 ){		
				if(bTruncate) {
					iEnd = i;
					strResultString = data.substring(iStart, iEnd);
					break;
				}
			}else {					
				if(!bTruncate) {
					iStart=i;
				}
				bTruncate = true;
				
			}

		}
		String convertHexBytesToString = ConvertorUtility.ConvertHexBytesToString(strResultString);
		System.out.println("result:"+convertHexBytesToString);
	}
	
	@Test
	public void testChu() {
		Random ran = new Random();
		for(int i=0; i<100; i++) {
			System.out.println(ran.nextInt(101));
		}
	
		
	}
}

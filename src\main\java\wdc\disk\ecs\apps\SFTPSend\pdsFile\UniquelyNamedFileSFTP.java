package wdc.disk.ecs.apps.SFTPSend.pdsFile;

import java.io.*;
import java.text.*;
import java.util.*;
import ibm.disk.utility.*;

/**
 *********************************************************************
 *                                                                   *
 * UniquelyNamedFileSFTP                                             *
 *                                                                   *
 *********************************************************************
 * This class can be used for files which need unique file names, it creates
 * a file name based on a specified starting string and the timestamp.
 * Just make sure the specified starting string is unique for all applications
 * which use this class, if the path is the same.
 * 
 * #see ibm.disk.utility.UniquelyNamedFile, change startChar to startString
 * @Date 2021-08-10 09:34:58
 * <AUTHOR>
 */
public class UniquelyNamedFileSFTP {
	private java.io.FileOutputStream fileStream;
	private java.lang.String path;
	private String startString;
	private int recordCount = 0;
	private static int index = 0;
	private java.lang.String filename = "";
	
	/**
	 * UniquelyNamedFileSFTP constructor comment.
	 * @param filepath java.lang.String The path where the file is to be stored.
	 * @param startString java.lang.String
	 */
	public UniquelyNamedFileSFTP(String filepath, String startString) {
		super();
		setPath(filepath);
		setStartString(startString);
		File dir = new File(filepath);
		dir.mkdirs();
	}
	
	/**
	 * Opens a new timestamped file, closes the old one if it was open.
	 * Errors are logged to the default log file.
	 * Creation date: (11/6/2002 4:53:49 PM)
	 */
	public boolean openFile() {
		if (this.fileStream != null) {
			LogStream.writeToErrorLog(LogStream.WARNING_MESSAGE, "Previously opened file(" + filename + ") was not closed, closing the file before opening a new one");		
			this.closeFile();
		}
	
		this.recordCount = 0;
		// create a file with a timestamp in it, try 3 times before giving up.
		Calendar cal = Calendar.getInstance();
		SimpleDateFormat df = new SimpleDateFormat("MMddHHmmss");
		String fname = df.format(new java.util.Date());
		int retry = 0;
		do {
			if (index > 1000) {
				index = 0;
			}
			this.filename = new String(getPath() + getStartString() + "." + index++);
			try {
				this.fileStream = new FileOutputStream(this.filename);
				retry = 5;
			} catch (FileNotFoundException e1) {
				if (retry < 2) {
					retry++;
				} else {
	  				LogStream.writeToErrorLog(LogStream.ERROR_MESSAGE,"Unable to open file " + this.filename + " exception: " + e1.toString());
	  				this.fileStream = null;
	  				this.filename  = null;
					return (false);
				}
			} catch (Exception e2) {
				LogStream.writeToErrorLog(LogStream.ERROR_MESSAGE,"Unable to open file " + this.filename + " exception: " + e2.toString());
				this.fileStream = null;
				this.filename = null;
				return false;
			}
		} while (retry != 5);
		
		return (true);	
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (11/6/2002 5:54:38 PM)
	 * @param data java.lang.String
	 */
	public boolean appendToFile(String data) {
		try {
			if (fileStream == null) {
				if (!this.openFile()) {
					return false;
				}
			}
			this.fileStream.write(data.getBytes());
			if (recordCount++ > 100) {
				this.closeFile();
			}	
		} catch (Exception e) {
			LogStream.writeToErrorLog(LogStream.WARNING_MESSAGE,"Unable to write data to file " + this.filename);
			return false;
		}
				
		return true;
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (11/6/2002 5:47:52 PM)
	 */
	public boolean closeFile() {
		if (this.fileStream != null) {
			try {
				this.fileStream.close();
				this.fileStream = null;
				this.filename = null;
				this.recordCount = 0;
			} catch (Exception e) {
				LogStream.writeToErrorLog(LogStream.WARNING_MESSAGE,"Unable to close file ");		
				return false;
			}
		}
		return true;
	}
	/**
	 * Insert the method's description here.
	 * Creation date: (11/6/2002 4:49:52 PM)
	 * @return java.lang.String
	 */
	public java.lang.String getPath() {
		return this.path;
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (11/6/2002 4:49:52 PM)
	 * @param newPath java.lang.String
	 */
	public void setPath(java.lang.String newPath) {
		if (newPath.endsWith(File.separator)) {
			path = newPath;
		} else {
			path = newPath+File.separator;
		}
	}
	
	/**
	 * Creation date: 2021-08-10 09:52:57
	 * @return String
	 */
	public String getStartString() {
		return startString;
	}
	/**
	 * Creation date: 2021-08-10 09:52:57
	 * @param startString String
	 */
	public void setStartString(String startString) {
		this.startString = startString;
	}
}

package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for MONDATA_15
 */
public class MONDATA_15  {
    private Short spindle;
    private Short dataindex;
    private Short bandnum;
    private Short reqopernum;
    private Double result;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public MONDATA_15() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Short getDataindex() {
        return dataindex;
    }

    public void setDataindex(Short dataindex) {
        this.dataindex = dataindex;
    }

    public Short getBandnum() {
        return bandnum;
    }

    public void setBandnum(Short bandnum) {
        this.bandnum = bandnum;
    }

    public Short getReqopernum() {
        return reqopernum;
    }

    public void setReqopernum(Short reqopernum) {
        this.reqopernum = reqopernum;
    }

    public Double getResult() {
        return result;
    }

    public void setResult(Double result) {
        this.result = result;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



*| ColumnName            |  DBColumnType|UniqueIndex |DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |    Y       |  4             |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |            |  4             |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |            |  10            |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |            |  6             |  System.String         |  6      |  3      |         |  0      |  3      |  
|  RESOURCE              |  Char        |            |  10            |  System.String         |  10     |  4      |         |  0      |  4      |  
|  SLOT                  |  SmallInt    |            |  2             |  System.Int16          |  2      |  5      |         |  0      |  5      |  
|  RPM_ID                |  SmallInt    |            |  2             |  System.Int16          |  2      |  6      |         |  0      |  6      |  
|  RPM_OD                |  SmallInt    |            |  2             |  System.Int16          |  2      |  7      |         |  0      |  7      |  
|  F_LOAD                |  SmallInt    |            |  2             |  System.Int16          |  2      |  8      |         |  0      |  8      |  
|  R_LOAD                |  SmallInt    |            |  2             |  System.Int16          |  2      |  9      |         |  0      |  9      |  
|  PT5_COUNT             |  SmallInt    |            |  2             |  System.Int16          |  2      |  10     |         |  0      |  10     |  
|  PT3_COUNT             |  SmallInt    |            |  2             |  System.Int16          |  2      |  11     |         |  0      |  11     |  

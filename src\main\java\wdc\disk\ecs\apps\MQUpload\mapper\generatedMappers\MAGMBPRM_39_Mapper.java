package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.MAGMBPRM_39;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for MAGMBPRM_39
 */
public interface MAGMBPRM_39_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.MAGMBPRM AS TARGET",
        "USING (VALUES (",
        "    #{elemname},",
        "    #{productnum},",
        "    #{channel},",
        "    #{half},",
        "    #{lowpassfreq},",
        "    #{highpassfreq},",
        "    #{freq},",
        "    #{parmnum},",
        "    #{slope},",
        "    #{offset},",
        "    #{corrcoeff},",
        "    #{maxdev},",
        "    #{avgdev},",
        "    #{spindle},",
        "    #{calibtime},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    ELEMNAME,",
        "    PRODUCTNUM,",
        "    CHANNEL,",
        "    HALF,",
        "    LOWPASSFREQ,",
        "    HIGHPASSFREQ,",
        "    FREQ,",
        "    PARMNUM,",
        "    SLOPE,",
        "    OFFSET,",
        "    CORRCOEFF,",
        "    MAXDEV,",
        "    AVGDEV,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.ELEMNAME = SOURCE.ELEMNAME AND TARGET.CHANNEL = SOURCE.CHANNEL AND TARGET.HALF = SOURCE.HALF AND TARGET.LOWPASSFREQ = SOURCE.LOWPASSFREQ AND TARGET.HIGHPASSFREQ = SOURCE.HIGHPASSFREQ AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CALIBTIME = SOURCE.CALIBTIME",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.PRODUCTNUM = SOURCE.PRODUCTNUM,",
        "    TARGET.FREQ = SOURCE.FREQ,",
        "    TARGET.PARMNUM = SOURCE.PARMNUM,",
        "    TARGET.SLOPE = SOURCE.SLOPE,",
        "    TARGET.OFFSET = SOURCE.OFFSET,",
        "    TARGET.CORRCOEFF = SOURCE.CORRCOEFF,",
        "    TARGET.MAXDEV = SOURCE.MAXDEV,",
        "    TARGET.AVGDEV = SOURCE.AVGDEV,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    ELEMNAME,",
        "    PRODUCTNUM,",
        "    CHANNEL,",
        "    HALF,",
        "    LOWPASSFREQ,",
        "    HIGHPASSFREQ,",
        "    FREQ,",
        "    PARMNUM,",
        "    SLOPE,",
        "    OFFSET,",
        "    CORRCOEFF,",
        "    MAXDEV,",
        "    AVGDEV,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.ELEMNAME,",
        "    SOURCE.PRODUCTNUM,",
        "    SOURCE.CHANNEL,",
        "    SOURCE.HALF,",
        "    SOURCE.LOWPASSFREQ,",
        "    SOURCE.HIGHPASSFREQ,",
        "    SOURCE.FREQ,",
        "    SOURCE.PARMNUM,",
        "    SOURCE.SLOPE,",
        "    SOURCE.OFFSET,",
        "    SOURCE.CORRCOEFF,",
        "    SOURCE.MAXDEV,",
        "    SOURCE.AVGDEV,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CALIBTIME,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(MAGMBPRM_39 record);
}











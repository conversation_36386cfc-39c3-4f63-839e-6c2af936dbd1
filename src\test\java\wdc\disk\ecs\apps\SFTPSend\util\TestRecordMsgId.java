package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.util.Calendar;

import org.junit.jupiter.api.Test;



public class TestRecordMsgId {
	@Test
	public void testRecordMsgId() throws IOException {
		RecordMsgIdService service = new RecordMsgIdService("Lihao", "C:\\Users\\<USER>\\Desktop\\");
		service.recordMsgId("2");
		service.recordMsgId("10");
		service.recordMsgId("11");
	}
	
	@Test
	public void testRecordDetail() throws IOException {
		RecordMsgIdService service = new RecordMsgIdService("Lihao", "C:\\Users\\<USER>\\Desktop\\");
		service.recordMsgDetail("10", "fileName0", "C:\\Users\\<USER>\\Desktop\\sendingDir\\");
		service.recordMsgDetail("11", "fileName1", "C:\\Users\\<USER>\\Desktop\\sendingDir\\");
		Calendar calendar = service.getLastCal();
		calendar.roll(Calendar.DAY_OF_MONTH, false);
		service.setLastCal(calendar);
		service.recordMsgDetail("12", "fileName2", "C:\\Users\\<USER>\\Desktop\\sendingDir\\");
	}
	
	@Test
	public void testFileName() {
		File file = new File("C:\\Users\\<USER>\\Desktop\\test");
		boolean result = file.delete();
		System.out.println(result);
		System.out.println(file.getName());
	}
}

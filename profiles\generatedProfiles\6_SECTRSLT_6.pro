* Table: DB2SYS.SECTRSLT(BINARY format)
*| ColumnName       | DBColType   | DBColLen | SysType         | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
| SPINDLE          | SmallInt    | 2        | System.Int16    | 2         | 0     | Y     | 0        | 0        | Y      |
| TESTNUM          | Integer     | 4        | System.Int32    | 4         | 1     | Y     | 8        | 1        | Y      |
| BANDNUM          | SmallInt    | 2        | System.Int16    | 2         | 2     | Y     | 16       | 2        | Y      |
| TRACK            | Double      | 8        | System.Double   | 8         | 3     | Y     | 24       | 3        | Y      |
| REV              | SmallInt    | 2        | System.Int16    | 2         | 4     | Y     | 40       | 4        | Y      |
| SECTOR           | SmallInt    | 2        | System.Int16    | 2         | 5     | Y     | 44       | 5        | Y      |
| REQOPERNUM       | SmallInt    | 2        | System.Int16    | 2         | 6     | Y     | 48       | 6        | Y      |
| SIDE             | Char        | 1        | System.String   | 1         | 7     | Y     | 52       | 7        | Y      |
| RESULT           | Double      | 8        | System.Double   | 8         | 8     |       | 56       | 8        |        |
| DTINSERT         | Timestamp   | 4        | System.DateTime | 4         | 9     |       | 0        | 9        |        |
| CORRELID         | Integer     | 4        | System.Int32    | 4         | 10    |       | 0        | 10       |        |

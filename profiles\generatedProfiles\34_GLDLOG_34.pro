* Table: DB2SYS.GLDLOG(BINARY format)
*| ColumnName       |  DBColumnType  |  DBColumnLength  |  SystemType       |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  UniIdx  |
|  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  0      |  Y      |  0      |  0      |  Y       |
|  GLIDENUM         |  Integer     |  4      |  System.Int32     |  4      |  1      |  Y      |  8      |  1      |  Y       |
|  BATCHID          |  Integer     |  4      |  System.Int32     |  4      |  2      |         |  16     |  2      |          |
|  PRODUCTCODE      |  SmallInt    |  2      |  System.Int16     |  2      |  3      |         |  24     |  3      |          |
|  DISKCODE         |  Integer     |  4      |  System.Int32     |  4      |  4      |         |  32     |  4      |          |
|  DISKSEQUENCE     |  Integer     |  4      |  System.Int64     |  2      |  5      |         |  152    |  5      |          |
|  GLIDERULE        |  SmallInt    |  2      |  System.Int16     |  2      |  6      |         |  48     |  6      |          |
|  GLIDECODE        |  SmallInt    |  2      |  System.Int16     |  2      |  7      |         |  52     |  7      |          |
|  QCODE            |  Integer     |  4      |  System.Int32     |  4      |  8      |         |  56     |  8      |          |
|  TOPLEVEL         |  Integer     |  4      |  System.Int32     |  2      |  9      |         |  64     |  9      |          |
|  BOTTOMLEVEL      |  Integer     |  4      |  System.Int32     |  2      |  10     |         |  68     |  10     |          |
|  STARTGLIDE       |  Timestamp   |  4      |  System.DateTime  |  4      |  11     |         |  72     |  11     |          |
|  STOPGLIDE        |  Timestamp   |  4      |  System.DateTime  |  4      |  12     |         |  80     |  12     |          |
|  OFFSET           |  Double      |  8      |  System.Double    |  8      |  13     |         |  88     |  13     |          |
|  DTINSERT         |  Timestamp   |  4      |  System.DateTime  |  4      |  14     |         |  0      |  14     |          |
|  CORRELID         |  Integer     |  4      |  System.Int32     |  4      |  15     |         |  0      |  15     |          |
|  LOT              |  Char        |  10     |  System.String    |  10     |  16     |         |  104    |  16     |          |
|  UNITID           |  Char        |  12     |  System.String    |  12     |  17     |         |  126    |  17     |          |
|  BOT_FULL         |  Char        |  1      |  System.String    |  1      |  18     |         |  208    |  18     |          |
|  LUL_SOFT_BOT     |  Integer     |  4      |  System.Int32     |  2      |  19     |         |  212    |  19     |          |
|  LUL_CRASH_BOT    |  Integer     |  4      |  System.Int32     |  2      |  20     |         |  216    |  20     |          |
|  LUL_HARD_BOT     |  Integer     |  4      |  System.Int32     |  2      |  21     |         |  220    |  21     |          |
|  LUL_ZONAL_BOT    |  Integer     |  4      |  System.Int32     |  2      |  22     |         |  224    |  22     |          |
|  DATA_SOFT_BOT    |  Integer     |  4      |  System.Int32     |  2      |  23     |         |  228    |  23     |          |
|  DATA_CRASH_BOT   |  Integer     |  4      |  System.Int32     |  2      |  24     |         |  232    |  24     |          |
|  DATA_HARD_BOT    |  Integer     |  4      |  System.Int32     |  2      |  25     |         |  236    |  25     |          |
|  DATA_ZONAL_BOT   |  Integer     |  4      |  System.Int32     |  2      |  26     |         |  240    |  26     |          |
|  IDC_SOFT_BOT     |  Integer     |  4      |  System.Int32     |  2      |  27     |         |  244    |  27     |          |
|  IDC_CRASH_BOT    |  Integer     |  4      |  System.Int32     |  2      |  28     |         |  248    |  28     |          |
|  IDC_HARD_BOT     |  Integer     |  4      |  System.Int32     |  2      |  29     |         |  252    |  29     |          |
|  IDC_ZONAL_BOT    |  Integer     |  4      |  System.Int32     |  2      |  30     |         |  256    |  30     |          |
|  TOP_FULL         |  Char        |  1      |  System.String    |  1      |  31     |         |  156    |  31     |          |
|  LUL_SOFT_TOP     |  Integer     |  4      |  System.Int32     |  2      |  32     |         |  160    |  32     |          |
|  LUL_CRASH_TOP    |  Integer     |  4      |  System.Int32     |  2      |  33     |         |  164    |  33     |          |
|  LUL_HARD_TOP     |  Integer     |  4      |  System.Int32     |  2      |  34     |         |  168    |  34     |          |
|  LUL_ZONAL_TOP    |  Integer     |  4      |  System.Int32     |  2      |  35     |         |  172    |  35     |          |
|  DATA_SOFT_TOP    |  Integer     |  4      |  System.Int32     |  2      |  36     |         |  176    |  36     |          |
|  DATA_CRASH_TOP   |  Integer     |  4      |  System.Int32     |  2      |  37     |         |  180    |  37     |          |
|  DATA_HARD_TOP    |  Integer     |  4      |  System.Int32     |  2      |  38     |         |  184    |  38     |          |
|  DATA_ZONAL_TOP   |  Integer     |  4      |  System.Int32     |  2      |  39     |         |  188    |  39     |          |
|  IDC_SOFT_TOP     |  Integer     |  4      |  System.Int32     |  2      |  40     |         |  192    |  40     |          |
|  IDC_CRASH_TOP    |  Integer     |  4      |  System.Int32     |  2      |  41     |         |  196    |  41     |          |
|  IDC_HARD_TOP     |  Integer     |  4      |  System.Int32     |  2      |  42     |         |  200    |  42     |          |
|  IDC_ZONAL_TOP    |  Integer     |  4      |  System.Int32     |  2      |  43     |         |  204    |  43     |          |
|  PADWIPE_COUNT    |  SmallInt    |  2      |  System.Int16     |  2      |  44     |         |  260    |  44     |          |
|  GLDRULEVER       |  SmallInt    |  2      |  System.Int16     |  2      |  45     |         |  264    |  45     |          |
|  GLDCHECKSUM      |  Char        |  32     |  System.String    |  32     |  46     |         |  268    |  46     |          |
|  EXPID            |  Char        |  10     |  System.String    |  10     |  47     |         |  334    |  47     |          |

package wdc.disk.ecs.apps.MQUpload.generator;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import java.io.FileWriter;
import java.io.Writer;
import java.util.Arrays;
import java.util.List;
import java.util.Properties;

public class MapperGenerator {
    private final VelocityEngine ve;
    
    public MapperGenerator() {
        ve = new VelocityEngine();
        Properties props = new Properties();
        // Update deprecated properties
        props.setProperty("resource.loaders", "classpath");
        props.setProperty("resource.loader.classpath.class", ClasspathResourceLoader.class.getName());
        ve.init(props);
    }
    
    public void generateMapper(String templatePath, String outputPath, MapperConfig config) {
        try {
            Template template = ve.getTemplate(templatePath);
            VelocityContext context = new VelocityContext();
            
            // Set template variables
            context.put("namespace", config.getNamespace());
            context.put("tableName", config.getTableName());
            context.put("primaryKeys", config.getPrimaryKeys());
            context.put("timestampColumn", config.getTimestampColumn());
            
            // Generate the file
            try (Writer writer = new FileWriter(outputPath)) {
                template.merge(context, writer);
            }
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate mapper XML", e);
        }
    }
    
    public static class MapperConfig {
        private String namespace;
        private String tableName;
        private List<String> primaryKeys;
        private String timestampColumn;
        
        // Getters and setters
        public String getNamespace() { return namespace; }
        public void setNamespace(String namespace) { this.namespace = namespace; }
        public String getTableName() { return tableName; }
        public void setTableName(String tableName) { this.tableName = tableName; }
        public List<String> getPrimaryKeys() { return primaryKeys; }
        public void setPrimaryKeys(List<String> primaryKeys) { this.primaryKeys = primaryKeys; }
        public String getTimestampColumn() { return timestampColumn; }
        public void setTimestampColumn(String timestampColumn) { this.timestampColumn = timestampColumn; }
    }
    
    public static void main(String[] args) {
        MapperGenerator generator = new MapperGenerator();
        
        MapperConfig config = new MapperConfig();
        config.setNamespace("wdc.disk.ecs.apps.MQUpload.mapper.LID_Mapper");
        config.setTableName("LID");
        config.setPrimaryKeys(Arrays.asList("LOT", "DT_ENTRY"));
        config.setTimestampColumn("DTINSERT");
        
        // Use direct template path instead of getResource
        generator.generateMapper(
            "mapper/MapperTemplate.vm",  // Template should be in src/main/resources/mapper/
            "src/main/resources/mapper/generatedXML/LID_Mapper.xml",
            config
        );
    }
}

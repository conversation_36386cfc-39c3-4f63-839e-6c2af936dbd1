package wdc.disk.ecs.apps.MQUpload.service;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.ProducerProperties;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
import wdc.disk.ecs.apps.MQUpload.model.ConsumerProperties;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;
import org.yaml.snakeyaml.Yaml;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.util.Map;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;

public class ConfigurationLoader {
    // Constants
    private static final String ENVIRONMENT_PROPERTY = "app.env";
    private static final String DEFAULT_ENVIRONMENT = "dev_localhost";


    private final LogStream log;
    private String configPath;
    private String environment;
    private Map<String, Object> config;

    // Properties objects
    private RabbitMQProperties rabbitMQProperties;
    private ProducerProperties producerProperties;
    private ConsumerProperties consumerProperties;
    private TableSchemaProperties tableSchemaProperties;

    public ConfigurationLoader(LogStream log, String configPath) throws SFTPSendException {
        this(log, configPath, System.getProperty(ENVIRONMENT_PROPERTY, DEFAULT_ENVIRONMENT));
    }

    public ConfigurationLoader(LogStream log, String configPath, String environment) throws SFTPSendException {
        this.log = log;
        this.configPath = configPath;
        this.environment = environment;

        // Initialize properties objects
        this.rabbitMQProperties = new RabbitMQProperties();
        this.producerProperties = new ProducerProperties();
        this.consumerProperties = new ConsumerProperties();
        this.tableSchemaProperties = new TableSchemaProperties();

        loadConfiguration();
    }

    public void loadConfiguration() throws SFTPSendException {
        log.write(LogStream.INFORMATION_MESSAGE, "Loading configuration for environment: '" + this.environment + "'");
        this.config = loadAndMergeConfigs();

        // Parse configuration into properties objects
        parseConfiguration();
    }

    private Map<String, Object> loadAndMergeConfigs() throws SFTPSendException {
        Yaml yaml = new Yaml();
        File baseFile = new File(this.configPath, "MQ_Upload.yml");
        if (!baseFile.exists()) {
            throw new SFTPSendException("FATAL: Base configuration file not found in path: " + baseFile.getAbsolutePath());
        }

        Map<String, Object> baseConfig;
        try (InputStream input = new FileInputStream(baseFile)) {
            baseConfig = yaml.load(input);
        } catch (Exception e) {
            throw new SFTPSendException("Could not read or parse base YAML file: " + baseFile.getAbsolutePath(), e);
        }

        String envFileName = "MQ_Upload." + this.environment + ".yml";
        File envFile = new File(this.configPath, envFileName);
        if (envFile.exists()) {
            try (InputStream input = new FileInputStream(envFile)) {
                Map<String, Object> envConfig = yaml.load(input);
                if (envConfig != null) {
                    log.write(LogStream.INFORMATION_MESSAGE, "Found and loaded environment-specific config: " + envFileName);
                    baseConfig = deepMerge(baseConfig, envConfig);
                }
            } catch (Exception e) {
                log.write(LogStream.WARNING_MESSAGE, "Error reading or parsing environment YAML file: " + envFile.getAbsolutePath());
                log.writeExceptionStack(e);
            }
        } else {
            // Log warning when environment-specific config is not found
            log.write(LogStream.WARNING_MESSAGE, "Environment-specific config not found for '" + this.environment + "'. Using base config only.");
        }
        return baseConfig;
    }

    public Map<String, Object> getConfig() {
        return this.config;
    }

    @SuppressWarnings("unchecked")
    private Map<String, Object> deepMerge(Map<String, Object> base, Map<String, Object> override) {
        Map<String, Object> merged = new LinkedHashMap<>(base);
        for (String key : override.keySet()) {
            Object overrideValue = override.get(key);
            Object baseValue = merged.get(key);
            if (baseValue instanceof Map && overrideValue instanceof Map) {
                merged.put(key, deepMerge((Map<String, Object>) baseValue, (Map<String, Object>) overrideValue));
            } else {
                merged.put(key, overrideValue);
            }
        }
        return merged;
    }

    @SuppressWarnings("unchecked")
    private void parseConfiguration() throws SFTPSendException {
        if (config == null) {
            throw new SFTPSendException("Configuration not loaded");
        }

        // Parse RabbitMQ configuration
        Map<String, Object> rabbitMQConfig = (Map<String, Object>) config.get("rabbitmq");
        if (rabbitMQConfig != null) {
            loadRabbitMQConfig(rabbitMQConfig);
        }

        // Parse Producer configuration
        Map<String, Object> producerConfig = (Map<String, Object>) config.get("producer");
        if (producerConfig != null) {
            loadProducerConfig(producerConfig);
        }

        // Parse Consumer configuration
        Map<String, Object> consumerConfig = (Map<String, Object>) config.get("consumer");
        if (consumerConfig != null) {
            loadConsumerConfig(consumerConfig);
        }

        // Parse Table Schema configuration
        Map<String, Object> tableSchemaConfig = (Map<String, Object>) config.get("table_schema");
        if (tableSchemaConfig != null) {
            loadTableSchemaConfig(tableSchemaConfig);
        }
    }

    @SuppressWarnings("unchecked")
    private void loadRabbitMQConfig(Map<String, Object> rabbitMQConfig) {
        // Handle host - can be single string or array
        Object hostObj = rabbitMQConfig.get("host");
        if (hostObj instanceof String) {
            rabbitMQProperties.setRabbitMQHosts(new String[]{(String) hostObj});
        } else if (hostObj instanceof List) {
            List<String> hostList = (List<String>) hostObj;
            rabbitMQProperties.setRabbitMQHosts(hostList.toArray(new String[0]));
        }

        // Handle other properties
        if (rabbitMQConfig.get("port") != null) {
            rabbitMQProperties.setRabbitMQPort(getInteger(rabbitMQConfig, "port"));
        }
        if (rabbitMQConfig.get("username") != null) {
            rabbitMQProperties.setRabbitMQUsername(getString(rabbitMQConfig, "username"));
        }
        if (rabbitMQConfig.get("password") != null) {
            rabbitMQProperties.setRabbitMQPassword(getString(rabbitMQConfig, "password"));
        }
        if (rabbitMQConfig.get("exchange") != null) {
            rabbitMQProperties.setRabbitMQExchange(getString(rabbitMQConfig, "exchange"));
        }

        // Handle queue config path
        String queueConfigPath = resolvePath(rabbitMQConfig.get("queue_config_path"));
        if (queueConfigPath != null) {
            rabbitMQProperties.setRabbitMQQueueConfigPath(queueConfigPath);
        }
    }

    // Getter methods for properties
    public RabbitMQProperties getRabbitMQProperties() {
        return rabbitMQProperties;
    }

    public ProducerProperties getProducerProperties() {
        return producerProperties;
    }

    public ConsumerProperties getConsumerProperties() {
        return consumerProperties;
    }

    public TableSchemaProperties getTableSchemaProperties() {
        return tableSchemaProperties;
    }

    public String getAppRoot() {
        return getString(config, "app_root");
    }

    @SuppressWarnings("unchecked")
    private void loadProducerConfig(Map<String, Object> producerConfig) {
        // Handle path objects or strings for directories
        String pdsDir = resolvePath(producerConfig.get("pds_dir"));
        String dbMsgIdProfile = resolvePath(producerConfig.get("db_msg_id_profile"));

        // Set required string properties
        producerProperties.setPdsDir(pdsDir != null ? normalizeAndEnsureEndingPathSeparator(pdsDir) : null);
        producerProperties.setDbMsgIdProfile(dbMsgIdProfile);

        // Set other properties
        if (producerConfig.get("monitor_interval") != null) {
            producerProperties.setMonitorInterval(getInteger(producerConfig, "monitor_interval"));
        }

        // Handle housekeeping configuration
        Map<String, Object> housekeepingConfig = (Map<String, Object>) producerConfig.get("housekeeping");
        if (housekeepingConfig != null) {
            producerProperties.setHousekeepingEnabled(getBoolean(housekeepingConfig, "enabled"));
            if (housekeepingConfig.get("interval_hours") != null) {
                producerProperties.setHousekeepingIntervalHours(getInteger(housekeepingConfig, "interval_hours"));
            }
            if (housekeepingConfig.get("retention_days") != null) {
                producerProperties.setHousekeepingRetentionDays(getInteger(housekeepingConfig, "retention_days"));
            }

            // Handle directories list
            Object directoriesObj = housekeepingConfig.get("directories");
            if (directoriesObj instanceof List) {
                List<String> directories = (List<String>) directoriesObj;
                producerProperties.setHousekeepingDirectories(directories);
            }
        }
    }

    @SuppressWarnings("unchecked")
    private void loadConsumerConfig(Map<String, Object> consumerConfig) {
        // Handle path resolution
        String mybatisConfigPath = resolvePath(consumerConfig.get("mybatis_config_path"));
        String mappingsPath = resolvePath(consumerConfig.get("mappings_path"));
        String profilesPath = resolvePath(consumerConfig.get("profiles_path"));

        consumerProperties.setMybatisConfigPath(mybatisConfigPath);
        consumerProperties.setMappingsPath(mappingsPath);
        consumerProperties.setProfilesPath(profilesPath);

        // Set parser class name
        if (consumerConfig.get("parser_class_name") != null) {
            consumerProperties.setParserClassName(getString(consumerConfig, "parser_class_name"));
        }

        // Handle queues configuration
        Object queuesObj = consumerConfig.get("queues");
        if (queuesObj instanceof List) {
            List<Map<String, Object>> queueConfigs = (List<Map<String, Object>>) queuesObj;
            List<ConsumerProperties.QueueConfig> queues = new ArrayList<>();

            for (Map<String, Object> queueMap : queueConfigs) {
                ConsumerProperties.QueueConfig queueConfig = new ConsumerProperties.QueueConfig();
                queueConfig.setName(getString(queueMap, "name"));
                queueConfig.setDescription(getString(queueMap, "description"));
                queueConfig.setEnabled(getBoolean(queueMap, "enabled"));
                queueConfig.setRetryCount(getInteger(queueMap, "retry_count"));
                queueConfig.setRetryDelay(getLong(queueMap, "retry_delay"));
                queueConfig.setPrefetchCount(getInteger(queueMap, "prefetch_count"));
                queueConfig.setProcessingInterval(getLong(queueMap, "processing_interval"));
                queues.add(queueConfig);
            }

            consumerProperties.setQueues(queues);
        }
    }

    private void loadTableSchemaConfig(Map<String, Object> tableSchemaConfig) {
        String mappingsPath = resolvePath(tableSchemaConfig.get("message_mappings_path"));
        String profilesPath = resolvePath(tableSchemaConfig.get("profile_base_path"));

        tableSchemaProperties.setMappingsPath(mappingsPath);
        tableSchemaProperties.setProfilesPath(profilesPath);
    }

    // Helper methods for type-safe config reading and path resolution
    private String getString(Map<String, Object> config, String key) {
        Object value = config.get(key);
        return value != null ? value.toString() : null;
    }

    private int getInteger(Map<String, Object> config, String key) {
        Object value = config.get(key);
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                return 0;
            }
        }
        return 0;
    }

    private long getLong(Map<String, Object> config, String key) {
        Object value = config.get(key);
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return 0L;
            }
        }
        return 0L;
    }

    private boolean getBoolean(Map<String, Object> config, String key) {
        Object value = config.get(key);
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        if (value instanceof String) {
            return Boolean.parseBoolean((String) value);
        }
        return false;
    }

    @SuppressWarnings("unchecked")
    private String resolvePath(Object pathObj) {
        if (pathObj == null) {
            return null;
        }

        if (pathObj instanceof String) {
            return (String) pathObj;
        }

        if (pathObj instanceof Map) {
            Map<String, Object> pathMap = (Map<String, Object>) pathObj;
            String base = getString(pathMap, "base");
            String relative = getString(pathMap, "relative");

            if (base != null && relative != null) {
                // Resolve base path from config
                String basePath = null;
                if ("app_root".equals(base)) {
                    basePath = getString(config, "app_root");
                } else if ("mq_storage_root".equals(base)) {
                    basePath = getString(config, "mq_storage_root");
                } else {
                    basePath = base; // Use as literal path
                }

                if (basePath != null) {
                    return new File(basePath, relative).getAbsolutePath();
                }
            }
        }

        return pathObj.toString();
    }

    private String normalizeAndEnsureEndingPathSeparator(String path) {
        if (path == null || path.isEmpty()) {
            return path;
        }

        // Normalize path separators
        String normalized = path.replace('/', File.separatorChar).replace('\\', File.separatorChar);

        // Ensure ending path separator
        if (!normalized.endsWith(File.separator)) {
            normalized += File.separator;
        }

        return normalized;
    }
}

* Table: DB2SYS.BATCHINFO(BINARY format)
*| ColumnName       | DBColType   | DBColLen | SysType         | BinLength | ColNo | NULLS | StartIdx | ValueIdx | UniIdx |
| SPINDLE          | SmallInt    | 2        | System.Int16    | 2         | 0     | Y     | 0        | 0        | Y      |
| BATCHID          | Integer     | 4        | System.Int32    | 4         | 1     | Y     | 8        | 1        | Y      |
| BATCHTIME        | Timestamp   | 4        | System.DateTime | 4         | 2     |       | 16       | 2        |        |
| EXPID            | Char        | 10       | System.String   | 10        | 3     |       | 24       | 3        |        |
| LOT              | Char        | 10       | System.String   | 10        | 4     |       | 46       | 4        | Y      |
| VARIETY          | Char        | 14       | System.String   | 14        | 5     |       | 68       | 5        |        |
| PRODUCT          | Char        | 6        | System.String   | 6         | 6     |       | 98       | 6        |        |
| PRODTYPE         | Char        | 1        | System.String   | 1         | 7     |       | 112      | 7        |        |
| RESOURCE         | Char        | 10       | System.String   | 10        | 8     |       | 114      | 8        |        |
| DTINSERT         | Timestamp   | 4        | System.DateTime | 4         | 9     |       | 0        | 9        |        |
| CORRELID         | Integer     | 4        | System.Int32    | 4         | 10    |       | 0        | 10       |        |

package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.Properties;

import org.junit.Test;

import javafx.scene.media.VideoTrack;

public class TestLoadProp {
	@Test
	public void testLoad() {
		Properties prop = new Properties();
		
		try {
			String propPath = "C:\\Users\\<USER>\\Desktop\\" + "log.properties" ;
			File propFile = new File(propPath);
			if(!propFile.exists()) {
				propFile.createNewFile();
				writeCurrentTimeToPropertieyFile(prop, propFile);
				return;
			}else {
				//Update time to file
				writeCurrentTimeToPropertieyFile(prop, propFile);
				return;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Test
	public void testCompare() {
		Properties prop = new Properties();

		try {
			String propPath = "C:\\Users\\<USER>\\Desktop\\" + "log.properties" ;
			File propFile = new File(propPath);
			if(!propFile.exists()) {
				propFile.createNewFile();
				writeCurrentTimeToPropertieyFile(prop, propFile);
				return;
			}else {
				//Update time to file
				long currentTimeMillis = System.currentTimeMillis();
//				long gap = ( 1 * 60 * 60 * 1000);
				long gap = ( 60 * 1000);
				long fromPorp = getFromPorp();
				if(currentTimeMillis - gap > fromPorp) {
					System.out.println("currentTimeMillis - gap > fromPorp" + " : true");
				}else {
					System.out.println("currentTimeMillis - gap > fromPorp" + " : false");
				}
				
//				writeCurrentTimeToPropertieyFile(prop, propFile);
				return;
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}

	public long getFromPorp() throws FileNotFoundException, IOException {
		String propPath = "C:\\Users\\<USER>\\Desktop\\" + "log.properties" ;
		File propFile = new File(propPath);
		Properties prop = new Properties();
		prop.load(new FileInputStream(propFile));
		
		String lastTime = prop.getProperty("LastDirCreatedTimeMillis");
		return Long.parseLong(lastTime);
	}

	private void writeCurrentTimeToPropertieyFile(Properties prop, File propFile)
			throws FileNotFoundException, IOException {
		long current =  System.currentTimeMillis();
		prop.setProperty("LastDirCreatedTimeMillis", Long.toString(current));
		writeToProp(prop, propFile);
	}
	
	

	private void writeToProp(Properties prop, File propFile) throws FileNotFoundException, IOException {
		FileOutputStream fileOutputStream = new FileOutputStream(propFile);
		prop.store(fileOutputStream, "Store log file folder create time");
		fileOutputStream.close();
	}
	
	@Test
	public void testLoad2() throws FileNotFoundException, IOException {
		FolderLastCreatedUtil folderLastCreatedUtil = new FolderLastCreatedUtil("C:\\Users\\<USER>\\Desktop\\" + "log.properties" );
		long latestFolderTimeFromPorp = folderLastCreatedUtil.getLatestFolderTimeFromPorp();
		System.out.println("last:" + latestFolderTimeFromPorp);
	}
	
	@Test
	public void testCompareByMinute() {
		//Read property file
		FolderLastCreatedUtil folderLastCreatedUtil = new FolderLastCreatedUtil("C:\\Users\\<USER>\\Desktop\\" + "folderCreate.properties");
		try {
			folderLastCreatedUtil.getLatestFolderTimeFromPorp();
			boolean compare = folderLastCreatedUtil.compareByMillis(1*60*1000);
			System.out.println("compare :" + compare);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	@Test
	public void testClass() {
		String folderName = "";
		FolderLastCreatedUtil folderLastCreatedUtil = new FolderLastCreatedUtil("C:\\Users\\<USER>\\Desktop\\" + "folderCreate.properties");
		try {
			File propFile = folderLastCreatedUtil.getPropFile();
			if(!propFile.exists()) {
				propFile.createNewFile();
				folderLastCreatedUtil.writeCurrentTimeToPropertieyFile();
			}else {
				boolean exceedTime = folderLastCreatedUtil.compare(Integer.parseInt("1"));
				if(exceedTime) {
					folderLastCreatedUtil.writeCurrentTimeToPropertieyFile();
					folderName = folderLastCreatedUtil.createFolderName();
				}else {
					long latestTime = folderLastCreatedUtil.getLatestFolderTimeFromPorp();
					folderName = folderLastCreatedUtil.getFolderNameFromTime(latestTime);
				}					
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	
	
	
	
}

package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;

/*
 * For example:
 *    fileName:  209_LUBEL54_F0412145931.400
 *    lastModify is file property
 *    fileNameDate is extracted from F0412145931 , then convert to long
 *    fileLastIndex is 400
 */
public class FileEntity {
	private File file;
	private String fileName;
	private long lastModify;
	private long fileNameDate;
	private int fileLastIndex;
	
	public FileEntity(String fileName, long lastModify, long fileNameDate, int fileLastIndex) {
		super();
		this.fileName = fileName;
		this.lastModify = lastModify;
		this.fileNameDate = fileNameDate;
		this.fileLastIndex = fileLastIndex;
	}
	
	public FileEntity(File file, String fileName, long lastModify, long fileNameDate, int fileLastIndex) {
		super();
		this.file = file;
		this.fileName = fileName;
		this.lastModify = lastModify;
		this.fileNameDate = fileNameDate;
		this.fileLastIndex = fileLastIndex;
	}

	public String getFileName() {
		return fileName;
	}
	public void setFileName(String fileName) {
		this.fileName = fileName;
	}
	public long getLastModify() {
		return lastModify;
	}
	public void setLastModify(long lastModify) {
		this.lastModify = lastModify;
	}
	public long getFileNameDate() {
		return fileNameDate;
	}
	public void setFileNameDate(long fileNameDate) {
		this.fileNameDate = fileNameDate;
	}
	public int getFileLastIndex() {
		return fileLastIndex;
	}
	public void setFileLastIndex(int fileLastIndex) {
		this.fileLastIndex = fileLastIndex;
	}
	
	public File getFile() {
		return file;
	}

	public void setFile(File file) {
		this.file = file;
	}

	@Override
	public String toString() {
		return "File name:" + fileName; 
	}
}

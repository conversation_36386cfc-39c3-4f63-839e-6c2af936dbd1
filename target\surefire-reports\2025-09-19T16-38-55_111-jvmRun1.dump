# Created at 2025-09-19T16:40:12.965
Exiting self fork JV<PERSON>. Received SHUTDOWN command from <PERSON><PERSON> shutdown hook.
Thread dump before exiting the process (33428@CND1332W93):
"main" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base/java.lang.Thread.sleep(Native Method)
        at java.base/java.lang.Thread.sleep(Thread.java:1015)
        at wdc.disk.ecs.apps.SFTPSend.thread.TestMonitorSftpThread.testThread(TestMonitorSftpThread.java:17)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
        at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
        at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
        at java.base/java.lang.reflect.Method.invoke(Method.java:568)
        at org.junit.platform.commons.util.ReflectionUtils.invokeMethod(ReflectionUtils.java:727)
        at org.junit.jupiter.engine.execution.MethodInvocation.proceed(MethodInvocation.java:60)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$ValidatingInvocation.proceed(InvocationInterceptorChain.java:131)
        at org.junit.jupiter.engine.extension.TimeoutExtension.intercept(TimeoutExtension.java:156)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestableMethod(TimeoutExtension.java:147)
        at org.junit.jupiter.engine.extension.TimeoutExtension.interceptTestMethod(TimeoutExtension.java:86)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor$$Lambda$180/0x00000000f963eac8.apply(Unknown Source)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall.lambda$ofVoidMethod$0(InterceptingExecutableInvoker.java:103)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$ReflectiveInterceptorCall$$Lambda$181/0x00000000f963f360.apply(Unknown Source)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.lambda$invoke$0(InterceptingExecutableInvoker.java:93)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker$$Lambda$356/0x00000000f96bc0e0.apply(Unknown Source)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain$InterceptedInvocation.proceed(InvocationInterceptorChain.java:106)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.proceed(InvocationInterceptorChain.java:64)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.chainAndInvoke(InvocationInterceptorChain.java:45)
        at org.junit.jupiter.engine.execution.InvocationInterceptorChain.invoke(InvocationInterceptorChain.java:37)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:92)
        at org.junit.jupiter.engine.execution.InterceptingExecutableInvoker.invoke(InterceptingExecutableInvoker.java:86)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.lambda$invokeTestMethod$7(TestMethodTestDescriptor.java:217)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor$$Lambda$392/0x00000000f96f5eb0.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.invokeTestMethod(TestMethodTestDescriptor.java:213)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:138)
        at org.junit.jupiter.engine.descriptor.TestMethodTestDescriptor.execute(TestMethodTestDescriptor.java:68)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:151)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$286/0x00000000f95332f8.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$285/0x00000000f9533140.invoke(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$284/0x00000000f95837f8.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService$$Lambda$290/0x00000000f96a6bf8.accept(Unknown Source)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$286/0x00000000f95332f8.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$285/0x00000000f9533140.invoke(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$284/0x00000000f95837f8.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService$$Lambda$290/0x00000000f96a6bf8.accept(Unknown Source)
        at java.base/java.util.ArrayList.forEach(ArrayList.java:1511)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.invokeAll(SameThreadHierarchicalTestExecutorService.java:41)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$6(NodeTestTask.java:155)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$286/0x00000000f95332f8.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$8(NodeTestTask.java:141)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$285/0x00000000f9533140.invoke(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.Node.around(Node.java:137)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.lambda$executeRecursively$9(NodeTestTask.java:139)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask$$Lambda$284/0x00000000f95837f8.execute(Unknown Source)
        at org.junit.platform.engine.support.hierarchical.ThrowableCollector.execute(ThrowableCollector.java:73)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.executeRecursively(NodeTestTask.java:138)
        at org.junit.platform.engine.support.hierarchical.NodeTestTask.execute(NodeTestTask.java:95)
        at org.junit.platform.engine.support.hierarchical.SameThreadHierarchicalTestExecutorService.submit(SameThreadHierarchicalTestExecutorService.java:35)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestExecutor.execute(HierarchicalTestExecutor.java:57)
        at org.junit.platform.engine.support.hierarchical.HierarchicalTestEngine.execute(HierarchicalTestEngine.java:54)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:147)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:127)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:90)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.lambda$execute$0(EngineExecutionOrchestrator.java:55)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator$$Lambda$239/0x00000000f96749b8.accept(Unknown Source)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.withInterceptedStreams(EngineExecutionOrchestrator.java:102)
        at org.junit.platform.launcher.core.EngineExecutionOrchestrator.execute(EngineExecutionOrchestrator.java:54)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:114)
        at org.junit.platform.launcher.core.DefaultLauncher.execute(DefaultLauncher.java:86)
        at org.junit.platform.launcher.core.DefaultLauncherSession$DelegatingLauncher.execute(DefaultLauncherSession.java:86)
        at org.apache.maven.surefire.junitplatform.LazyLauncher.execute(LazyLauncher.java:50)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.execute(JUnitPlatformProvider.java:184)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invokeAllTests(JUnitPlatformProvider.java:148)
        at org.apache.maven.surefire.junitplatform.JUnitPlatformProvider.invoke(JUnitPlatformProvider.java:122)
        at org.apache.maven.surefire.booter.ForkedBooter.runSuitesInProcess(ForkedBooter.java:385)
        at org.apache.maven.surefire.booter.ForkedBooter.execute(ForkedBooter.java:162)
        at org.apache.maven.surefire.booter.ForkedBooter.run(ForkedBooter.java:507)
        at org.apache.maven.surefire.booter.ForkedBooter.main(ForkedBooter.java:495)

"JIT Compilation Thread-000" 
   java.lang.Thread.State: RUNNABLE

"JIT Compilation Thread-001 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT Compilation Thread-002 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT Compilation Thread-003 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT Compilation Thread-004 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT Compilation Thread-005 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT Compilation Thread-006 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT Diagnostic Compilation Thread-007 Suspended" 
   java.lang.Thread.State: RUNNABLE

"JIT-SamplerThread" 
   java.lang.Thread.State: TIMED_WAITING

"IProfiler" 
   java.lang.Thread.State: RUNNABLE

"Common-Cleaner" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base/java.lang.Object.wait(Native Method)
        at java.base/java.lang.Object.wait(Object.java:221)
        at java.base/java.lang.ref.ReferenceQueue.remove(ReferenceQueue.java:138)
        at java.base/jdk.internal.ref.CleanerImpl.run(CleanerImpl.java:140)
        at java.base/java.lang.Thread.run(Thread.java:884)
        at java.base/jdk.internal.misc.InnocuousThread.run(InnocuousThread.java:162)

"Concurrent Mark Helper" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"GC Worker" 
   java.lang.Thread.State: RUNNABLE

"Attach API wait loop" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base/java.lang.Thread.sleep(Native Method)
        at java.base/java.lang.Thread.sleep(Thread.java:1015)
        at java.base/openj9.internal.tools.attach.target.WaitLoop.checkReplyAndCreateAttachment(WaitLoop.java:142)
        at java.base/openj9.internal.tools.attach.target.WaitLoop.waitForNotification(WaitLoop.java:117)
        at java.base/openj9.internal.tools.attach.target.WaitLoop.run(WaitLoop.java:157)

"Finalizer thread" 
   java.lang.Thread.State: RUNNABLE

"surefire-forkedjvm-stream-flusher" 
   java.lang.Thread.State: TIMED_WAITING
        at java.base/jdk.internal.misc.Unsafe.park(Native Method)
        at java.base/java.util.concurrent.locks.LockSupport.parkNanos(LockSupport.java:252)
        at java.base/java.util.concurrent.locks.AbstractQueuedSynchronizer$ConditionObject.awaitNanos(AbstractQueuedSynchronizer.java:1672)
        at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:1182)
        at java.base/java.util.concurrent.ScheduledThreadPoolExecutor$DelayedWorkQueue.take(ScheduledThreadPoolExecutor.java:899)
        at java.base/java.util.concurrent.ThreadPoolExecutor.getTask(ThreadPoolExecutor.java:1062)
        at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1122)
        at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
        at java.base/java.lang.Thread.run(Thread.java:884)

"surefire-forkedjvm-command-thread" 
   java.lang.Thread.State: RUNNABLE
        at java.management/com.ibm.java.lang.management.internal.ThreadMXBeanImpl.getMultiThreadInfoImpl(Native Method)
        at java.management/com.ibm.java.lang.management.internal.ThreadMXBeanImpl.getThreadInfo(ThreadMXBeanImpl.java:270)
        at org.apache.maven.surefire.booter.ForkedBooter.generateThreadDump(ForkedBooter.java:579)
        at org.apache.maven.surefire.booter.ForkedBooter.access$600(ForkedBooter.java:79)
        at org.apache.maven.surefire.booter.ForkedBooter$4.update(ForkedBooter.java:315)
        at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.callListeners(CommandReader.java:357)
        at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.exitByConfiguration(CommandReader.java:367)
        at org.apache.maven.surefire.booter.CommandReader$CommandRunnable.run(CommandReader.java:330)
        at java.base/java.lang.Thread.run(Thread.java:884)

"file lock watchdog" 
   java.lang.Thread.State: WAITING
        at java.base/java.lang.Object.wait(Native Method)
        at java.base/java.lang.Object.wait(Object.java:192)
        at java.base/java.util.TimerThread.mainLoop(Timer.java:537)
        at java.base/java.util.TimerThread.run(Timer.java:516)




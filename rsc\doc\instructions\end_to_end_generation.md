# End-to-End Generation Workflow

## 1. Objective

This document outlines the high-level workflow for generating all necessary files and configurations for a new data table. The process starts with a single JSON string and orchestrates the steps detailed in separate, more specific documents.

## 2. Input JSON

The entire process is initiated by a JSON string that defines the table and message information. For example:

```json
{
  "tabCode": "GLDRSLT_2",
  "mqMessageId": "2",
  "msgType": "Binary",
  "tableName": "GLDRSLT"
}
```

## 3. Process Flow

To add support for a new table, follow these steps in order. Each step refers to a detailed guide for specific instructions.

### Step 1: Generate the Profile File

First, you need to generate an enhanced profile (`.pro`) file for the new table. This process adds the `UniIdx` column required for data processing.

**For detailed instructions, please refer to:**
`rsc/doc/instructions/profile_convertion.md`

### Step 2: Generate POJO and Mapper Files

Once the profile file is created, you can generate the corresponding Plain Old Java Object (POJO) and Mapper files.

**For detailed instructions on configuring and running the generator, please refer to:**
`rsc/doc/instructions/POJO_Mapper_generator.md`

### Step 3: Configure the Message Queue

Finally, you must configure the message queue to handle the new data type. This involves updating both the queue definition and the consumer configuration.

**For detailed instructions on editing the queue configuration files, please refer to:**
`rsc/doc/instructions/queue_configuration.md`
*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  KEY                   |  BigInt      |  8      |  System.Int64          |  8      |  3      |         |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |         |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  RECIPENAME            |  Char        |  12     |  System.String         |  12     |  8      |         |  0      |  8      |  
|  AVG_FRONTFORCER       |  Decimal     |  9      |  System.Decimal        |  9      |  9      |         |  0      |  9      |  
|  RNG_FRONTFORCER       |  Decimal     |  9      |  System.Decimal        |  9      |  10     |         |  0      |  10     |  
|  AVG_FRONTTENSIONR     |  Decimal     |  9      |  System.Decimal        |  9      |  11     |         |  0      |  11     |  
|  RNG_FRONTTENSIONR     |  Decimal     |  9      |  System.Decimal        |  9      |  12     |         |  0      |  12     |  
|  AVG_BACKFORCER        |  Decimal     |  9      |  System.Decimal        |  9      |  13     |         |  0      |  13     |  
|  RNG_BACKFORCER        |  Decimal     |  9      |  System.Decimal        |  9      |  14     |         |  0      |  14     |  
|  AVG_BACKTENSIONR      |  Decimal     |  9      |  System.Decimal        |  9      |  15     |         |  0      |  15     |  
|  RNG_BACKTENSIONR      |  Decimal     |  9      |  System.Decimal        |  9      |  16     |         |  0      |  16     |  
|  AVG_FRONTSTEPSR       |  Decimal     |  9      |  System.Decimal        |  9      |  17     |         |  0      |  17     |  
|  RNG_FRONTSTEPSR       |  Decimal     |  9      |  System.Decimal        |  9      |  18     |         |  0      |  18     |  
|  AVG_FRONTTAPEPOSITIONR  |  Decimal     |  9      |  System.Decimal        |  9      |  19     |         |  0      |  19     |  
|  RNG_FRONTTAPEPOSITIONR  |  Decimal     |  9      |  System.Decimal        |  9      |  20     |         |  0      |  20     |  
|  AVG_FRONTTAPESCALEFACTORR  |  Decimal     |  9      |  System.Decimal        |  9      |  21     |         |  0      |  21     |  
|  RNG_FRONTTAPESCALEFACTORR  |  Decimal     |  9      |  System.Decimal        |  9      |  22     |         |  0      |  22     |  
|  AVG_BACKSTEPSR        |  Decimal     |  9      |  System.Decimal        |  9      |  23     |         |  0      |  23     |  
|  RNG_BACKSTEPSR        |  Decimal     |  9      |  System.Decimal        |  9      |  24     |         |  0      |  24     |  
|  AVG_BACKTAPEPOSITIONR  |  Decimal     |  9      |  System.Decimal        |  9      |  25     |         |  0      |  25     |  
|  RNG_BACKTAPEPOSITIONR  |  Decimal     |  9      |  System.Decimal        |  9      |  26     |         |  0      |  26     |  
|  AVG_BACKTAPESCALEFACTORR  |  Decimal     |  9      |  System.Decimal        |  9      |  27     |         |  0      |  27     |  
|  RNG_BACKTAPESCALEFACTORR  |  Decimal     |  9      |  System.Decimal        |  9      |  28     |         |  0      |  28     |  
|  AVG_FRONTFORCEL       |  Decimal     |  9      |  System.Decimal        |  9      |  29     |         |  0      |  29     |  
|  RNG_FRONTFORCEL       |  Decimal     |  9      |  System.Decimal        |  9      |  30     |         |  0      |  30     |  
|  AVG_FRONTTENSIONL     |  Decimal     |  9      |  System.Decimal        |  9      |  31     |         |  0      |  31     |  
|  RNG_FRONTTENSIONL     |  Decimal     |  9      |  System.Decimal        |  9      |  32     |         |  0      |  32     |  
|  AVG_BACKFORCEL        |  Decimal     |  9      |  System.Decimal        |  9      |  33     |         |  0      |  33     |  
|  RNG_BACKFORCEL        |  Decimal     |  9      |  System.Decimal        |  9      |  34     |         |  0      |  34     |  
|  AVG_BACKTENSIONL      |  Decimal     |  9      |  System.Decimal        |  9      |  35     |         |  0      |  35     |  
|  RNG_BACKTENSIONL      |  Decimal     |  9      |  System.Decimal        |  9      |  36     |         |  0      |  36     |  
|  AVG_FRONTSTEPSL       |  Decimal     |  9      |  System.Decimal        |  9      |  37     |         |  0      |  37     |  
|  RNG_FRONTSTEPSL       |  Decimal     |  9      |  System.Decimal        |  9      |  38     |         |  0      |  38     |  
|  AVG_FRONTTAPEPOSITIONL  |  Decimal     |  9      |  System.Decimal        |  9      |  39     |         |  0      |  39     |  
|  RNG_FRONTTAPEPOSITIONL  |  Decimal     |  9      |  System.Decimal        |  9      |  40     |         |  0      |  40     |  
|  AVG_FRONTTAPESCALEFACTORL  |  Decimal     |  9      |  System.Decimal        |  9      |  41     |         |  0      |  41     |  
|  RNG_FRONTTAPESCALEFACTORL  |  Decimal     |  9      |  System.Decimal        |  9      |  42     |         |  0      |  42     |  
|  AVG_BACKSTEPSL        |  Decimal     |  9      |  System.Decimal        |  9      |  43     |         |  0      |  43     |  
|  RNG_BACKSTEPSL        |  Decimal     |  9      |  System.Decimal        |  9      |  44     |         |  0      |  44     |  
|  AVG_BACKTAPEPOSITIONL  |  Decimal     |  9      |  System.Decimal        |  9      |  45     |         |  0      |  45     |  
|  RNG_BACKTAPEPOSITIONL  |  Decimal     |  9      |  System.Decimal        |  9      |  46     |         |  0      |  46     |  
|  AVG_BACKTAPESCALEFACTORL  |  Decimal     |  9      |  System.Decimal        |  9      |  47     |         |  0      |  47     |  
|  RNG_BACKTAPESCALEFACTORL  |  Decimal     |  9      |  System.Decimal        |  9      |  48     |         |  0      |  48     |  
|  AVG_SPINDLESPEED      |  Decimal     |  9      |  System.Decimal        |  9      |  49     |         |  0      |  49     |  
|  RNG_SPINDLESPEED      |  Decimal     |  9      |  System.Decimal        |  9      |  50     |         |  0      |  50     |  
|  AVG_RIGHTPOS          |  Decimal     |  9      |  System.Decimal        |  9      |  51     |         |  0      |  51     |  
|  RNG_RIGHTPOS          |  Decimal     |  9      |  System.Decimal        |  9      |  52     |         |  0      |  52     |  
|  AVG_LEFTPOS           |  Decimal     |  9      |  System.Decimal        |  9      |  53     |         |  0      |  53     |  
|  RNG_LEFTPOS           |  Decimal     |  9      |  System.Decimal        |  9      |  54     |         |  0      |  54     |  
|  AVG_BULKERASEPOS      |  Decimal     |  9      |  System.Decimal        |  9      |  55     |         |  0      |  55     |  
|  RNG_BULKERASEPOS      |  Decimal     |  9      |  System.Decimal        |  9      |  56     |         |  0      |  56     |  
|  FTPTAPE               |  Char        |  12     |  System.String         |  12     |  57     |         |  0      |  57     |  

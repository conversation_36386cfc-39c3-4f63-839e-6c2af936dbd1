package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSCHANNEL
 */
public class TDSCHANNEL  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private Integer spindle;
    private String channelId;
    private String headId;
    private Double totalTaCnt;
    private Double smallTaCnt;
    private Double mediumTaCnt;
    private Double largeTaCnt;
    private Double monsterTaCnt;
    private Double positiveTaCnt;
    private Double avgClusterMaxAbsAmp;
    private Double totalClusterCnt;
    private Double qualifiedClusterCnt;
    private Double totalCdpCnt;
    private Double totalVoidCnt;
    private Double smallVoidCnt;
    private Double mediumVoidCnt;
    private Double largeVoidCnt;
    private Double lmsCnt;
    private Double cscratchCnt;
    private Double patchCnt;
    private Double taMaxRad;
    private Double taMinRad;
    private Double taMaxArea;
    private Double smallClusterCnt;
    private Double mediumClusterCnt;
    private Double largeClusterCnt;
    private Double totalContamCnt;
    private Double scratchCnt;
    private Double corruptFlag;
    private Double totalTaCntBin1;
    private Double totalTaCntBin2;
    private Double totalTaCntBin3;
    private Double totalTaCntBin4;
    private Double totalTaCntBin5;
    private Double totalTaCntBin6;
    private Double totalTaCntBin7;
    private Double totalTaCntBin8;
    private Double totalTaCntBin9;
    private Double totalTaCntBin10;
    private Double totalTaCntBin11;
    private Double totalTaCntBin12;
    private Double totalTaCntBin13;
    private Double totalTaCntBin14;
    private Double totalTaCntBin15;
    private Double htaCnt;
    private Double ltaCnt;
    private Double widetaCnt;
    private Double largePosTaCnt;
    private Double totalTaPdiCnt;
    private Double totalTaHeaddingCnt;
    private Double totalTaCirbliscCnt;
    private Double totalTaIrrpmrCnt;
    private Double totalTaCirblisrpCnt;
    private Double totalTaCsurfaceCnt;
    private Double totalTaUnknownCnt;
    private Double smallTaPdiCnt;
    private Double smallTaHeaddingCnt;
    private Double smallTaCirbliscCnt;
    private Double smallTaIrrpmrCnt;
    private Double smallTaCirblisrpCnt;
    private Double smallTaCsurfaceCnt;
    private Double smallTaUnknownCnt;
    private Double mediumTaPdiCnt;
    private Double mediumTaHeaddingCnt;
    private Double mediumTaCirbliscCnt;
    private Double mediumTaIrrpmrCnt;
    private Double mediumTaCirblisrpCnt;
    private Double mediumTaCsurfaceCnt;
    private Double mediumTaUnknownCnt;
    private Double largeTaPdiCnt;
    private Double largeTaHeaddingCnt;
    private Double largeTaCirbliscCnt;
    private Double largeTaIrrpmrCnt;
    private Double largeTaCirblisrpCnt;
    private Double largeTaCsurfaceCnt;
    private Double largeTaUnknownCnt;
    private Double carbonVoidCnt;
    private Double lddCnt;
    private Double avgRmsDeltaMv;
    private Double patchTaCnt;
    private Double patchVoidCnt;
    private Double patchContamCnt;
    private Double ncorrCnt;
    private Double corrCnt;

    // Default constructor
    public TDSCHANNEL() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getHeadId() {
        return headId;
    }

    public void setHeadId(String headId) {
        this.headId = headId;
    }

    public Double getTotalTaCnt() {
        return totalTaCnt;
    }

    public void setTotalTaCnt(Double totalTaCnt) {
        this.totalTaCnt = totalTaCnt;
    }

    public Double getSmallTaCnt() {
        return smallTaCnt;
    }

    public void setSmallTaCnt(Double smallTaCnt) {
        this.smallTaCnt = smallTaCnt;
    }

    public Double getMediumTaCnt() {
        return mediumTaCnt;
    }

    public void setMediumTaCnt(Double mediumTaCnt) {
        this.mediumTaCnt = mediumTaCnt;
    }

    public Double getLargeTaCnt() {
        return largeTaCnt;
    }

    public void setLargeTaCnt(Double largeTaCnt) {
        this.largeTaCnt = largeTaCnt;
    }

    public Double getMonsterTaCnt() {
        return monsterTaCnt;
    }

    public void setMonsterTaCnt(Double monsterTaCnt) {
        this.monsterTaCnt = monsterTaCnt;
    }

    public Double getPositiveTaCnt() {
        return positiveTaCnt;
    }

    public void setPositiveTaCnt(Double positiveTaCnt) {
        this.positiveTaCnt = positiveTaCnt;
    }

    public Double getAvgClusterMaxAbsAmp() {
        return avgClusterMaxAbsAmp;
    }

    public void setAvgClusterMaxAbsAmp(Double avgClusterMaxAbsAmp) {
        this.avgClusterMaxAbsAmp = avgClusterMaxAbsAmp;
    }

    public Double getTotalClusterCnt() {
        return totalClusterCnt;
    }

    public void setTotalClusterCnt(Double totalClusterCnt) {
        this.totalClusterCnt = totalClusterCnt;
    }

    public Double getQualifiedClusterCnt() {
        return qualifiedClusterCnt;
    }

    public void setQualifiedClusterCnt(Double qualifiedClusterCnt) {
        this.qualifiedClusterCnt = qualifiedClusterCnt;
    }

    public Double getTotalCdpCnt() {
        return totalCdpCnt;
    }

    public void setTotalCdpCnt(Double totalCdpCnt) {
        this.totalCdpCnt = totalCdpCnt;
    }

    public Double getTotalVoidCnt() {
        return totalVoidCnt;
    }

    public void setTotalVoidCnt(Double totalVoidCnt) {
        this.totalVoidCnt = totalVoidCnt;
    }

    public Double getSmallVoidCnt() {
        return smallVoidCnt;
    }

    public void setSmallVoidCnt(Double smallVoidCnt) {
        this.smallVoidCnt = smallVoidCnt;
    }

    public Double getMediumVoidCnt() {
        return mediumVoidCnt;
    }

    public void setMediumVoidCnt(Double mediumVoidCnt) {
        this.mediumVoidCnt = mediumVoidCnt;
    }

    public Double getLargeVoidCnt() {
        return largeVoidCnt;
    }

    public void setLargeVoidCnt(Double largeVoidCnt) {
        this.largeVoidCnt = largeVoidCnt;
    }

    public Double getLmsCnt() {
        return lmsCnt;
    }

    public void setLmsCnt(Double lmsCnt) {
        this.lmsCnt = lmsCnt;
    }

    public Double getCscratchCnt() {
        return cscratchCnt;
    }

    public void setCscratchCnt(Double cscratchCnt) {
        this.cscratchCnt = cscratchCnt;
    }

    public Double getPatchCnt() {
        return patchCnt;
    }

    public void setPatchCnt(Double patchCnt) {
        this.patchCnt = patchCnt;
    }

    public Double getTaMaxRad() {
        return taMaxRad;
    }

    public void setTaMaxRad(Double taMaxRad) {
        this.taMaxRad = taMaxRad;
    }

    public Double getTaMinRad() {
        return taMinRad;
    }

    public void setTaMinRad(Double taMinRad) {
        this.taMinRad = taMinRad;
    }

    public Double getTaMaxArea() {
        return taMaxArea;
    }

    public void setTaMaxArea(Double taMaxArea) {
        this.taMaxArea = taMaxArea;
    }

    public Double getSmallClusterCnt() {
        return smallClusterCnt;
    }

    public void setSmallClusterCnt(Double smallClusterCnt) {
        this.smallClusterCnt = smallClusterCnt;
    }

    public Double getMediumClusterCnt() {
        return mediumClusterCnt;
    }

    public void setMediumClusterCnt(Double mediumClusterCnt) {
        this.mediumClusterCnt = mediumClusterCnt;
    }

    public Double getLargeClusterCnt() {
        return largeClusterCnt;
    }

    public void setLargeClusterCnt(Double largeClusterCnt) {
        this.largeClusterCnt = largeClusterCnt;
    }

    public Double getTotalContamCnt() {
        return totalContamCnt;
    }

    public void setTotalContamCnt(Double totalContamCnt) {
        this.totalContamCnt = totalContamCnt;
    }

    public Double getScratchCnt() {
        return scratchCnt;
    }

    public void setScratchCnt(Double scratchCnt) {
        this.scratchCnt = scratchCnt;
    }

    public Double getCorruptFlag() {
        return corruptFlag;
    }

    public void setCorruptFlag(Double corruptFlag) {
        this.corruptFlag = corruptFlag;
    }

    public Double getTotalTaCntBin1() {
        return totalTaCntBin1;
    }

    public void setTotalTaCntBin1(Double totalTaCntBin1) {
        this.totalTaCntBin1 = totalTaCntBin1;
    }

    public Double getTotalTaCntBin2() {
        return totalTaCntBin2;
    }

    public void setTotalTaCntBin2(Double totalTaCntBin2) {
        this.totalTaCntBin2 = totalTaCntBin2;
    }

    public Double getTotalTaCntBin3() {
        return totalTaCntBin3;
    }

    public void setTotalTaCntBin3(Double totalTaCntBin3) {
        this.totalTaCntBin3 = totalTaCntBin3;
    }

    public Double getTotalTaCntBin4() {
        return totalTaCntBin4;
    }

    public void setTotalTaCntBin4(Double totalTaCntBin4) {
        this.totalTaCntBin4 = totalTaCntBin4;
    }

    public Double getTotalTaCntBin5() {
        return totalTaCntBin5;
    }

    public void setTotalTaCntBin5(Double totalTaCntBin5) {
        this.totalTaCntBin5 = totalTaCntBin5;
    }

    public Double getTotalTaCntBin6() {
        return totalTaCntBin6;
    }

    public void setTotalTaCntBin6(Double totalTaCntBin6) {
        this.totalTaCntBin6 = totalTaCntBin6;
    }

    public Double getTotalTaCntBin7() {
        return totalTaCntBin7;
    }

    public void setTotalTaCntBin7(Double totalTaCntBin7) {
        this.totalTaCntBin7 = totalTaCntBin7;
    }

    public Double getTotalTaCntBin8() {
        return totalTaCntBin8;
    }

    public void setTotalTaCntBin8(Double totalTaCntBin8) {
        this.totalTaCntBin8 = totalTaCntBin8;
    }

    public Double getTotalTaCntBin9() {
        return totalTaCntBin9;
    }

    public void setTotalTaCntBin9(Double totalTaCntBin9) {
        this.totalTaCntBin9 = totalTaCntBin9;
    }

    public Double getTotalTaCntBin10() {
        return totalTaCntBin10;
    }

    public void setTotalTaCntBin10(Double totalTaCntBin10) {
        this.totalTaCntBin10 = totalTaCntBin10;
    }

    public Double getTotalTaCntBin11() {
        return totalTaCntBin11;
    }

    public void setTotalTaCntBin11(Double totalTaCntBin11) {
        this.totalTaCntBin11 = totalTaCntBin11;
    }

    public Double getTotalTaCntBin12() {
        return totalTaCntBin12;
    }

    public void setTotalTaCntBin12(Double totalTaCntBin12) {
        this.totalTaCntBin12 = totalTaCntBin12;
    }

    public Double getTotalTaCntBin13() {
        return totalTaCntBin13;
    }

    public void setTotalTaCntBin13(Double totalTaCntBin13) {
        this.totalTaCntBin13 = totalTaCntBin13;
    }

    public Double getTotalTaCntBin14() {
        return totalTaCntBin14;
    }

    public void setTotalTaCntBin14(Double totalTaCntBin14) {
        this.totalTaCntBin14 = totalTaCntBin14;
    }

    public Double getTotalTaCntBin15() {
        return totalTaCntBin15;
    }

    public void setTotalTaCntBin15(Double totalTaCntBin15) {
        this.totalTaCntBin15 = totalTaCntBin15;
    }

    public Double getHtaCnt() {
        return htaCnt;
    }

    public void setHtaCnt(Double htaCnt) {
        this.htaCnt = htaCnt;
    }

    public Double getLtaCnt() {
        return ltaCnt;
    }

    public void setLtaCnt(Double ltaCnt) {
        this.ltaCnt = ltaCnt;
    }

    public Double getWidetaCnt() {
        return widetaCnt;
    }

    public void setWidetaCnt(Double widetaCnt) {
        this.widetaCnt = widetaCnt;
    }

    public Double getLargePosTaCnt() {
        return largePosTaCnt;
    }

    public void setLargePosTaCnt(Double largePosTaCnt) {
        this.largePosTaCnt = largePosTaCnt;
    }

    public Double getTotalTaPdiCnt() {
        return totalTaPdiCnt;
    }

    public void setTotalTaPdiCnt(Double totalTaPdiCnt) {
        this.totalTaPdiCnt = totalTaPdiCnt;
    }

    public Double getTotalTaHeaddingCnt() {
        return totalTaHeaddingCnt;
    }

    public void setTotalTaHeaddingCnt(Double totalTaHeaddingCnt) {
        this.totalTaHeaddingCnt = totalTaHeaddingCnt;
    }

    public Double getTotalTaCirbliscCnt() {
        return totalTaCirbliscCnt;
    }

    public void setTotalTaCirbliscCnt(Double totalTaCirbliscCnt) {
        this.totalTaCirbliscCnt = totalTaCirbliscCnt;
    }

    public Double getTotalTaIrrpmrCnt() {
        return totalTaIrrpmrCnt;
    }

    public void setTotalTaIrrpmrCnt(Double totalTaIrrpmrCnt) {
        this.totalTaIrrpmrCnt = totalTaIrrpmrCnt;
    }

    public Double getTotalTaCirblisrpCnt() {
        return totalTaCirblisrpCnt;
    }

    public void setTotalTaCirblisrpCnt(Double totalTaCirblisrpCnt) {
        this.totalTaCirblisrpCnt = totalTaCirblisrpCnt;
    }

    public Double getTotalTaCsurfaceCnt() {
        return totalTaCsurfaceCnt;
    }

    public void setTotalTaCsurfaceCnt(Double totalTaCsurfaceCnt) {
        this.totalTaCsurfaceCnt = totalTaCsurfaceCnt;
    }

    public Double getTotalTaUnknownCnt() {
        return totalTaUnknownCnt;
    }

    public void setTotalTaUnknownCnt(Double totalTaUnknownCnt) {
        this.totalTaUnknownCnt = totalTaUnknownCnt;
    }

    public Double getSmallTaPdiCnt() {
        return smallTaPdiCnt;
    }

    public void setSmallTaPdiCnt(Double smallTaPdiCnt) {
        this.smallTaPdiCnt = smallTaPdiCnt;
    }

    public Double getSmallTaHeaddingCnt() {
        return smallTaHeaddingCnt;
    }

    public void setSmallTaHeaddingCnt(Double smallTaHeaddingCnt) {
        this.smallTaHeaddingCnt = smallTaHeaddingCnt;
    }

    public Double getSmallTaCirbliscCnt() {
        return smallTaCirbliscCnt;
    }

    public void setSmallTaCirbliscCnt(Double smallTaCirbliscCnt) {
        this.smallTaCirbliscCnt = smallTaCirbliscCnt;
    }

    public Double getSmallTaIrrpmrCnt() {
        return smallTaIrrpmrCnt;
    }

    public void setSmallTaIrrpmrCnt(Double smallTaIrrpmrCnt) {
        this.smallTaIrrpmrCnt = smallTaIrrpmrCnt;
    }

    public Double getSmallTaCirblisrpCnt() {
        return smallTaCirblisrpCnt;
    }

    public void setSmallTaCirblisrpCnt(Double smallTaCirblisrpCnt) {
        this.smallTaCirblisrpCnt = smallTaCirblisrpCnt;
    }

    public Double getSmallTaCsurfaceCnt() {
        return smallTaCsurfaceCnt;
    }

    public void setSmallTaCsurfaceCnt(Double smallTaCsurfaceCnt) {
        this.smallTaCsurfaceCnt = smallTaCsurfaceCnt;
    }

    public Double getSmallTaUnknownCnt() {
        return smallTaUnknownCnt;
    }

    public void setSmallTaUnknownCnt(Double smallTaUnknownCnt) {
        this.smallTaUnknownCnt = smallTaUnknownCnt;
    }

    public Double getMediumTaPdiCnt() {
        return mediumTaPdiCnt;
    }

    public void setMediumTaPdiCnt(Double mediumTaPdiCnt) {
        this.mediumTaPdiCnt = mediumTaPdiCnt;
    }

    public Double getMediumTaHeaddingCnt() {
        return mediumTaHeaddingCnt;
    }

    public void setMediumTaHeaddingCnt(Double mediumTaHeaddingCnt) {
        this.mediumTaHeaddingCnt = mediumTaHeaddingCnt;
    }

    public Double getMediumTaCirbliscCnt() {
        return mediumTaCirbliscCnt;
    }

    public void setMediumTaCirbliscCnt(Double mediumTaCirbliscCnt) {
        this.mediumTaCirbliscCnt = mediumTaCirbliscCnt;
    }

    public Double getMediumTaIrrpmrCnt() {
        return mediumTaIrrpmrCnt;
    }

    public void setMediumTaIrrpmrCnt(Double mediumTaIrrpmrCnt) {
        this.mediumTaIrrpmrCnt = mediumTaIrrpmrCnt;
    }

    public Double getMediumTaCirblisrpCnt() {
        return mediumTaCirblisrpCnt;
    }

    public void setMediumTaCirblisrpCnt(Double mediumTaCirblisrpCnt) {
        this.mediumTaCirblisrpCnt = mediumTaCirblisrpCnt;
    }

    public Double getMediumTaCsurfaceCnt() {
        return mediumTaCsurfaceCnt;
    }

    public void setMediumTaCsurfaceCnt(Double mediumTaCsurfaceCnt) {
        this.mediumTaCsurfaceCnt = mediumTaCsurfaceCnt;
    }

    public Double getMediumTaUnknownCnt() {
        return mediumTaUnknownCnt;
    }

    public void setMediumTaUnknownCnt(Double mediumTaUnknownCnt) {
        this.mediumTaUnknownCnt = mediumTaUnknownCnt;
    }

    public Double getLargeTaPdiCnt() {
        return largeTaPdiCnt;
    }

    public void setLargeTaPdiCnt(Double largeTaPdiCnt) {
        this.largeTaPdiCnt = largeTaPdiCnt;
    }

    public Double getLargeTaHeaddingCnt() {
        return largeTaHeaddingCnt;
    }

    public void setLargeTaHeaddingCnt(Double largeTaHeaddingCnt) {
        this.largeTaHeaddingCnt = largeTaHeaddingCnt;
    }

    public Double getLargeTaCirbliscCnt() {
        return largeTaCirbliscCnt;
    }

    public void setLargeTaCirbliscCnt(Double largeTaCirbliscCnt) {
        this.largeTaCirbliscCnt = largeTaCirbliscCnt;
    }

    public Double getLargeTaIrrpmrCnt() {
        return largeTaIrrpmrCnt;
    }

    public void setLargeTaIrrpmrCnt(Double largeTaIrrpmrCnt) {
        this.largeTaIrrpmrCnt = largeTaIrrpmrCnt;
    }

    public Double getLargeTaCirblisrpCnt() {
        return largeTaCirblisrpCnt;
    }

    public void setLargeTaCirblisrpCnt(Double largeTaCirblisrpCnt) {
        this.largeTaCirblisrpCnt = largeTaCirblisrpCnt;
    }

    public Double getLargeTaCsurfaceCnt() {
        return largeTaCsurfaceCnt;
    }

    public void setLargeTaCsurfaceCnt(Double largeTaCsurfaceCnt) {
        this.largeTaCsurfaceCnt = largeTaCsurfaceCnt;
    }

    public Double getLargeTaUnknownCnt() {
        return largeTaUnknownCnt;
    }

    public void setLargeTaUnknownCnt(Double largeTaUnknownCnt) {
        this.largeTaUnknownCnt = largeTaUnknownCnt;
    }

    public Double getCarbonVoidCnt() {
        return carbonVoidCnt;
    }

    public void setCarbonVoidCnt(Double carbonVoidCnt) {
        this.carbonVoidCnt = carbonVoidCnt;
    }

    public Double getLddCnt() {
        return lddCnt;
    }

    public void setLddCnt(Double lddCnt) {
        this.lddCnt = lddCnt;
    }

    public Double getAvgRmsDeltaMv() {
        return avgRmsDeltaMv;
    }

    public void setAvgRmsDeltaMv(Double avgRmsDeltaMv) {
        this.avgRmsDeltaMv = avgRmsDeltaMv;
    }

    public Double getPatchTaCnt() {
        return patchTaCnt;
    }

    public void setPatchTaCnt(Double patchTaCnt) {
        this.patchTaCnt = patchTaCnt;
    }

    public Double getPatchVoidCnt() {
        return patchVoidCnt;
    }

    public void setPatchVoidCnt(Double patchVoidCnt) {
        this.patchVoidCnt = patchVoidCnt;
    }

    public Double getPatchContamCnt() {
        return patchContamCnt;
    }

    public void setPatchContamCnt(Double patchContamCnt) {
        this.patchContamCnt = patchContamCnt;
    }

    public Double getNcorrCnt() {
        return ncorrCnt;
    }

    public void setNcorrCnt(Double ncorrCnt) {
        this.ncorrCnt = ncorrCnt;
    }

    public Double getCorrCnt() {
        return corrCnt;
    }

    public void setCorrCnt(Double corrCnt) {
        this.corrCnt = corrCnt;
    }

}



package wdc.disk.ecs.apps.SFTPSend.util;

import org.junit.jupiter.api.Test;

public class TestSplit {

	@Test
	public void testSplit() {
		String string = "1/2/3";
		String[] split = string.split("/");
		System.out.println(split.length);
		
	}
	
	@Test
	public void testRegex() {
		String str = "1234_line";
		String extractNumberByRegex = CompareTwoFileSequence.extractNumberByRegex(str);
		System.out.println(extractNumberByRegex);
	}
	
	@Test
	public void testCreateMode() {
		CreateShareFolderMode mode = CreateShareFolderMode.initialize;
		System.out.print("Mode:"+ mode.toString());
	}
}

package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.FileFilter;
import java.io.FileNotFoundException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;

import ibm.disk.utility.LogStream;

public class FileSortUtil implements FileFilter {
	// Used in the method--accept() of this class, to count the number of files in directory
	private long fileCount = 0;
	
	// Limit the number of files JVM read one time to avoid OOM
	private int maxReadFiles = 500000;
	
	File [] filelist = null;
	LogStream log;
	
	public FileSortUtil() {
		super();
	}
	
	public FileSortUtil(int maxReadFiles, LogStream log) {
		super();
		if(maxReadFiles != 0) {
			this.maxReadFiles = maxReadFiles;			
		}
		this.log = log;
	}

	public FileSortUtil(LogStream log) {
		super();
		this.log = log;
	}

	public List<FileEntity> getFiles(List<FileEntity> list,File dir) {
//		displayLogAndConsole("Start listing file: " + new Date().toString());
		filelist = dir.listFiles(this);
		displayLogAndConsole("File size:" + filelist.length);
		for(int i=0; i<filelist.length; i++) {
			File file = filelist[i];
			long lastModified = file.lastModified();
//			displayLogAndConsole("File:" + i + " " + filelist[i].getName() + " .Last Modi:" + new Date(lastModified).toString());
			String fileName = file.getAbsolutePath();
			
			Date extractDate = CompareTwoFileSequence.extractDate(fileName);
			long time = extractDate.getTime();
			
			String extractedIndex = CompareTwoFileSequence.extractIndex(fileName);
			int valueOfExtractedIndex = Integer.valueOf(extractedIndex).intValue();
			
			FileEntity fileEntity = new FileEntity(file,fileName, lastModified, time, valueOfExtractedIndex);
			
			list.add(fileEntity);
		}
		
//		displayLogAndConsole("End listing file: " + new Date().toString());
		return list;
	}
	
	public List<FileEntity> sortFiles(List<FileEntity> fileList) {
		Comparator<FileEntity> comparator = new Comparator<FileEntity>() {
			@Override
			public int compare(FileEntity o1, FileEntity o2) {
				if(o1.getLastModify() > o2.getLastModify()) {
					return 1;
				}else if(o1.getLastModify() == o2.getLastModify()) {
					if(o1.getFileNameDate() > o2.getFileNameDate()) {
						return 1;
					}else if(o1.getFileNameDate() == o2.getFileNameDate()) {
						if(o1.getFileLastIndex() > o2.getFileLastIndex()) {
							return 1;
						}else if(o1.getFileLastIndex() == o2.getFileLastIndex()) {
							return 0;
						}else {
							return -1;
						}
					}else {
						return -1;
					}
				}else {
					return -1;
				}
			}
		};
		
//		displayLogAndConsole("Start sorting file: " + new Date().toString());
		Collections.sort(fileList, comparator);
//		displayLogAndConsole("End sorting file:" + new Date().toString());
		return fileList;
	}





	@Override
	public boolean accept(File pathname) {
		if(pathname.isFile()) {
			fileCount++;
			if (fileCount < maxReadFiles) {
				return true;
			} else {
				return false;
			}			
		}else {
			return false;
		}
	}
	
	public void displayLogAndConsole(String msg) {
		if(log != null) {
			log.write(msg);
		}
		System.out.println(msg);
	}


}

*| ColumnName       |  DBColumnType  |  DBColumnLength  |  SystemType       |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  0      |  Y      |  0      |  0      |  
|  TESTNUM          |  Integer     |  4      |  System.Int32     |  4      |  1      |  Y      |  8      |  1      |  
|  BATCHID          |  Integer     |  4      |  System.Int32     |  4      |  2      |         |  16     |  2      |  
|  PRODUCTCODE      |  SmallInt    |  2      |  System.Int16     |  2      |  3      |         |  24     |  3      |  
|  DISKCODE         |  Integer     |  4      |  System.Int32     |  4      |  4      |         |  32     |  4      |  
|  DISKSEQUENCE     |  Integer     |  4      |  System.Int64     |  2      |  5      |         |  152    |  5      |  
|  MAGRULE          |  SmallInt    |  2      |  System.Int16     |  2      |  6      |         |  48     |  6      |  
|  TESTCODE         |  SmallInt    |  2      |  System.Int16     |  2      |  7      |         |  52     |  7      |  
|  DETQCODE1        |  Integer     |  4      |  System.Int32     |  4      |  8      |         |  56     |  8      |  
|  DETQCODE2        |  Integer     |  4      |  System.Int32     |  4      |  9      |         |  64     |  9      |  
|  PARQCODE1        |  Integer     |  4      |  System.Int32     |  4      |  10     |         |  72     |  10     |  
|  PARQCODE2        |  Integer     |  4      |  System.Int32     |  4      |  11     |         |  80     |  11     |  
|  STARTMAG         |  Timestamp   |  4      |  System.DateTime  |  4      |  12     |         |  88     |  12     |  
|  STOPMAG          |  Timestamp   |  4      |  System.DateTime  |  4      |  13     |         |  96     |  13     |  
|  DTINSERT         |  Timestamp   |  4      |  System.DateTime  |  4      |  14     |         |  0      |  14     |  
|  CORRELID         |  Integer     |  4      |  System.Int32     |  4      |  15     |         |  0      |  15     |  
|  LOT              |  Char        |  10     |  System.String    |  10     |  16     |         |  104    |  16     |  
|  UNITID           |  Char        |  12     |  System.String    |  12     |  17     |         |  126    |  17     |  
|  MAGRULEVER       |  SmallInt    |  2      |  System.Int16     |  2      |  18     |         |  156    |  18     |  
|  MAGCHECKSUM      |  Char        |  32     |  System.String    |  32     |  19     |         |  160    |  19     |  
|  EXPID            |  Char        |  10     |  System.String    |  10     |  20     |         |  226    |  20     |  

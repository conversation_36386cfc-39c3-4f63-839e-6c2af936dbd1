package wdc.disk.ecs.apps.SFTPSend.util;


/**
 * Enum for share folder creation mode
 */
public enum CreateShareFolderMode {
    /**
     * Initialize mode - creates new folders
     */
    initialize(0),
    
    /**
     * Uploading mode - uses existing folders
     */
    uploading(1);

    private final int value;

    CreateShareFolderMode(int value) {
        this.value = value;
    }

    public int getValue() {
        return value;
    }

    public static CreateShareFolderMode fromValue(int value) {
        for (CreateShareFolderMode mode : values()) {
            if (mode.getValue() == value) {
                return mode;
            }
        }
        return uploading; // default value
    }
}
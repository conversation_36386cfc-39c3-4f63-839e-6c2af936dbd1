## ENV: dev_DECO_linux:
app_root: '/u/deco/MQUpload'
mq_storage_root: '/u/deco/data/MQfiles/sent'

# Producer Configuration
producer:
  # File monitoring configurations
  pds_dir: "/u/deco/data/MQfiles/sent"
  binary_format: true

  # File processor configurations
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  pack_same_msg_id: false
  to_dir: "/u/deco/data/MQfiles/sent/sent"

  # DB Message ID Profile configurations
  db_msg_id_profile: "/u/deco/MQUpload/profiles/pdsMsgId.pro"
  monitor_interval: 10

  housekeeping:
      enabled: true
      interval_hours: 4
      retention_days: 3
      directories:
        - sent
        - problem

# RabbitMQ Configuration
rabbitmq:
  host: "csf-md-rabitmq1.ad.shared"
  port: 5672
  username: "mqupload"
  password: "upload66"
  exchange: "ecs.direct"
  queue_config_path: "/u/deco/MQUpload/config/test-queue-config.csv"

table_schema:
  message_mappings_path: "/u/deco/MQUpload/config/message_mappings.json"
  profile_base_path: "/u/deco/MQUpload/profiles"

## ENV: dev_TV_linux
mq_storage_root: '/u/tsd/misc/mq_send/sent'
app_root: '/u/MQUpload'

# Environment-specific configurations
environments:
  DEV:
    mq_storage_root: "C:\\opt\\ecs\\storage\\mq"
    app_root: "C:\\Development\\TFS_SourceCode\\ECS\\SZ_GO\\Common\\Dev\\SFTPSend_Hao"
    rabbitmq_env: "DEV"
  PROD:
    mq_storage_root: "C:\\opt\\ecs\\storage\\mq\\prod"
    app_root: "C:\\opt\\ecs\\apps\\mqupload"
    rabbitmq_env: "PROD"
  TV_UAT:
    mq_storage_root: "/u/tsd/misc/mq_send/sent"
    app_root: "/u/MQUpload"
    rabbitmq_env: "DEV"


# Producer Configuration (same for all environments)
producer:
  pds_dir:
    base: mq_storage_root
    relative: ""
  binary_format: true
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  to_dir:
    base: mq_storage_root
    relative: "sent"
  db_msg_id_profile:
    base: app_root
    relative: "profiles/pdsMsgId.pro"
  monitor_interval: 10
  housekeeping:
      enabled: true
      interval_hours: 24
      retention_days: 3
      directories:
        - sent
        - problem


# Environment-specific RabbitMQ configurations
rabbitmq:
  DEV:
    host: "csf-md-rabitmq1.ad.shared"
    port: 5672
    username: "mqupload"
    password: "upload66"
    exchange: "ecs.direct"
    queue_config_path:
      base: app_root
      relative: "config/test-queue-config.csv"
  PROD:
    host: "csf-mp-talend03.ad.shared"
    port: 5672
    username: "mqupload"
    password: "upload66"
    exchange: "ecs.direct"
    queue_config_path:
      base: app_root
      relative: "config/queue-config.csv"

table_schema:
  message_mappings_path:
    base: app_root
    relative: "config/message_mappings.json"
  profile_base_path:
    base: app_root
    relative: "profiles"

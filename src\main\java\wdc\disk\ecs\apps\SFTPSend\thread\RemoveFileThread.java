package wdc.disk.ecs.apps.SFTPSend.thread;

import java.util.*;
import ibm.disk.utility.*;


import java.io.*;
import java.nio.file.Files;
/**
 *********************************************************************
 *                                                                   *
 *  RemoveFileThread												 *                                                 *
 *                                                                   *
 *********************************************************************
 * Delete the already sent PDS files that exceed the retention time,
 * Working dir is pdsDir/sent
 * @Date 2021-08-12 09:22:32
 * <AUTHOR>
 */
public class RemoveFileThread extends Thread {
	private File dir;
	private long msRetention;
	private LogStream log;
	private boolean exiting = false;
	
	/**
	 * RemoveFileThread constructor comment.
	 * @param dir java.lang.String Fully qualified path of the directory where files
	 * will be deleted.
	 * @param hoursOfRetention int Number of hours which the file will be kept
	 * @param logFile ibm.disk.utility.LogStream log stream to write messages.
	 */
	public RemoveFileThread(String dirName, int hoursOfRetention, LogStream logFile) throws IOException {
		super();
		dir = new File(dirName);
		if (!dir.isDirectory()) {
			throw new IOException("Unable to start RemoveFileThread, " + dirName + " is not a directory");
		}

		log = logFile;
		msRetention = (long) hoursOfRetention * 60 * 60 * 1000;
		
		log.write(LogStream.HILITE_INFO_MESSAGE, "RemoveFileThread started for directory=" + dirName + " and retention of files is " + hoursOfRetention + " hours");
		Thread.currentThread().setName("RemoveFileThread");
		this.start();
		
	}
	
	/**
	 * When an object implementing interface <code>Runnable</code> is used 
	 * to create a thread, starting the thread causes the object's 
	 * <code>run</code> method to be called in that separately executing 
	 * thread. 
	 * <p>
	 * The general contract of the method <code>run</code> is that it may 
	 * take any action whatsoever.
	 *
	 * @see     java.lang.Thread#run()
	 */
	public void run() {
		while (!exiting) {
			File [] list = dir.listFiles();
			// List files before to ensure the log is correct
			log.write(LogStream.WARNING_MESSAGE, "Looking for files older than " + new Date(new Date().getTime() - msRetention));
			int count = 0;
			for (int i = 0; i < list.length; i++) {
				log.write("Processing " + list[i].getAbsolutePath() + ".Dir:" + list[i].isDirectory()  + " . File:" + list[i].isFile());
				if (list[i].isFile()) {
					boolean success = deleteOverTimeFile(list[i]);
					if(success) count++;
				}
				if(list[i].isDirectory()) {
					log.write("Sub dir processing.");
					File subDir = list[i];
					if (subDir.lastModified() + msRetention < new Date().getTime()) {
						File[] subDirFiles = subDir.listFiles();
						for (int j = 0; j < subDirFiles.length; j++) {
							boolean success = deleteOverTimeFile(subDirFiles[j]);
							if(success) count++;
						}
						int length = subDir.listFiles().length;
						log.write("Folder trying to delete." + subDir.getName() + " . Length:" + length);
						if(subDir.listFiles().length == 0) {
							boolean deleteFolder = subDir.delete();
							log.write("Delete result:" + deleteFolder);
							if(deleteFolder) {
								log.write("Folder is deleted." + subDir.getName());
							}
						}
					}	
				}
			}
			log("Count of successfully deleted files: " + count);
			try {
				 // Try again in an hour
				Thread.sleep(60*60*1000);  
			} catch (Exception e) {
				log.write("Interrupt remore file thread.");
				break;}
		}
		logError("RemoveFileThread of SFTPSend exited successfully");
	}
	
	public boolean deleteOverTimeFile(File file) {
		if (file.isFile()) {
			if (file.lastModified() + msRetention < new Date().getTime()) {
				if (file.delete()) {
					logDebug("Success to delete file: " + file);
					return true;
				} else {
					logError("Failed to delete file: " + file);
					return false;
				}
			}
		}
		return false;
	}

	/**
	 * log debug message
	 * Author: Enhua
	 * Date: 2021-07-08 07:57:03
	 * @param debugMsg
	 */
	public void logDebug(String debugMsg) {
		log.write(LogStream.DEBUG_MESSAGE, debugMsg);
	}
	
	/**
	 * log message
	 * Author: Enhua
	 * Date: 2021-07-19 14:48:40
	 * @param msg
	 */
	public void log(String msg) {
		log.write(LogStream.INFORMATION_MESSAGE, msg);
	}
	
	/**
	 * Log error message
	 * Author: Enhua
	 * Date: 2021-07-02 08:53:30
	 * @param errMsg
	 */
	public void logError(String errMsg) {
		log.write(LogStream.ERROR_MESSAGE, errMsg);
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (10/27/2003 9:52:30 AM)
	 * @return boolean
	 */
	public boolean isExiting() {
		return exiting;
	}
	
	/**
	 * Insert the method's description here.
	 * Creation date: (10/27/2003 9:52:30 AM)
	 * @param newExiting boolean
	 */
	public void setExiting(boolean newExiting) {
		exiting = newExiting;
		this.interrupt();
	}
	
	/**
	 * Creation date: 2021-08-12 09:36:54
	 * @return java.io.File
	 */
	public java.io.File getDir() {
		return dir;
	}

	/**
	 * Creation date: 2021-08-12 09:36:54
	 * @param dir java.io.File
	 */
	public void setDir(java.io.File dir) {
		this.dir = dir;
	}

	/**
	 * Creation date: 2021-08-12 09:36:54
	 * @return long
	 */
	public long getMsRetention() {
		return msRetention;
	}

	/**
	 * Creation date: 2021-08-12 09:36:54
	 * @param msRetention long
	 */
	public void setMsRetention(long msRetention) {
		this.msRetention = msRetention;
	}

	/**
	 * Creation date: 2021-08-12 09:36:54
	 * @return LogStream
	 */
	public LogStream getLog() {
		return log;
	}

	/**
	 * Creation date: 2021-08-12 09:36:54
	 * @param log LogStream
	 */
	public void setLog(LogStream log) {
		this.log = log;
	}
}

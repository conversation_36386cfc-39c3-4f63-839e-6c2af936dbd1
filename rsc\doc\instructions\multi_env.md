下面我将用 Java 来实现完全相同的多 Profile 文件配置加载逻辑。

我们将使用业界标准的 SnakeYAML 库来解析 YAML 文件。如果你使用 Maven 或 Gradle，可以很方便地将其添加到项目中。


2. 准备配置文件
和之前的例子一样，将配置文件放在 src/main/resources 目录下。当项目被构建时，这个目录下的文件会被自动打包到 classpath 中，方便程序读取。

文件结构:

/my-java-project
├── src/main/java/
│   └── com/example/
│       ├── ConfigLoader.java   # 配置加载的核心逻辑
│       └── Main.java           # 主程序入口
├── src/main/resources/
│   ├── config.yml              # 基础配置
│   ├── config-dev.yml          # 开发环境配置
│   └── config-prod.yml         # 生产环境配置
└── pom.xml
配置文件内容 (保持不变):

config.yml: 包含 app, database, logging 的通用默认值。

config-dev.yml: 覆盖 database.host 并添加 debug: true。

config-prod.yml: 覆盖 database.host, database.pool_size, 和 logging.level。

3. 实现配置加载和合并的逻辑 (Java)
我们将创建一个 ConfigLoader 工具类来封装所有逻辑。

ConfigLoader.java

Java

package com.example;

import org.yaml.snakeyaml.Yaml;

import java.io.InputStream;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;

public class ConfigLoader {

    /**
     * 加载并合并配置。
     * @return 合并后的最终配置 Map
     */
    public static Map<String, Object> loadConfig() {
        // 1. 确定当前环境
        // 优先从环境变量读取，如果不存在，则从系统属性读取，默认为 "dev"
        String env = System.getenv("APP_ENV");
        if (env == null || env.trim().isEmpty()) {
            env = System.getProperty("app.env", "dev");
        }
        System.out.println("--- Loading configuration for environment: '" + env + "' ---");

        Yaml yaml = new Yaml();

        // 2. 加载基础配置文件
        InputStream baseInputStream = getResourceAsStream("config.yml");
        Map<String, Object> baseConfig = yaml.load(baseInputStream);

        // 3. 尝试加载环境特定配置文件
        String envConfigFileName = "config-" + env + ".yml";
        InputStream envInputStream = getResourceAsStream(envConfigFileName);

        if (envInputStream != null) {
            System.out.println("Found environment specific config: " + envConfigFileName);
            Map<String, Object> envConfig = yaml.load(envInputStream);
            // 4. 执行深度合并
            return deepMerge(baseConfig, envConfig);
        } else {
            System.out.println("Warning: Environment specific config not found for '" + env + "'. Using base config only.");
            return baseConfig;
        }
    }

    /**
     * 从 classpath 加载资源文件为 InputStream。
     */
    private static InputStream getResourceAsStream(String fileName) {
        return ConfigLoader.class.getClassLoader().getResourceAsStream(fileName);
    }

    /**
     * 深度合并两个 Map。
     * 'override' Map 中的值会覆盖 'base' Map 中的值。
     * @param base 基础 Map
     * @param override 覆盖 Map
     * @return 合并后的新 Map
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> deepMerge(Map<String, Object> base, Map<String, Object> override) {
        // 创建一个 base 的副本，以避免修改原始对象
        Map<String, Object> merged = new LinkedHashMap<>(base);

        for (String key : override.keySet()) {
            Object overrideValue = override.get(key);
            Object baseValue = merged.get(key);

            // 如果两个值都是 Map，则进行递归合并
            if (baseValue instanceof Map && overrideValue instanceof Map) {
                merged.put(key, deepMerge((Map<String, Object>) baseValue, (Map<String, Object>) overrideValue));
            } else {
                // 否则，直接用 override 的值进行覆盖
                merged.put(key, overrideValue);
            }
        }
        return merged;
    }
}
4. 编写主程序进行演示
现在，我们创建一个 Main 类来调用 ConfigLoader 并打印出不同环境下的最终配置。

Main.java

Java

package com.example;

import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;
import java.util.Map;

public class Main {
    public static void main(String[] args) {
        // 使用 SnakeYAML 来格式化输出，方便查看
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        options.setPrettyFlow(true);
        Yaml yamlPrinter = new Yaml(options);

        // --- 场景一：模拟开发环境 (默认) ---
        // 清除可能存在的系统属性，确保走默认 'dev' 逻辑
        System.clearProperty("app.env"); 
        
        Map<String, Object> devConfig = ConfigLoader.loadConfig();
        System.out.println("\n[Final DEV Configuration]");
        System.out.println(yamlPrinter.dump(devConfig));

        System.out.println("\n" + "=".repeat(40) + "\n");

        // --- 场景二：模拟生产环境 ---
        // 通过设置系统属性来切换环境
        System.setProperty("app.env", "prod");
        
        Map<String, Object> prodConfig = ConfigLoader.loadConfig();
        System.out.println("\n[Final PROD Configuration]");
        System.out.println(yamlPrinter.dump(prodConfig));
        
        System.out.println("\n" + "=".repeat(40) + "\n");
        
        // --- 场景三：模拟一个不存在配置文件的环境（回退到基础配置）---
        System.setProperty("app.env", "uat");

        Map<String, Object> uatConfig = ConfigLoader.loadConfig();
        System.out.println("\n[Final UAT Configuration (Fallback)]");
        System.out.println(yamlPrinter.dump(uatConfig));
    }
}
package wdc.disk.ecs.apps.MQUpload.parser;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.regex.Pattern;

public class AsciiMessageParser implements MessageParser {
    private static final int MAX_ARRAY_SIZE = 1024;
    private static final char DELIMITER_MARKER = '*';
    private static final DateTimeFormatter DATE_FORMAT = 
        DateTimeFormatter.ofPattern("yyyy-MM-dd-HH.mm.ss.SSSSSS");
    
    @Override
    public ParseResult parse(String message) {
        if (message == null || message.isEmpty()) {
            return new ParseResult(null, null, null);
        }

        // Find delimiter marker
        int delimiterMarkerPos = message.indexOf(DELIMITER_MARKER);
        if (delimiterMarkerPos == -1) {
            return new ParseResult(null, null, null);
        }

        // Get delimiter character
        char delimiter = message.charAt(delimiterMarkerPos + 1);
        if (delimiter == ' ') {
            return new ParseResult(null, null, null);
        }

        // Parse header information
        String[] headerParts = message.substring(0, delimiterMarkerPos).trim().split("\\s+");
        if (headerParts.length < 4) {
            return new ParseResult(null, null, null);
        }

        // Parse datetime from the first part
        LocalDateTime dateTime = null;
        try {
            dateTime = LocalDateTime.parse(headerParts[0], DATE_FORMAT);
        } catch (DateTimeParseException e) {
            return new ParseResult(null, null, null);
        }

        // Extract message type (second token in header)
        String msgType = headerParts[1];

        // Parse data fields
        String dataSection = message.substring(delimiterMarkerPos + 2);
        // Escape special regex characters in delimiter
        String delimiterRegex = Pattern.quote(String.valueOf(delimiter));
        String[] fields = dataSection.split(delimiterRegex);
        
        // Clean up fields and remove empty entries
        String[] parsedFields = new String[Math.min(fields.length, MAX_ARRAY_SIZE)];
        int fieldCount = 0;
        for (String field : fields) {
            if (fieldCount >= MAX_ARRAY_SIZE) break;
            String trimmedField = field.trim();
            parsedFields[fieldCount++] = trimmedField;
        }

//        // Create final array with exact size + 1 (for dateTime)
//        String[] result = new String[fieldCount + 1];
//        // Add dateTime as first element
//        result[0] = dateTime.format(DATE_FORMAT);
//        // Copy the parsed fields starting at index 1
//        System.arraycopy(parsedFields, 0, result, 1, fieldCount);
        
        return new ParseResult(parsedFields, dateTime, msgType);
    }
}





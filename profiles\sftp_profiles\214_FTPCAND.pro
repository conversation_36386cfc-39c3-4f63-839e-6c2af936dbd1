*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |         |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  MEAS_TIME             |  Timestamp   |  4      |  System.DateTime       |  4      |  8      |         |  0      |  8      |  
|  OPERATOR              |  Char        |  7      |  System.String         |  7      |  9      |         |  0      |  9      |  
|  SCR_SAMP1A            |  SmallInt    |  2      |  System.Int16          |  2      |  10     |         |  0      |  10     |  
|  SCR_SAMP2A            |  SmallInt    |  2      |  System.Int16          |  2      |  11     |         |  0      |  11     |  
|  SCR_SAMP3A            |  SmallInt    |  2      |  System.Int16          |  2      |  12     |         |  0      |  12     |  
|  SCR_SAMP1B            |  SmallInt    |  2      |  System.Int16          |  2      |  13     |         |  0      |  13     |  
|  SCR_SAMP2B            |  SmallInt    |  2      |  System.Int16          |  2      |  14     |         |  0      |  14     |  
|  SCR_SAMP3B            |  SmallInt    |  2      |  System.Int16          |  2      |  15     |         |  0      |  15     |  
|  SCR_AVGA              |  SmallInt    |  2      |  System.Int16          |  2      |  16     |         |  0      |  16     |  
|  SCR_AVGB              |  SmallInt    |  2      |  System.Int16          |  2      |  17     |         |  0      |  17     |  
|  SCR_MIN               |  SmallInt    |  2      |  System.Int16          |  2      |  18     |         |  0      |  18     |  
|  SCR_MAX               |  SmallInt    |  2      |  System.Int16          |  2      |  19     |         |  0      |  19     |  
|  PAR_SAMP1A            |  SmallInt    |  2      |  System.Int16          |  2      |  20     |         |  0      |  20     |  
|  PAR_SAMP2A            |  SmallInt    |  2      |  System.Int16          |  2      |  21     |         |  0      |  21     |  
|  PAR_SAMP3A            |  SmallInt    |  2      |  System.Int16          |  2      |  22     |         |  0      |  22     |  
|  PAR_SAMP1B            |  SmallInt    |  2      |  System.Int16          |  2      |  23     |         |  0      |  23     |  
|  PAR_SAMP2B            |  SmallInt    |  2      |  System.Int16          |  2      |  24     |         |  0      |  24     |  
|  PAR_SAMP3B            |  SmallInt    |  2      |  System.Int16          |  2      |  25     |         |  0      |  25     |  
|  PAR_AVGA              |  SmallInt    |  2      |  System.Int16          |  2      |  26     |         |  0      |  26     |  
|  PAR_AVGB              |  SmallInt    |  2      |  System.Int16          |  2      |  27     |         |  0      |  27     |  
|  PAR_MIN               |  SmallInt    |  2      |  System.Int16          |  2      |  28     |         |  0      |  28     |  
|  PAR_MAX               |  SmallInt    |  2      |  System.Int16          |  2      |  29     |         |  0      |  29     |  

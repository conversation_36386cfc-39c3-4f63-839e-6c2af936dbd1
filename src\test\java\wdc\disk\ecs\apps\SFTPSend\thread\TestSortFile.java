package wdc.disk.ecs.apps.SFTPSend.thread;

import java.io.File;
import java.io.FileFilter;
import java.util.Date;

import org.junit.jupiter.api.Test;

import wdc.disk.ecs.apps.SFTPSend.SFTPSendApp;
import wdc.disk.ecs.apps.SFTPSend.util.CompareTwoFileSequence;

public class TestSortFile {
	@Test
	public void testSort() {
		SFTPSendApp app = new SFTPSendApp();
		File[] sortedFiles = app.getSortedFiles(new File("C:\\opt\\ecs\\storage\\mq\\sent"));
		for(int i=0; i<= sortedFiles.length; i++){
			System.out.println("File:" + sortedFiles[i] + " time:" + sortedFiles[i].lastModified());
		}
	}
	
	@Test
	public void testFileName() {
		File file = new File("C:\\Users\\<USER>\\Desktop\\initSendMail");
		System.out.println(file.getName());
	}
	
	@Test
	public void testSortFile() {
//		String folderString =  "\\\\csfsvmip01a-419-d03.wdc.com\\CSF_Talend_Share\\InsertedArchive_1\\POC\\TEST\\2";
		String folderString =  "\\\\csfsvmip01a-419-d03.wdc.com\\CSF_Talend_Share\\InsertedArchive\\POC\\309\\20220803";
		System.out.println("Start:" + System.currentTimeMillis());
		
		File[] sortedFiles = getSortedFiles(new File(folderString));
		System.out.println("Sorted end:" + System.currentTimeMillis());
		for(int i=0; i<sortedFiles.length; i++) {
			System.out.println("File " + i + " " + sortedFiles[i]);
		}
		System.out.println(new Date());
	}
	
	public File[] getSortedFiles(File dir) {
		// get the files in the directory
		File [] filelist = dir.listFiles(new FileFilter() {
			@Override
			public boolean accept(File pathname) {
				if(pathname.isFile()) {
						return true;
				}
				return false;
			}
		});
	
		File temp1, temp2;
		// sort the files based on last modified
		for (int i = 0; i < filelist.length -1; i++) {
			for (int j = 0; j < filelist.length -1-i; j++) {
				if (filelist[j].lastModified() > filelist[j+1].lastModified()) {
					temp1 = filelist[j];
					filelist[j] = filelist[j+1];
					filelist[j+1] = temp1;
				}
				if (filelist[j].lastModified() == filelist[j+1].lastModified()) {
					boolean before = CompareTwoFileSequence.compareTwoFileIfSameLastModify(filelist[j].getName(), filelist[j+1].getName());
					//if file1 after file2, then switch position.
					if(!before) {
						temp1 = filelist[j];
						filelist[j] = filelist[j+1];
						filelist[j+1] = temp1;
					}
				}
			}
		}
		return filelist;
	}
	
	
}

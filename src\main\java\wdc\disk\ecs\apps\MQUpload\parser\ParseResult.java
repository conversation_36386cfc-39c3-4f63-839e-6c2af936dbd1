package wdc.disk.ecs.apps.MQUpload.parser;

import java.time.LocalDateTime;
import java.util.Arrays;

public class ParseResult {
    private String[] parsedFields;
    private LocalDateTime dateTime;
    private String tabCode;  
    private String tableName;
    
    public ParseResult(String[] parsedFields, LocalDateTime dateTime, String tabCode) {
        this.parsedFields = parsedFields;
        this.dateTime = dateTime;
        this.tabCode = tabCode;
    }

 
    public ParseResult(String[] parsedFields, LocalDateTime dateTime) {
        this(parsedFields, dateTime, null);
    }

    public String[] getParsedFields() {
        return parsedFields;
    }

    public LocalDateTime getDateTime() {
        return dateTime;
    }

    public String getTabCode() {
        return tabCode;
    }

    public String getTableName() {
        return tableName;
    }

    public void setTableName(String tableName) {
        this.tableName = tableName;
    }

    @Override
    public String toString() {
        return "ParseResult{" +
                "parsedFields=" + Arrays.toString(parsedFields) +
                ", dateTime=" + dateTime +
                ", tabCode='" + tabCode + '\'' +
                ", tableName='" + tableName + '\'' +
                '}';
    }
}






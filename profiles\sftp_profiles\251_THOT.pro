*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  MEAS_TIME             |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |  Y      |  0      |  1      |  
|  TYPE                  |  Char        |  1      |  System.String         |  1      |  2      |         |  0      |  2      |  
|  SUBSTRATE             |  VarChar     |  30     |  System.String         |  30     |  3      |  Y      |  0      |  3      |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  4      |  Y      |  0      |  4      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  5      |         |  0      |  5      |  
|  VENDOR                |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  WAVELENGTH            |  Char        |  10     |  System.String         |  10     |  7      |  Y      |  0      |  7      |  
|  TOOLNAME              |  Char        |  10     |  System.String         |  10     |  8      |         |  0      |  8      |  
|  TESTFILENAME          |  VarChar     |  40     |  System.String         |  40     |  9      |         |  0      |  9      |  
|  A_FULL_SURFACE        |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  A_RADIUS1             |  Double      |  8      |  System.Double         |  8      |  11     |         |  0      |  11     |  
|  A_RADIUS2             |  Double      |  8      |  System.Double         |  8      |  12     |         |  0      |  12     |  
|  A_RADIUS3             |  Double      |  8      |  System.Double         |  8      |  13     |         |  0      |  13     |  
|  A_FULL_SURFACE_TRACKS  |  Double      |  8      |  System.Double         |  8      |  14     |         |  0      |  14     |  
|  A_RADIUS1_TRACKS      |  Double      |  8      |  System.Double         |  8      |  15     |         |  0      |  15     |  
|  A_RADIUS2_TRACKS      |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  A_RADIUS3_TRACKS      |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  A_FULL_SURFACE_RMS_AVG  |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  A_RADIUS1_RMS_AVG     |  Double      |  8      |  System.Double         |  8      |  19     |         |  0      |  19     |  
|  A_RADIUS2_RMS_AVG     |  Double      |  8      |  System.Double         |  8      |  20     |         |  0      |  20     |  
|  A_RADIUS3_RMS_AVG     |  Double      |  8      |  System.Double         |  8      |  21     |         |  0      |  21     |  
|  A_FULL_SURFACE_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  22     |         |  0      |  22     |  
|  A_RADIUS1_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  23     |         |  0      |  23     |  
|  A_RADIUS2_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  24     |         |  0      |  24     |  
|  A_RADIUS3_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  25     |         |  0      |  25     |  
|  B_FULL_SURFACE        |  Double      |  8      |  System.Double         |  8      |  26     |         |  0      |  26     |  
|  B_RADIUS1             |  Double      |  8      |  System.Double         |  8      |  27     |         |  0      |  27     |  
|  B_RADIUS2             |  Double      |  8      |  System.Double         |  8      |  28     |         |  0      |  28     |  
|  B_RADIUS3             |  Double      |  8      |  System.Double         |  8      |  29     |         |  0      |  29     |  
|  B_FULL_SURFACE_TRACKS  |  Double      |  8      |  System.Double         |  8      |  30     |         |  0      |  30     |  
|  B_RADIUS1_TRACKS      |  Double      |  8      |  System.Double         |  8      |  31     |         |  0      |  31     |  
|  B_RADIUS2_TRACKS      |  Double      |  8      |  System.Double         |  8      |  32     |         |  0      |  32     |  
|  B_RADIUS3_TRACKS      |  Double      |  8      |  System.Double         |  8      |  33     |         |  0      |  33     |  
|  B_FULL_SURFACE_RMS_AVG  |  Double      |  8      |  System.Double         |  8      |  34     |         |  0      |  34     |  
|  B_RADIUS1_RMS_AVG     |  Double      |  8      |  System.Double         |  8      |  35     |         |  0      |  35     |  
|  B_RADIUS2_RMS_AVG     |  Double      |  8      |  System.Double         |  8      |  36     |         |  0      |  36     |  
|  B_RADIUS3_RMS_AVG     |  Double      |  8      |  System.Double         |  8      |  37     |         |  0      |  37     |  
|  B_FULL_SURFACE_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  38     |         |  0      |  38     |  
|  B_RADIUS1_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  39     |         |  0      |  39     |  
|  B_RADIUS2_RMS_STDDEV  |  Double      |  8      |  System.Double         |  8      |  40     |         |  0      |  40     |  

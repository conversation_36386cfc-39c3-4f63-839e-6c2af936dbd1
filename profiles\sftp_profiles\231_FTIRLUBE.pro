*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  4      |         |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  6      |         |  0      |  6      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  SPUTRESC              |  Char        |  10     |  System.String         |  10     |  8      |         |  0      |  8      |  
|  BKG_RESC              |  Char        |  10     |  System.String         |  10     |  9      |         |  0      |  9      |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  10     |         |  0      |  10     |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  11     |         |  0      |  11     |  
|  SIDEIND               |  Char        |  1      |  System.String         |  1      |  12     |         |  0      |  12     |  
|  READING1              |  Double      |  8      |  System.Double         |  8      |  13     |         |  0      |  13     |  
|  READING2              |  Double      |  8      |  System.Double         |  8      |  14     |         |  0      |  14     |  
|  READING3              |  Double      |  8      |  System.Double         |  8      |  15     |         |  0      |  15     |  
|  READING4              |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  AVERAGE               |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  RANGE                 |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  DIP_CNT               |  Integer     |  4      |  System.Int32          |  4      |  19     |         |  0      |  19     |  
|  DIP_RATE              |  Double      |  8      |  System.Double         |  8      |  20     |         |  0      |  20     |  
|  MEASURED              |  Char        |  1      |  System.String         |  1      |  21     |         |  0      |  21     |  
|  MANDRILPOS            |  Char        |  1      |  System.String         |  1      |  22     |         |  0      |  22     |  

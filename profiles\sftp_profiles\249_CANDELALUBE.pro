*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |         |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |         |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  MEAS_TIME             |  Timestamp   |  4      |  System.DateTime       |  4      |  8      |         |  0      |  8      |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  9      |         |  0      |  9      |  
|  FACTOR                |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  MANDRIL               |  SmallInt    |  2      |  System.Int16          |  2      |  11     |         |  0      |  11     |  
|  SIDE                  |  Char        |  1      |  System.String         |  1      |  12     |         |  0      |  12     |  
|  EAR1_SIZE             |  Integer     |  4      |  System.Int32          |  4      |  13     |         |  0      |  13     |  
|  EAR2_SIZE             |  Integer     |  4      |  System.Int32          |  4      |  14     |         |  0      |  14     |  
|  EAR1_THICK            |  Double      |  8      |  System.Double         |  8      |  15     |         |  0      |  15     |  
|  EAR2_THICK            |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  EAR_REF               |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  ID_DROPLET_SIZE       |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  OD_DROPLET_SIZE       |  Double      |  8      |  System.Double         |  8      |  19     |         |  0      |  19     |  
|  ID_DROPLET_THICK      |  Double      |  8      |  System.Double         |  8      |  20     |         |  0      |  20     |  
|  OD_DROPLET_THICK      |  Double      |  8      |  System.Double         |  8      |  21     |         |  0      |  21     |  
|  OD_REF                |  Double      |  8      |  System.Double         |  8      |  22     |         |  0      |  22     |  
|  LINE1_THICK           |  Double      |  8      |  System.Double         |  8      |  23     |         |  0      |  23     |  
|  LINE2_THICK           |  Double      |  8      |  System.Double         |  8      |  24     |         |  0      |  24     |  
|  LINE_REF              |  Double      |  8      |  System.Double         |  8      |  25     |         |  0      |  25     |  

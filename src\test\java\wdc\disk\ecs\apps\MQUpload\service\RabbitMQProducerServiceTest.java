//package wdc.disk.ecs.apps.MQUpload.service;
//
//import static org.junit.jupiter.api.Assertions.assertThrows;
//import static org.mockito.ArgumentMatchers.any;
//import static org.mockito.ArgumentMatchers.anyString;
//import static org.mockito.ArgumentMatchers.contains;
//import static org.mockito.ArgumentMatchers.eq;
//import static org.mockito.Mockito.doThrow;
//import org.mockito.MockedStatic;
//import static org.mockito.Mockito.mock;
//import static org.mockito.Mockito.mockStatic;
//import static org.mockito.Mockito.times;
//import static org.mockito.Mockito.verify;
//import static org.mockito.Mockito.when;
//
//import java.io.IOException;
//import java.util.ArrayList;
//import java.util.List;
//
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.mockito.Mock;
//import org.mockito.MockedStatic;
//import org.mockito.MockitoAnnotations;
//
//import com.rabbitmq.client.Channel;
//import com.rabbitmq.client.Connection;
//import com.rabbitmq.client.ConnectionFactory;
//
//import ibm.disk.utility.LogStream;
//import wdc.disk.ecs.apps.MQUpload.model.ConfigurationProperties;
//import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;
//import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;
//import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
//
//public class RabbitMQProducerServiceTest {
//
//    @Mock
//    private LogStream logStream;
//
//    @Mock
//    private ConfigurationProperties config;
//
//    @Mock
//    private ConnectionFactory connectionFactory;
//
//    @Mock
//    private Connection connection;
//
//    @Mock
//    private Channel channel;
//
//    private RabbitMQProducerService service;
//    private QueueMetadataEntry testQueueEntry;
//    private MockedStatic<RabbitMQMetadataLoader> queueConfigMock;
//
//    @BeforeEach
//    void setUp() throws Exception {
//        MockitoAnnotations.openMocks(this);
//    
//        // Basic configuration
//        when(config.getRabbitMQHost()).thenReturn("localhost");
//        when(config.getRabbitMQPort()).thenReturn(5672);
//        when(config.getRabbitMQUsername()).thenReturn("guest");
//        when(config.getRabbitMQPassword()).thenReturn("guest");
//        when(config.getQueueConfigPath()).thenReturn("test/queue-config.csv");
//    
//        // Setup test queue entry
//        testQueueEntry = new QueueMetadataEntry();
//        testQueueEntry.setName("test_queue");
//        testQueueEntry.setExchange("test_exchange");
//        testQueueEntry.setRoutingKey("test_key");
//        testQueueEntry.setDurable(true);
//        testQueueEntry.setType("classic");
//    
//        // Mock queue config loading
//        RabbitMQMetadataLoader queueConfig = mock(RabbitMQMetadataLoader.class);
//        when(queueConfig.getEntries()).thenReturn(List.of(testQueueEntry));
//        queueConfigMock = mockStatic(RabbitMQMetadataLoader.class);
//        queueConfigMock.when(() -> RabbitMQMetadataLoader.loadFromCsv(anyString())).thenReturn(queueConfig);
//    
//        // Connection setup
//        when(connectionFactory.newConnection()).thenReturn(connection);
//        when(connection.createChannel()).thenReturn(channel);
//        when(channel.isOpen()).thenReturn(true);
//        when(connection.isOpen()).thenReturn(true);
//    
//        service = new RabbitMQProducerService(config, logStream, connectionFactory);
//    }
//
//    @AfterEach
//    void tearDown() {
//        if (queueConfigMock != null) {
//            queueConfigMock.close();
//        }
//    }
//
//    @Test
//    void testUploadMessageSuccess() throws Exception {
//        List<RabbitMessage> messages = new ArrayList<>();
//        RabbitMessage message = new RabbitMessage("test_key", "test content".getBytes());
//        messages.add(message);
//
//        service.uploadMessage(messages);
//
//        verify(channel).basicPublish(
//            eq(testQueueEntry.getExchange()),
//            eq(testQueueEntry.getRoutingKey()),
//            any(),
//            eq(message.getData())
//        );
//        verify(logStream).write(
//            eq(LogStream.INFORMATION_MESSAGE), 
//            eq("Successfully uploaded message with routing key: test_key")
//        );
//    }
//
//    @Test
//    void testUploadMessageInvalidType() throws Exception {
//        List<RabbitMessage> messages = new ArrayList<>();
//        RabbitMessage message = new RabbitMessage("invalid_key", "test content".getBytes());
//        messages.add(message);
//    
//        assertThrows(IOException.class, () -> service.uploadMessage(messages));
//        verify(logStream, times(3)).write(
//            eq(LogStream.WARNING_MESSAGE),
//            contains("Upload attempt")
//        );
//        verify(logStream).write(
//            eq(LogStream.ERROR_MESSAGE), 
//            eq("Failed to upload messages after 3 attempts")
//        );
//    }
//
//    @Test
//    void testQueueInitialization() throws Exception {
//        verify(channel).exchangeDeclare(
//            eq(testQueueEntry.getExchange()),
//            eq(testQueueEntry.getExchangeType()),
//            eq(testQueueEntry.isDurable())
//        );
//        
//        verify(channel).queueDeclare(
//            eq(testQueueEntry.getName()),
//            eq(testQueueEntry.isDurable()),
//            eq(testQueueEntry.isExclusive()),
//            eq(testQueueEntry.isAutoDelete()),
//            any()
//        );
//
//        verify(channel).queueBind(
//            eq(testQueueEntry.getName()),
//            eq(testQueueEntry.getExchange()),
//            eq(testQueueEntry.getRoutingKey())
//        );
//    }
//
//    @Test
//    void testUploadMessageWithRetry() throws Exception {
//        List<RabbitMessage> messages = new ArrayList<>();
//        RabbitMessage message = new RabbitMessage("test_key", "test content".getBytes());
//        messages.add(message);
//
//        doThrow(new IOException("Failed"))
//            .doNothing()
//            .when(channel)
//            .basicPublish(anyString(), anyString(), any(), any());
//
//        service.uploadMessage(messages);
//
//        verify(channel, times(2)).basicPublish(anyString(), anyString(), any(), any());
//        verify(logStream).write(
//            eq(LogStream.WARNING_MESSAGE), 
//            eq("Upload attempt 1/3 failed: Failed")
//        );
//    }
//
//    @Test
//    void testUploadMessageMaxRetryFailure() throws Exception {
//        List<RabbitMessage> messages = new ArrayList<>();
//        RabbitMessage message = new RabbitMessage("test_key", "test content".getBytes());
//        messages.add(message);
//
//        doThrow(new IOException("Failed"))
//            .when(channel)
//            .basicPublish(anyString(), anyString(), any(), any());
//
//        assertThrows(IOException.class, () -> service.uploadMessage(messages));
//        verify(channel, times(3)).basicPublish(anyString(), anyString(), any(), any());
//        verify(logStream, times(3)).write(
//            eq(LogStream.WARNING_MESSAGE), 
//            contains("Upload attempt")
//        );
//        verify(logStream).write(
//            eq(LogStream.ERROR_MESSAGE), 
//            eq("Failed to upload messages after 3 attempts")
//        );
//    }
//
//    @Test
//    void testClose() throws Exception {
//        service.close();
//
//        verify(channel).close();
//        verify(connection).close();
//        verify(logStream).write(
//            eq(LogStream.INFORMATION_MESSAGE), 
//            eq("RabbitMQ connection closed")
//        );
//    }
//}
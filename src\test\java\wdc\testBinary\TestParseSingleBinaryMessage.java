package wdc.testBinary;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.ProfileException;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryService;
import wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility;

public class TestParseSingleBinaryMessage {
	String binaryStr = "";
	
	@BeforeEach
	public void Before() {
		binaryStr = "0c00 0000 d907 d412 0b00 574d 5343 5230 3134 3000 01e2 2561 0300 0000";
		binaryStr.replaceAll(" ", "");
	}
	
	@Test
	public void testParseSingleBinaryMsg() {
		int result = ConvertorUtility.ConvertHexBytesToInteger("0c000000", true);
		System.out.println(result);
		int result2 = ConvertorUtility.ConvertHexBytesToInteger("d907d412", true);
		System.out.println(result2);
		int int1 = ConvertorUtility.ConvertHexBytesToInteger("0800",true);
		System.out.println("str:" + int1);
	}
	
	@Test
	public void testLong() {
		String str = "a5f0a5f0";
		long convertHexBytesToLong = ConvertorUtility.ConvertHexBytesToInteger(str, true);
		System.out.println(convertHexBytesToLong);
	}
	
	@Test
	public void testDouble() {
		String str = "c006b851eb851eb8";
		Double convertHexBytesToLong = ConvertorUtility.ConvertHexBytesToDouble(str, true);
		System.out.println(convertHexBytesToLong);
	}
	
	@Test
	public void testparseFile() throws IOException, ProfileException {
		String path = "C:\\Users\\<USER>\\Desktop\\220804171733.782";
		File file = new File(path);
		FileInputStream fis = new FileInputStream(file);
		BinaryService service = new BinaryService();
		fis = new FileInputStream(file);
		int tmp;
		StringBuffer sBuffer = new StringBuffer();
		while((tmp = fis.read()) != -1) {
			sBuffer.append(StringMethods.pad(Integer.toHexString(tmp), 2, '0', true));
		}
		System.out.println("raw data:" + sBuffer.toString());
		try {
			if(fis != null) {
				fis.close();						
				fis = null;
			}
		} catch (IOException e) {
			e.printStackTrace();
		} 
		String str = service.generatePdsStringForIndividualMsg(sBuffer.toString());
		System.out.println(str);
	}
	
	@Test
	public void testRecordMsgId() {
		String path = "/opt/ecs/storage/mq/sent/2_10_msg.ini";
		String separator = whichSeparator(path);
		int lastIndexOf = path.lastIndexOf(separator);
		String fileName = null;
		if(lastIndexOf != -1) {
			fileName = path.substring(lastIndexOf + 1);			
		}
		int positionOfMsgId = fileName.indexOf("_");
		String msgId = fileName.substring(0, positionOfMsgId);
		System.out.println(msgId);
		
	}
	
	public String whichSeparator(String path) {
		//Linux slash
		int indexOfSlashOne = path.indexOf("/");
		if(indexOfSlashOne != -1) {
			return "/";
		}
		//Windows slash
		int indexOfSlashTwo = path.indexOf("\\");
		if(indexOfSlashTwo != -1) {
			return "\\";
		}
		
		return "";
	}
	
}



















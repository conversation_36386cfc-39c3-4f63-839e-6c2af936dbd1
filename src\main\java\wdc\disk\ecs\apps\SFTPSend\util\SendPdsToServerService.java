package wdc.disk.ecs.apps.SFTPSend.util;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.Vector;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.SftpATTRS;
import com.jcraft.jsch.SftpException;

import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

public class SendPdsToServerService {
	private Session session;
	private ChannelSftp channel;
	private SendByMsgIdMode mode;
	private ibm.disk.profiles.DiskProfile dbMsgIDProfile;
	public static int RenameRate = 100;
	public SendMethod sendMethod;
	public LogStream log;
	private String folderNameString;
	private CreateShareFolderMode createShareFolderMode = CreateShareFolderMode.uploading;
	
	//init for one time ,so next time will fetch from this.
	private SendMethod sendDirectory = new SendDirectly();
	private SendMethod sendByMsgId = new SendByMsgId();
	private SendMethod splitSendByMsgId = new SplitSendByMsgId();
	private SendMethod sendAndSplitByQty = new SendAndSplitByQty();
		
		
	

	public SendPdsToServerService(DiskProfile dbMsgIDProfile, String modeStr, LogStream log,Session session, ChannelSftp channel) throws ProfileException {
		super();
		switchMode(modeStr);
		this.dbMsgIDProfile = dbMsgIDProfile;
		this.log = log;
		this.session = session;
		this.channel = channel;
	}

	public SendPdsToServerService(DiskProfile dbMsgIDProfile,LogStream log,Session session, ChannelSftp channel) throws ProfileException {
		super();
		this.dbMsgIDProfile = dbMsgIDProfile;
		this.log = log;
		this.session = session;
		this.channel = channel;
	}
	
	
	@SuppressWarnings("unchecked")
	public int getProfileSendQty(String msgId) {
		
		@SuppressWarnings("rawtypes")
		Vector keys = new Vector();
		keys.add(DBMsgIDRecord.COMPARE_BY_MSGID);
		keys.add(msgId);
		int sendQty = 0;
		try {
			DBMsgIDRecord dbMsgIDRec = (DBMsgIDRecord) dbMsgIDProfile.getRecord(1, keys);
			if(dbMsgIDRec != null) {
				sendQty = dbMsgIDRec.getSendQty();				
			}
		} catch (ProfileException e) {
			e.printStackTrace();
		}
		return sendQty;
	}
	
	public ArrayList<String> getMsgList() throws ProfileException{
		ArrayList<String> msgList = new ArrayList<String>();
		@SuppressWarnings("rawtypes")
		Vector allProfileRecords = dbMsgIDProfile.getAllProfileRecords();
		for(int i=0; i<allProfileRecords.size(); i++) {
			 String dbMsgID = ((DBMsgIDRecord) allProfileRecords.get(i)).getDBMsgID();
			 if(dbMsgID != null && !"".equals(dbMsgID)) {
				 msgList.add(dbMsgID.trim());				 
			 }
		}
		return msgList;
	}

	public String generateSendToServerDir(String fileName, String serverDirName) {
		if(mode == SendByMsgIdMode.SendDirectly) {
			return serverDirName;
		}else {
			String msgId = getFileMsgId(fileName);
			String destDirStr = serverDirName + msgId;
			return destDirStr;				
		}
	}
	
	public String getFileMsgId(String fileName) {
		String msgId = null;
		char underline = '_';
		int indexOfUnderLine = fileName.indexOf(underline);
		if(indexOfUnderLine != -1) {
			msgId = fileName.substring(0, indexOfUnderLine);
		}else {
			System.out.println("File name doesn't contain _. " + fileName);			
		}
		return msgId;
	}
	
	public void goBackToParentDir(String dir) throws SftpException {
		channel.cd(dir);
		channel.cd("..");
	}
	
	public int getDirFileQty(String path) throws SftpException {
		@SuppressWarnings("rawtypes")
		Vector ls = channel.ls(path);
		int size = ls.size();
		return size;
	}
	
	
	
	
	public boolean makeDirFromChannel(String dir) throws SftpException {
		log.write(LogStream.INFORMATION_MESSAGE, "Trying to create dir:" + dir );
		boolean result = false;
		checkSessionAndChannelAlive();
		
		//check director is exist?
		SftpATTRS sftpAttributes = null;
		try{
			sftpAttributes = channel.stat(dir);
		} catch (SftpException e) {//skip no such file exception
//			System.out.println("makeDirFromChannel.Skip no such file exception.");
		}

		boolean needCreateDir = false;
		if(null != sftpAttributes){
			//to fix issue of "289_current" is a file, not directory, delete messageid_current (289_current) file
			if(!sftpAttributes.isDir()){
				try{
					channel.rm(dir);
					needCreateDir = true;
//					sftpAttributes = channel.stat(dir);
				}
				catch (SftpException e) {
//					System.out.println("makeDirFromChannel.Remove dir exception.");
				}
			}
		}else {
			needCreateDir = true;
		}
		
		if(needCreateDir){
			try {
				System.out.println("Make dir:" + dir);
				channel.mkdir(dir);
				log.write("Make dir successful. Dir:" + dir);
				result = true;
			} catch (SftpException e) {
				if(e.id != 4) {
					System.out.println("makeDirFromChannel exception.");
					throw e;
				}
			}
			
//			try{
//				sftpAttributes = null;
//				sftpAttributes = channel.stat(dir);
//			} catch (SftpException e) {//skip no such file exception
//				System.out.println("Skip no such file exception.");
//			}
//			if(sftpAttributes == null || !sftpAttributes.isDir()) {
//				makeDirFromChannel(dir);
//			}
		}
		return result;
	}
	
	public boolean createDirForStartingFromChannel(String dir) throws SftpException {
		log.write(LogStream.INFORMATION_MESSAGE, "Trying to create dir:" + dir );
		boolean result = false;
		checkSessionAndChannelAlive();
		
		//check director is exist?
		SftpATTRS sftpAttributes = null;
		try{
			sftpAttributes = channel.stat(dir);
		} catch (SftpException e) {//skip no such file exception
//			System.out.println("makeDirFromChannel.Skip no such file exception.");
		}

		boolean needCreateDir = false;
		if(null != sftpAttributes){
			log.write(LogStream.INFORMATION_MESSAGE, "Dir already existed.Dir:" + dir );
			return false;
		}else {
			needCreateDir = true;
		}
		
		if(needCreateDir){
			try {
				System.out.println("Make dir:" + dir);
				channel.mkdir(dir);
				log.write("Make dir successfully. Dir:" + dir);
				result = true;
			} catch (SftpException e) {
				if(e.id != 4) {
					System.out.println("makeDirFromChannel exception.");
					throw e;
				}
			}
		}
		return result;
	}

	public void process(String serverDirName,String msgId, int fileIndex, String srcFilePath) throws SftpException {	
		//Check the server directory is existed.
		try{
			if(channel != null && channel.isConnected()) {
				channel.stat(serverDirName);				
			}else {
				log.write(LogStream.ERROR_MESSAGE, "Channel is null or disconnected.");
			}
		} catch (SftpException e) {//skip no such file exception
			e.printStackTrace();
			log.writeExceptionStack(e);
			log.write(LogStream.ERROR_MESSAGE, "Check server Dir.Dir:" + serverDirName );
		}
		
		sendMethod.process(this,serverDirName, msgId, fileIndex, srcFilePath, log);
	}

	public void renameMsgCurrentDir(String serverDir, String srcDir, String fileSep, String folderName) throws SftpException {
		String destDir = "";
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSS");
		String dateFormat = sdf.format(new Date());
		destDir = serverDir + fileSep + folderName + "_" + dateFormat; 
		channel.rename(srcDir, destDir);
	}

	public boolean compareProfileQtyAndServerFileQty(String msgId, String src_msgId_dir_current) throws SftpException {
		int dirFileQty = getDirFileQty(src_msgId_dir_current);
		int profileSendQty = getProfileSendQty(msgId);
		System.out.println("Dir File Qty: " + dirFileQty + ".Profile qty:" + profileSendQty);
		if(dirFileQty >= profileSendQty) {
			System.out.println("Exceed:" + true);
			return true;
		}
		return false;
	}
	
	public boolean compareSetQtyAndServerFileQty(int setQty, String src_msgId_dir_current) throws SftpException {
		int dirFileQty = getDirFileQty(src_msgId_dir_current);
		if(dirFileQty >= setQty) {
			return true;
		}
		return false;
	}
	
	public ChannelSftp getChannel() {
		return channel;
	}

	public void setChannel(ChannelSftp channel) {
		this.channel = channel;
	}

	public SendByMsgIdMode getMode() {
		return mode;
	}

	public void setMode(SendByMsgIdMode mode) {
		this.mode = mode;
	}
	
	public void switchMode(String modeStr) {
		if(modeStr == null || modeStr.isEmpty()){
			mode = SendByMsgIdMode.SendDirectly;
			this.sendMethod = new SendDirectly();
		}else {
			int value = Integer.valueOf(modeStr.trim()).intValue();
			switch (value) {
			case 0:
				this.mode = SendByMsgIdMode.SendDirectly;
				this.sendMethod = getSendDirectory();
				break;
			case 1:
				this.mode = SendByMsgIdMode.SendByMsgId;
				this.sendMethod = getSendByMsgId();
				break;
			case 2:
				this.mode = SendByMsgIdMode.SplitSendByMsgId;
				this.sendMethod = getSplitSendByMsgId();
				break;
			case 3:
				this.mode = SendByMsgIdMode.SendAndSplitByQty;
				this.sendMethod = getSendAndSplitByQty();
				break;
			default:
				this.mode = SendByMsgIdMode.SendDirectly;
				this.sendMethod = getSendDirectory();
				break;
			}
		}
	}

	public String getFolderNameString() {
		return folderNameString;
	}

	public void setFolderNameString(String folderNameString) {
		this.folderNameString = folderNameString;
	}
	
	public void checkSessionAndChannelAlive() throws SftpException {
		boolean sessionConn = this.session.isConnected();
		boolean channelConn = this.channel.isConnected();
		if(!sessionConn || !channelConn) {
			String msg = "Session conn:" + sessionConn + " channel conn:" + channelConn;
			System.out.println(msg);
			throw new SftpException(0, msg);
		}
			
	}

	//In share folder,will create empty message folder and message_current folder. 
	public void initializeMsgFolderInServer(ArrayList<String> msgList, String sftpServerDirName) throws SftpException {
		log.write("Initialize Msg Folder In Server.");
		for(int i=0; i<msgList.size(); i++) {
			String fileSeparater = "/";
			String msgId = msgList.get(i);
			String serverDir_msgId_dir = sftpServerDirName + fileSeparater + msgId;
			String serverDir_msgId_dir_current = sftpServerDirName + fileSeparater + msgId + fileSeparater + msgId + "_current";
			createDirForStartingFromChannel(serverDir_msgId_dir);
			createDirForStartingFromChannel(serverDir_msgId_dir_current);
		}
	}

	public void downloadProfileFromServer(String downloadProfilePath, String replacementProfile) {
		ChannelSftp channel = getChannel();
		if((null == downloadProfilePath) || downloadProfilePath.isEmpty()) {
			log.write("Download file path is empty.");
			return;
		}
		
		try {
			log.write("Trying to get conf from server.");
			channel.get(downloadProfilePath, replacementProfile);
			log.write("Main Configure file has been replaced.");
		} catch (SftpException e) {
			log.write("Main Configure file can't be replaced.");
			log.writeExceptionStack(e);
		}
	}

	public CreateShareFolderMode getCreateShareFolderMode() {
		return createShareFolderMode;
	}

	public void setCreateShareFolderMode(CreateShareFolderMode createShareFolderMode) {
		this.createShareFolderMode = createShareFolderMode;
	}

	public Session getSession() {
		return session;
	}

	public void setSession(Session session) {
		this.session = session;
	}

	public SendMethod getSendDirectory() {
		return sendDirectory;
	}

	public SendMethod getSendByMsgId() {
		return sendByMsgId;
	}

	public SendMethod getSplitSendByMsgId() {
		return splitSendByMsgId;
	}

	public SendMethod getSendAndSplitByQty() {
		return sendAndSplitByQty;
	}

	
	
}

package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for WASH_BATH_LIFE
 */
public class WASH_BATH_LIFE  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime dtEntry;
    private String resource;
    private String interval;
    private String preoxidePriorityLow;
    private String preoxidePriorityHigh;
    private String loaderDiwLife;
    private String loaderUsLife;
    private String usBathlife;
    private String msBathlife;
    private String loaderFilters;
    private String scrubBrushesLife;
    private String scrubFilters;
    private String usFilters;
    private String qdr1Filters;
    private String msFilters;
    private String rinseFilters;
    private String scrubBrushesDisk;
    private String preoxidePriorityLowLimit;
    private String preoxidePriorityHighLimit;
    private String loaderDiwLifeLimit;
    private String loaderUsLifeLimit;
    private String usBathlifeLimit;
    private String msBathlifeLimit;
    private String loaderFiltersLimit;
    private String scrubBrushesLifeLimit;
    private String scrubFiltersLimit;
    private String usFiltersLimit;
    private String qdr1FiltersLimit;
    private String msFiltersLimit;
    private String rinseFiltersLimit;
    private String scrubBrushesDiskLimit;
    private java.time.LocalDateTime preoxidePriorityLowLastupdateTs;
    private java.time.LocalDateTime preoxidePriorityHighLastupdateTs;
    private java.time.LocalDateTime loaderDiwLifeLastupdateTs;
    private java.time.LocalDateTime loaderUsLifeLastupdateTs;
    private java.time.LocalDateTime usBathlifeLastupdateTs;
    private java.time.LocalDateTime msBathlifeLastupdateTs;
    private java.time.LocalDateTime loaderFiltersLastupdateTs;
    private java.time.LocalDateTime scrubBrushesLifeLastupdateTs;
    private java.time.LocalDateTime scrubFiltersLastupdateTs;
    private java.time.LocalDateTime usFiltersLastupdateTs;
    private java.time.LocalDateTime qdr1FiltersLastupdateTs;
    private java.time.LocalDateTime msFiltersLastupdateTs;
    private java.time.LocalDateTime rinseFiltersLastupdateTs;
    private String comments;

    // Default constructor
    public WASH_BATH_LIFE() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getDtEntry() {
        return dtEntry;
    }

    public void setDtEntry(java.time.LocalDateTime dtEntry) {
        this.dtEntry = dtEntry;
    }

    public String getResource() {
        return resource;
    }

    public void setResource(String resource) {
        this.resource = resource;
    }

    public String getInterval() {
        return interval;
    }

    public void setInterval(String interval) {
        this.interval = interval;
    }

    public String getPreoxidePriorityLow() {
        return preoxidePriorityLow;
    }

    public void setPreoxidePriorityLow(String preoxidePriorityLow) {
        this.preoxidePriorityLow = preoxidePriorityLow;
    }

    public String getPreoxidePriorityHigh() {
        return preoxidePriorityHigh;
    }

    public void setPreoxidePriorityHigh(String preoxidePriorityHigh) {
        this.preoxidePriorityHigh = preoxidePriorityHigh;
    }

    public String getLoaderDiwLife() {
        return loaderDiwLife;
    }

    public void setLoaderDiwLife(String loaderDiwLife) {
        this.loaderDiwLife = loaderDiwLife;
    }

    public String getLoaderUsLife() {
        return loaderUsLife;
    }

    public void setLoaderUsLife(String loaderUsLife) {
        this.loaderUsLife = loaderUsLife;
    }

    public String getUsBathlife() {
        return usBathlife;
    }

    public void setUsBathlife(String usBathlife) {
        this.usBathlife = usBathlife;
    }

    public String getMsBathlife() {
        return msBathlife;
    }

    public void setMsBathlife(String msBathlife) {
        this.msBathlife = msBathlife;
    }

    public String getLoaderFilters() {
        return loaderFilters;
    }

    public void setLoaderFilters(String loaderFilters) {
        this.loaderFilters = loaderFilters;
    }

    public String getScrubBrushesLife() {
        return scrubBrushesLife;
    }

    public void setScrubBrushesLife(String scrubBrushesLife) {
        this.scrubBrushesLife = scrubBrushesLife;
    }

    public String getScrubFilters() {
        return scrubFilters;
    }

    public void setScrubFilters(String scrubFilters) {
        this.scrubFilters = scrubFilters;
    }

    public String getUsFilters() {
        return usFilters;
    }

    public void setUsFilters(String usFilters) {
        this.usFilters = usFilters;
    }

    public String getQdr1Filters() {
        return qdr1Filters;
    }

    public void setQdr1Filters(String qdr1Filters) {
        this.qdr1Filters = qdr1Filters;
    }

    public String getMsFilters() {
        return msFilters;
    }

    public void setMsFilters(String msFilters) {
        this.msFilters = msFilters;
    }

    public String getRinseFilters() {
        return rinseFilters;
    }

    public void setRinseFilters(String rinseFilters) {
        this.rinseFilters = rinseFilters;
    }

    public String getScrubBrushesDisk() {
        return scrubBrushesDisk;
    }

    public void setScrubBrushesDisk(String scrubBrushesDisk) {
        this.scrubBrushesDisk = scrubBrushesDisk;
    }

    public String getPreoxidePriorityLowLimit() {
        return preoxidePriorityLowLimit;
    }

    public void setPreoxidePriorityLowLimit(String preoxidePriorityLowLimit) {
        this.preoxidePriorityLowLimit = preoxidePriorityLowLimit;
    }

    public String getPreoxidePriorityHighLimit() {
        return preoxidePriorityHighLimit;
    }

    public void setPreoxidePriorityHighLimit(String preoxidePriorityHighLimit) {
        this.preoxidePriorityHighLimit = preoxidePriorityHighLimit;
    }

    public String getLoaderDiwLifeLimit() {
        return loaderDiwLifeLimit;
    }

    public void setLoaderDiwLifeLimit(String loaderDiwLifeLimit) {
        this.loaderDiwLifeLimit = loaderDiwLifeLimit;
    }

    public String getLoaderUsLifeLimit() {
        return loaderUsLifeLimit;
    }

    public void setLoaderUsLifeLimit(String loaderUsLifeLimit) {
        this.loaderUsLifeLimit = loaderUsLifeLimit;
    }

    public String getUsBathlifeLimit() {
        return usBathlifeLimit;
    }

    public void setUsBathlifeLimit(String usBathlifeLimit) {
        this.usBathlifeLimit = usBathlifeLimit;
    }

    public String getMsBathlifeLimit() {
        return msBathlifeLimit;
    }

    public void setMsBathlifeLimit(String msBathlifeLimit) {
        this.msBathlifeLimit = msBathlifeLimit;
    }

    public String getLoaderFiltersLimit() {
        return loaderFiltersLimit;
    }

    public void setLoaderFiltersLimit(String loaderFiltersLimit) {
        this.loaderFiltersLimit = loaderFiltersLimit;
    }

    public String getScrubBrushesLifeLimit() {
        return scrubBrushesLifeLimit;
    }

    public void setScrubBrushesLifeLimit(String scrubBrushesLifeLimit) {
        this.scrubBrushesLifeLimit = scrubBrushesLifeLimit;
    }

    public String getScrubFiltersLimit() {
        return scrubFiltersLimit;
    }

    public void setScrubFiltersLimit(String scrubFiltersLimit) {
        this.scrubFiltersLimit = scrubFiltersLimit;
    }

    public String getUsFiltersLimit() {
        return usFiltersLimit;
    }

    public void setUsFiltersLimit(String usFiltersLimit) {
        this.usFiltersLimit = usFiltersLimit;
    }

    public String getQdr1FiltersLimit() {
        return qdr1FiltersLimit;
    }

    public void setQdr1FiltersLimit(String qdr1FiltersLimit) {
        this.qdr1FiltersLimit = qdr1FiltersLimit;
    }

    public String getMsFiltersLimit() {
        return msFiltersLimit;
    }

    public void setMsFiltersLimit(String msFiltersLimit) {
        this.msFiltersLimit = msFiltersLimit;
    }

    public String getRinseFiltersLimit() {
        return rinseFiltersLimit;
    }

    public void setRinseFiltersLimit(String rinseFiltersLimit) {
        this.rinseFiltersLimit = rinseFiltersLimit;
    }

    public String getScrubBrushesDiskLimit() {
        return scrubBrushesDiskLimit;
    }

    public void setScrubBrushesDiskLimit(String scrubBrushesDiskLimit) {
        this.scrubBrushesDiskLimit = scrubBrushesDiskLimit;
    }

    public java.time.LocalDateTime getPreoxidePriorityLowLastupdateTs() {
        return preoxidePriorityLowLastupdateTs;
    }

    public void setPreoxidePriorityLowLastupdateTs(java.time.LocalDateTime preoxidePriorityLowLastupdateTs) {
        this.preoxidePriorityLowLastupdateTs = preoxidePriorityLowLastupdateTs;
    }

    public java.time.LocalDateTime getPreoxidePriorityHighLastupdateTs() {
        return preoxidePriorityHighLastupdateTs;
    }

    public void setPreoxidePriorityHighLastupdateTs(java.time.LocalDateTime preoxidePriorityHighLastupdateTs) {
        this.preoxidePriorityHighLastupdateTs = preoxidePriorityHighLastupdateTs;
    }

    public java.time.LocalDateTime getLoaderDiwLifeLastupdateTs() {
        return loaderDiwLifeLastupdateTs;
    }

    public void setLoaderDiwLifeLastupdateTs(java.time.LocalDateTime loaderDiwLifeLastupdateTs) {
        this.loaderDiwLifeLastupdateTs = loaderDiwLifeLastupdateTs;
    }

    public java.time.LocalDateTime getLoaderUsLifeLastupdateTs() {
        return loaderUsLifeLastupdateTs;
    }

    public void setLoaderUsLifeLastupdateTs(java.time.LocalDateTime loaderUsLifeLastupdateTs) {
        this.loaderUsLifeLastupdateTs = loaderUsLifeLastupdateTs;
    }

    public java.time.LocalDateTime getUsBathlifeLastupdateTs() {
        return usBathlifeLastupdateTs;
    }

    public void setUsBathlifeLastupdateTs(java.time.LocalDateTime usBathlifeLastupdateTs) {
        this.usBathlifeLastupdateTs = usBathlifeLastupdateTs;
    }

    public java.time.LocalDateTime getMsBathlifeLastupdateTs() {
        return msBathlifeLastupdateTs;
    }

    public void setMsBathlifeLastupdateTs(java.time.LocalDateTime msBathlifeLastupdateTs) {
        this.msBathlifeLastupdateTs = msBathlifeLastupdateTs;
    }

    public java.time.LocalDateTime getLoaderFiltersLastupdateTs() {
        return loaderFiltersLastupdateTs;
    }

    public void setLoaderFiltersLastupdateTs(java.time.LocalDateTime loaderFiltersLastupdateTs) {
        this.loaderFiltersLastupdateTs = loaderFiltersLastupdateTs;
    }

    public java.time.LocalDateTime getScrubBrushesLifeLastupdateTs() {
        return scrubBrushesLifeLastupdateTs;
    }

    public void setScrubBrushesLifeLastupdateTs(java.time.LocalDateTime scrubBrushesLifeLastupdateTs) {
        this.scrubBrushesLifeLastupdateTs = scrubBrushesLifeLastupdateTs;
    }

    public java.time.LocalDateTime getScrubFiltersLastupdateTs() {
        return scrubFiltersLastupdateTs;
    }

    public void setScrubFiltersLastupdateTs(java.time.LocalDateTime scrubFiltersLastupdateTs) {
        this.scrubFiltersLastupdateTs = scrubFiltersLastupdateTs;
    }

    public java.time.LocalDateTime getUsFiltersLastupdateTs() {
        return usFiltersLastupdateTs;
    }

    public void setUsFiltersLastupdateTs(java.time.LocalDateTime usFiltersLastupdateTs) {
        this.usFiltersLastupdateTs = usFiltersLastupdateTs;
    }

    public java.time.LocalDateTime getQdr1FiltersLastupdateTs() {
        return qdr1FiltersLastupdateTs;
    }

    public void setQdr1FiltersLastupdateTs(java.time.LocalDateTime qdr1FiltersLastupdateTs) {
        this.qdr1FiltersLastupdateTs = qdr1FiltersLastupdateTs;
    }

    public java.time.LocalDateTime getMsFiltersLastupdateTs() {
        return msFiltersLastupdateTs;
    }

    public void setMsFiltersLastupdateTs(java.time.LocalDateTime msFiltersLastupdateTs) {
        this.msFiltersLastupdateTs = msFiltersLastupdateTs;
    }

    public java.time.LocalDateTime getRinseFiltersLastupdateTs() {
        return rinseFiltersLastupdateTs;
    }

    public void setRinseFiltersLastupdateTs(java.time.LocalDateTime rinseFiltersLastupdateTs) {
        this.rinseFiltersLastupdateTs = rinseFiltersLastupdateTs;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

}



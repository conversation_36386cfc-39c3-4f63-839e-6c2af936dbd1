package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.GMONDATA_18;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for GMONDATA_18
 */
public interface GMONDATA_18_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.GMONDATA AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{diskid},",
        "    #{batchtime},",
        "    #{surface},",
        "    #{rule},",
        "    #{glideheadid},",
        "    #{hcf},",
        "    #{method},",
        "    #{amp},",
        "    #{dev},",
        "    #{glidehits},",
        "    #{bumphcf},",
        "    #{qcode},",
        "    #{recsent},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    DISK<PERSON>,",
        "    BATCHTIM<PERSON>,",
        "    SURFACE,",
        "    RULE,",
        "    GLIDEHEADID,",
        "    HCF,",
        "    METHOD,",
        "    AMP,",
        "    DEV,",
        "    GLIDEHITS,",
        "    BUMPHCF,",
        "    QCODE,",
        "    RECSENT,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.DISKID = SOURCE.DISKID AND TARGET.BATCHTIME = SOURCE.BATCHTIME AND TARGET.SURFACE = SOURCE.SURFACE AND TARGET.RULE = SOURCE.RULE AND TARGET.GLIDEHEADID = SOURCE.GLIDEHEADID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.HCF = SOURCE.HCF,",
        "    TARGET.METHOD = SOURCE.METHOD,",
        "    TARGET.AMP = SOURCE.AMP,",
        "    TARGET.DEV = SOURCE.DEV,",
        "    TARGET.GLIDEHITS = SOURCE.GLIDEHITS,",
        "    TARGET.BUMPHCF = SOURCE.BUMPHCF,",
        "    TARGET.QCODE = SOURCE.QCODE,",
        "    TARGET.RECSENT = SOURCE.RECSENT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    DISKID,",
        "    BATCHTIME,",
        "    SURFACE,",
        "    RULE,",
        "    GLIDEHEADID,",
        "    HCF,",
        "    METHOD,",
        "    AMP,",
        "    DEV,",
        "    GLIDEHITS,",
        "    BUMPHCF,",
        "    QCODE,",
        "    RECSENT,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.DISKID,",
        "    SOURCE.BATCHTIME,",
        "    SOURCE.SURFACE,",
        "    SOURCE.RULE,",
        "    SOURCE.GLIDEHEADID,",
        "    SOURCE.HCF,",
        "    SOURCE.METHOD,",
        "    SOURCE.AMP,",
        "    SOURCE.DEV,",
        "    SOURCE.GLIDEHITS,",
        "    SOURCE.BUMPHCF,",
        "    SOURCE.QCODE,",
        "    SOURCE.RECSENT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(GMONDATA_18 record);
}











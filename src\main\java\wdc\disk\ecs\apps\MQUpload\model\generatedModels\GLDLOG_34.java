package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for GLDLOG_34
 */
public class GLDLOG_34  {
    private Short spindle;
    private Integer glidenum;
    private Integer batchid;
    private Short productcode;
    private Integer diskcode;
    private String disksequence;
    private Short gliderule;
    private Short glidecode;
    private Integer qcode;
    private Integer toplevel;
    private Integer bottomlevel;
    private java.time.LocalDateTime startglide;
    private java.time.LocalDateTime stopglide;
    private Double offset;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;
    private String lot;
    private String unitid;
    private String botFull;
    private Integer lulSoftBot;
    private Integer lulCrashBot;
    private Integer lulHardBot;
    private Integer lulZonalBot;
    private Integer dataSoftBot;
    private Integer dataCrashBot;
    private Integer dataHardBot;
    private Integer dataZonalBot;
    private Integer idcSoftBot;
    private Integer idcCrashBot;
    private Integer idcHardBot;
    private Integer idcZonalBot;
    private String topFull;
    private Integer lulSoftTop;
    private Integer lulCrashTop;
    private Integer lulHardTop;
    private Integer lulZonalTop;
    private Integer dataSoftTop;
    private Integer dataCrashTop;
    private Integer dataHardTop;
    private Integer dataZonalTop;
    private Integer idcSoftTop;
    private Integer idcCrashTop;
    private Integer idcHardTop;
    private Integer idcZonalTop;
    private Short padwipeCount;
    private Short gldrulever;
    private String gldchecksum;
    private String expid;

    // Default constructor
    public GLDLOG_34() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getGlidenum() {
        return glidenum;
    }

    public void setGlidenum(Integer glidenum) {
        this.glidenum = glidenum;
    }

    public Integer getBatchid() {
        return batchid;
    }

    public void setBatchid(Integer batchid) {
        this.batchid = batchid;
    }

    public Short getProductcode() {
        return productcode;
    }

    public void setProductcode(Short productcode) {
        this.productcode = productcode;
    }

    public Integer getDiskcode() {
        return diskcode;
    }

    public void setDiskcode(Integer diskcode) {
        this.diskcode = diskcode;
    }

    public String getDisksequence() {
        return disksequence;
    }

    public void setDisksequence(String disksequence) {
        this.disksequence = disksequence;
    }

    public Short getGliderule() {
        return gliderule;
    }

    public void setGliderule(Short gliderule) {
        this.gliderule = gliderule;
    }

    public Short getGlidecode() {
        return glidecode;
    }

    public void setGlidecode(Short glidecode) {
        this.glidecode = glidecode;
    }

    public Integer getQcode() {
        return qcode;
    }

    public void setQcode(Integer qcode) {
        this.qcode = qcode;
    }

    public Integer getToplevel() {
        return toplevel;
    }

    public void setToplevel(Integer toplevel) {
        this.toplevel = toplevel;
    }

    public Integer getBottomlevel() {
        return bottomlevel;
    }

    public void setBottomlevel(Integer bottomlevel) {
        this.bottomlevel = bottomlevel;
    }

    public java.time.LocalDateTime getStartglide() {
        return startglide;
    }

    public void setStartglide(java.time.LocalDateTime startglide) {
        this.startglide = startglide;
    }

    public java.time.LocalDateTime getStopglide() {
        return stopglide;
    }

    public void setStopglide(java.time.LocalDateTime stopglide) {
        this.stopglide = stopglide;
    }

    public Double getOffset() {
        return offset;
    }

    public void setOffset(Double offset) {
        this.offset = offset;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

    public String getLot() {
        return lot;
    }

    public void setLot(String lot) {
        this.lot = lot;
    }

    public String getUnitid() {
        return unitid;
    }

    public void setUnitid(String unitid) {
        this.unitid = unitid;
    }

    public String getBotFull() {
        return botFull;
    }

    public void setBotFull(String botFull) {
        this.botFull = botFull;
    }

    public Integer getLulSoftBot() {
        return lulSoftBot;
    }

    public void setLulSoftBot(Integer lulSoftBot) {
        this.lulSoftBot = lulSoftBot;
    }

    public Integer getLulCrashBot() {
        return lulCrashBot;
    }

    public void setLulCrashBot(Integer lulCrashBot) {
        this.lulCrashBot = lulCrashBot;
    }

    public Integer getLulHardBot() {
        return lulHardBot;
    }

    public void setLulHardBot(Integer lulHardBot) {
        this.lulHardBot = lulHardBot;
    }

    public Integer getLulZonalBot() {
        return lulZonalBot;
    }

    public void setLulZonalBot(Integer lulZonalBot) {
        this.lulZonalBot = lulZonalBot;
    }

    public Integer getDataSoftBot() {
        return dataSoftBot;
    }

    public void setDataSoftBot(Integer dataSoftBot) {
        this.dataSoftBot = dataSoftBot;
    }

    public Integer getDataCrashBot() {
        return dataCrashBot;
    }

    public void setDataCrashBot(Integer dataCrashBot) {
        this.dataCrashBot = dataCrashBot;
    }

    public Integer getDataHardBot() {
        return dataHardBot;
    }

    public void setDataHardBot(Integer dataHardBot) {
        this.dataHardBot = dataHardBot;
    }

    public Integer getDataZonalBot() {
        return dataZonalBot;
    }

    public void setDataZonalBot(Integer dataZonalBot) {
        this.dataZonalBot = dataZonalBot;
    }

    public Integer getIdcSoftBot() {
        return idcSoftBot;
    }

    public void setIdcSoftBot(Integer idcSoftBot) {
        this.idcSoftBot = idcSoftBot;
    }

    public Integer getIdcCrashBot() {
        return idcCrashBot;
    }

    public void setIdcCrashBot(Integer idcCrashBot) {
        this.idcCrashBot = idcCrashBot;
    }

    public Integer getIdcHardBot() {
        return idcHardBot;
    }

    public void setIdcHardBot(Integer idcHardBot) {
        this.idcHardBot = idcHardBot;
    }

    public Integer getIdcZonalBot() {
        return idcZonalBot;
    }

    public void setIdcZonalBot(Integer idcZonalBot) {
        this.idcZonalBot = idcZonalBot;
    }

    public String getTopFull() {
        return topFull;
    }

    public void setTopFull(String topFull) {
        this.topFull = topFull;
    }

    public Integer getLulSoftTop() {
        return lulSoftTop;
    }

    public void setLulSoftTop(Integer lulSoftTop) {
        this.lulSoftTop = lulSoftTop;
    }

    public Integer getLulCrashTop() {
        return lulCrashTop;
    }

    public void setLulCrashTop(Integer lulCrashTop) {
        this.lulCrashTop = lulCrashTop;
    }

    public Integer getLulHardTop() {
        return lulHardTop;
    }

    public void setLulHardTop(Integer lulHardTop) {
        this.lulHardTop = lulHardTop;
    }

    public Integer getLulZonalTop() {
        return lulZonalTop;
    }

    public void setLulZonalTop(Integer lulZonalTop) {
        this.lulZonalTop = lulZonalTop;
    }

    public Integer getDataSoftTop() {
        return dataSoftTop;
    }

    public void setDataSoftTop(Integer dataSoftTop) {
        this.dataSoftTop = dataSoftTop;
    }

    public Integer getDataCrashTop() {
        return dataCrashTop;
    }

    public void setDataCrashTop(Integer dataCrashTop) {
        this.dataCrashTop = dataCrashTop;
    }

    public Integer getDataHardTop() {
        return dataHardTop;
    }

    public void setDataHardTop(Integer dataHardTop) {
        this.dataHardTop = dataHardTop;
    }

    public Integer getDataZonalTop() {
        return dataZonalTop;
    }

    public void setDataZonalTop(Integer dataZonalTop) {
        this.dataZonalTop = dataZonalTop;
    }

    public Integer getIdcSoftTop() {
        return idcSoftTop;
    }

    public void setIdcSoftTop(Integer idcSoftTop) {
        this.idcSoftTop = idcSoftTop;
    }

    public Integer getIdcCrashTop() {
        return idcCrashTop;
    }

    public void setIdcCrashTop(Integer idcCrashTop) {
        this.idcCrashTop = idcCrashTop;
    }

    public Integer getIdcHardTop() {
        return idcHardTop;
    }

    public void setIdcHardTop(Integer idcHardTop) {
        this.idcHardTop = idcHardTop;
    }

    public Integer getIdcZonalTop() {
        return idcZonalTop;
    }

    public void setIdcZonalTop(Integer idcZonalTop) {
        this.idcZonalTop = idcZonalTop;
    }

    public Short getPadwipeCount() {
        return padwipeCount;
    }

    public void setPadwipeCount(Short padwipeCount) {
        this.padwipeCount = padwipeCount;
    }

    public Short getGldrulever() {
        return gldrulever;
    }

    public void setGldrulever(Short gldrulever) {
        this.gldrulever = gldrulever;
    }

    public String getGldchecksum() {
        return gldchecksum;
    }

    public void setGldchecksum(String gldchecksum) {
        this.gldchecksum = gldchecksum;
    }

    public String getExpid() {
        return expid;
    }

    public void setExpid(String expid) {
        this.expid = expid;
    }

}



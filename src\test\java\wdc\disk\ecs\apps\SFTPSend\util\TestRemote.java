package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;

import org.junit.jupiter.api.Test;


public class TestRemote {
	@Test
	public void testWriteToServer() {
		String str = "C:\\Users\\<USER>\\Desktop\\initSendMail";
		File file = new File(str);
		System.out.println("exist : "+file.exists());
		String dest = "\\\\csfsvmip01a-419-d01.wdc.com\\CSF_Talend_Share\\init";
		boolean renameTo = file.renameTo(new File(dest));
		System.out.println(renameTo);
	}
}

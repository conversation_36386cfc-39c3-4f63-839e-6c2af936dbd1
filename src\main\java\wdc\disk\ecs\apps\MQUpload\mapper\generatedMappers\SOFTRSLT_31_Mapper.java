package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.SOFTRSLT_31;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for SOFTRSLT_31
 */
public interface SOFTRSLT_31_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.SOFTRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{testnum},",
        "    #{bandnum},",
        "    #{reqopernum},",
        "    #{votes},",
        "    #{topresult},",
        "    #{bottomresult},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    REQOPERNUM,",
        "    VOTES,",
        "    TOPRESULT,",
        "    BOTTOMRESULT,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TESTNUM = SOURCE.TESTNUM AND TARGET.BANDNUM = SOURCE.BANDNUM AND TARGET.REQOPERNUM = SOURCE.REQOPERNUM AND TARGET.VOTES = SOURCE.VOTES",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.TOPRESULT = SOURCE.TOPRESULT,",
        "    TARGET.BOTTOMRESULT = SOURCE.BOTTOMRESULT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    REQOPERNUM,",
        "    VOTES,",
        "    TOPRESULT,",
        "    BOTTOMRESULT,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.BANDNUM,",
        "    SOURCE.REQOPERNUM,",
        "    SOURCE.VOTES,",
        "    SOURCE.TOPRESULT,",
        "    SOURCE.BOTTOMRESULT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(SOFTRSLT_31 record);
}











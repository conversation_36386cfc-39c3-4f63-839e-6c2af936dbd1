package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSSCAN
 */
public class TDSSCAN  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private String rawdataFile;
    private String lot;
    private Short disksequence;
    private Double scanTimeMsec;
    private Double binNumber;
    private Double vacummPressure;
    private Integer spindle;
    private String mapFilename;
    private String product;
    private String cellId;
    private String finalGrade;
    private String glideGrade;
    private String certGrade;
    private String recipeFilename;
    private String hsa1Id;
    private String hsa2Id;
    private String hsa3Id;
    private Double totalToscCount;
    private String evalCode;
    private Double hsa1CycleCount;
    private Double hsa2CycleCount;
    private Double hsa3CycleCount;
    private String testerType;
    private String softwareVersion;
    private Double scanStartRadiusMm;
    private Double scanStopRadiusMm;
    private String formFactor;
    private Double scanOption;
    private Double hbo;
    private String autoSitemarkByGrade;
    private String supplyCassette;
    private String outputCassette;
    private String outputLot;
    private Short outputSlot;
    private String unitid;
    private Integer ruleNumber;
    private String expid;
    private String inputVariety;
    private String robotClass;
    private String retested;
    private String acErased;
    private String certifyScanned;
    private String tdsScanned;
    private String tdesDfaActive;
    private String tdesDfaMarked;
    private String testSequence;
    private String finalgradePass;
    private String topgradePass;
    private String botgradePass;
    private String topgrade;
    private String botgrade;
    private Double checksumGeneral;
    private Double checksumTds;
    private Double checksumCert;
    private Double checksumGrading;
    private String rttcCounts;
    private String dcErased;
    private Short testedTonescan;

    // Default constructor
    public TDSSCAN() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public String getRawdataFile() {
        return rawdataFile;
    }

    public void setRawdataFile(String rawdataFile) {
        this.rawdataFile = rawdataFile;
    }

    public String getLot() {
        return lot;
    }

    public void setLot(String lot) {
        this.lot = lot;
    }

    public Short getDisksequence() {
        return disksequence;
    }

    public void setDisksequence(Short disksequence) {
        this.disksequence = disksequence;
    }

    public Double getScanTimeMsec() {
        return scanTimeMsec;
    }

    public void setScanTimeMsec(Double scanTimeMsec) {
        this.scanTimeMsec = scanTimeMsec;
    }

    public Double getBinNumber() {
        return binNumber;
    }

    public void setBinNumber(Double binNumber) {
        this.binNumber = binNumber;
    }

    public Double getVacummPressure() {
        return vacummPressure;
    }

    public void setVacummPressure(Double vacummPressure) {
        this.vacummPressure = vacummPressure;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getMapFilename() {
        return mapFilename;
    }

    public void setMapFilename(String mapFilename) {
        this.mapFilename = mapFilename;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public String getCellId() {
        return cellId;
    }

    public void setCellId(String cellId) {
        this.cellId = cellId;
    }

    public String getFinalGrade() {
        return finalGrade;
    }

    public void setFinalGrade(String finalGrade) {
        this.finalGrade = finalGrade;
    }

    public String getGlideGrade() {
        return glideGrade;
    }

    public void setGlideGrade(String glideGrade) {
        this.glideGrade = glideGrade;
    }

    public String getCertGrade() {
        return certGrade;
    }

    public void setCertGrade(String certGrade) {
        this.certGrade = certGrade;
    }

    public String getRecipeFilename() {
        return recipeFilename;
    }

    public void setRecipeFilename(String recipeFilename) {
        this.recipeFilename = recipeFilename;
    }

    public String getHsa1Id() {
        return hsa1Id;
    }

    public void setHsa1Id(String hsa1Id) {
        this.hsa1Id = hsa1Id;
    }

    public String getHsa2Id() {
        return hsa2Id;
    }

    public void setHsa2Id(String hsa2Id) {
        this.hsa2Id = hsa2Id;
    }

    public String getHsa3Id() {
        return hsa3Id;
    }

    public void setHsa3Id(String hsa3Id) {
        this.hsa3Id = hsa3Id;
    }

    public Double getTotalToscCount() {
        return totalToscCount;
    }

    public void setTotalToscCount(Double totalToscCount) {
        this.totalToscCount = totalToscCount;
    }

    public String getEvalCode() {
        return evalCode;
    }

    public void setEvalCode(String evalCode) {
        this.evalCode = evalCode;
    }

    public Double getHsa1CycleCount() {
        return hsa1CycleCount;
    }

    public void setHsa1CycleCount(Double hsa1CycleCount) {
        this.hsa1CycleCount = hsa1CycleCount;
    }

    public Double getHsa2CycleCount() {
        return hsa2CycleCount;
    }

    public void setHsa2CycleCount(Double hsa2CycleCount) {
        this.hsa2CycleCount = hsa2CycleCount;
    }

    public Double getHsa3CycleCount() {
        return hsa3CycleCount;
    }

    public void setHsa3CycleCount(Double hsa3CycleCount) {
        this.hsa3CycleCount = hsa3CycleCount;
    }

    public String getTesterType() {
        return testerType;
    }

    public void setTesterType(String testerType) {
        this.testerType = testerType;
    }

    public String getSoftwareVersion() {
        return softwareVersion;
    }

    public void setSoftwareVersion(String softwareVersion) {
        this.softwareVersion = softwareVersion;
    }

    public Double getScanStartRadiusMm() {
        return scanStartRadiusMm;
    }

    public void setScanStartRadiusMm(Double scanStartRadiusMm) {
        this.scanStartRadiusMm = scanStartRadiusMm;
    }

    public Double getScanStopRadiusMm() {
        return scanStopRadiusMm;
    }

    public void setScanStopRadiusMm(Double scanStopRadiusMm) {
        this.scanStopRadiusMm = scanStopRadiusMm;
    }

    public String getFormFactor() {
        return formFactor;
    }

    public void setFormFactor(String formFactor) {
        this.formFactor = formFactor;
    }

    public Double getScanOption() {
        return scanOption;
    }

    public void setScanOption(Double scanOption) {
        this.scanOption = scanOption;
    }

    public Double getHbo() {
        return hbo;
    }

    public void setHbo(Double hbo) {
        this.hbo = hbo;
    }

    public String getAutoSitemarkByGrade() {
        return autoSitemarkByGrade;
    }

    public void setAutoSitemarkByGrade(String autoSitemarkByGrade) {
        this.autoSitemarkByGrade = autoSitemarkByGrade;
    }

    public String getSupplyCassette() {
        return supplyCassette;
    }

    public void setSupplyCassette(String supplyCassette) {
        this.supplyCassette = supplyCassette;
    }

    public String getOutputCassette() {
        return outputCassette;
    }

    public void setOutputCassette(String outputCassette) {
        this.outputCassette = outputCassette;
    }

    public String getOutputLot() {
        return outputLot;
    }

    public void setOutputLot(String outputLot) {
        this.outputLot = outputLot;
    }

    public Short getOutputSlot() {
        return outputSlot;
    }

    public void setOutputSlot(Short outputSlot) {
        this.outputSlot = outputSlot;
    }

    public String getUnitid() {
        return unitid;
    }

    public void setUnitid(String unitid) {
        this.unitid = unitid;
    }

    public Integer getRuleNumber() {
        return ruleNumber;
    }

    public void setRuleNumber(Integer ruleNumber) {
        this.ruleNumber = ruleNumber;
    }

    public String getExpid() {
        return expid;
    }

    public void setExpid(String expid) {
        this.expid = expid;
    }

    public String getInputVariety() {
        return inputVariety;
    }

    public void setInputVariety(String inputVariety) {
        this.inputVariety = inputVariety;
    }

    public String getRobotClass() {
        return robotClass;
    }

    public void setRobotClass(String robotClass) {
        this.robotClass = robotClass;
    }

    public String getRetested() {
        return retested;
    }

    public void setRetested(String retested) {
        this.retested = retested;
    }

    public String getAcErased() {
        return acErased;
    }

    public void setAcErased(String acErased) {
        this.acErased = acErased;
    }

    public String getCertifyScanned() {
        return certifyScanned;
    }

    public void setCertifyScanned(String certifyScanned) {
        this.certifyScanned = certifyScanned;
    }

    public String getTdsScanned() {
        return tdsScanned;
    }

    public void setTdsScanned(String tdsScanned) {
        this.tdsScanned = tdsScanned;
    }

    public String getTdesDfaActive() {
        return tdesDfaActive;
    }

    public void setTdesDfaActive(String tdesDfaActive) {
        this.tdesDfaActive = tdesDfaActive;
    }

    public String getTdesDfaMarked() {
        return tdesDfaMarked;
    }

    public void setTdesDfaMarked(String tdesDfaMarked) {
        this.tdesDfaMarked = tdesDfaMarked;
    }

    public String getTestSequence() {
        return testSequence;
    }

    public void setTestSequence(String testSequence) {
        this.testSequence = testSequence;
    }

    public String getFinalgradePass() {
        return finalgradePass;
    }

    public void setFinalgradePass(String finalgradePass) {
        this.finalgradePass = finalgradePass;
    }

    public String getTopgradePass() {
        return topgradePass;
    }

    public void setTopgradePass(String topgradePass) {
        this.topgradePass = topgradePass;
    }

    public String getBotgradePass() {
        return botgradePass;
    }

    public void setBotgradePass(String botgradePass) {
        this.botgradePass = botgradePass;
    }

    public String getTopgrade() {
        return topgrade;
    }

    public void setTopgrade(String topgrade) {
        this.topgrade = topgrade;
    }

    public String getBotgrade() {
        return botgrade;
    }

    public void setBotgrade(String botgrade) {
        this.botgrade = botgrade;
    }

    public Double getChecksumGeneral() {
        return checksumGeneral;
    }

    public void setChecksumGeneral(Double checksumGeneral) {
        this.checksumGeneral = checksumGeneral;
    }

    public Double getChecksumTds() {
        return checksumTds;
    }

    public void setChecksumTds(Double checksumTds) {
        this.checksumTds = checksumTds;
    }

    public Double getChecksumCert() {
        return checksumCert;
    }

    public void setChecksumCert(Double checksumCert) {
        this.checksumCert = checksumCert;
    }

    public Double getChecksumGrading() {
        return checksumGrading;
    }

    public void setChecksumGrading(Double checksumGrading) {
        this.checksumGrading = checksumGrading;
    }

    public String getRttcCounts() {
        return rttcCounts;
    }

    public void setRttcCounts(String rttcCounts) {
        this.rttcCounts = rttcCounts;
    }

    public String getDcErased() {
        return dcErased;
    }

    public void setDcErased(String dcErased) {
        this.dcErased = dcErased;
    }

    public Short getTestedTonescan() {
        return testedTonescan;
    }

    public void setTestedTonescan(Short testedTonescan) {
        this.testedTonescan = testedTonescan;
    }

}



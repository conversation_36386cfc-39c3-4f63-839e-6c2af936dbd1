#!/bin/bash

# Set the classpath to the project's JAR file.
# This assumes the script is being run from the 'scripts' directory.
CLASSPATH="../target/SFTPSend-1.0-SNAPSHOT.jar"

# The main class to execute
MAIN_CLASS="wdc.disk.ecs.apps.MQUpload.processor.FileInfoProcessor"

# --- CONFIGURATION ---
# Please fill in the source and destination directories below.
SOURCE_DIR=""
DEST_DIR=""
# ---------------------

# Check if directories are configured
if [ -z "${SOURCE_DIR}" ] || [ -z "${DEST_DIR}" ]; then
    echo "Error: Please edit this script and set the SOURCE_DIR and DEST_DIR variables."
    exit 1
fi

echo "Starting FileInfoProcessor..."
echo "Source Directory: ${SOURCE_DIR}"
echo "Destination Directory: ${DEST_DIR}"

# Run the Java application
java -cp "${CLASSPATH}" "${MAIN_CLASS}" "${SOURCE_DIR}" "${DEST_DIR}"

echo "FileInfoProcessor finished."

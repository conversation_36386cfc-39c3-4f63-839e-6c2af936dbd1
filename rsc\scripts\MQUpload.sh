#!/bin/bash

# Set application variables
APP_HOME=/opt/MQUpload
CONFIG_PROFILE=${APP_HOME}/config/MQ_Upload.yml
LOG_FILE=${APP_HOME}/logs/mqupload.log
CLASSPATH=${APP_HOME}/target/*.jar

# Create necessary directories if they don't exist
mkdir -p "${APP_HOME}/logs"
mkdir -p "${APP_HOME}/config"

# Run application
echo "Starting MQUpload application..."
java -Dapp.env=DEV -cp "${CLASSPATH}" wdc.disk.ecs.apps.MQUpload.producer.MQUpload "${CONFIG_PROFILE}" "${LOG_FILE}"
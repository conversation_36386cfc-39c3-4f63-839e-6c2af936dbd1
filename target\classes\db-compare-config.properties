# Production DB Configuration
prod.db.url=***************************************************
prod.db.user=ndutld
prod.db.password=Wdc@te678

# Test DB Configuration
test.db.url=***************************************************
test.db.user=ndutld
test.db.password=Itc@me123

# Table Configurations
tables.count=8

# Table 1
table.1.name=DB2SYS.TDS_CHANNEL_STAT
table.1.dateColumn=MEAS_TIME
table.1.condition=SPINDLE = 10151

# Table 2
table.2.name=DB2SYS.TDS_SCAN
table.2.dateColumn=MEAS_TIME
table.2.condition=SPINDLE = 10151

# Table 3
table.3.name=DB2SYS.TDS_HEAD_STAT
table.3.dateColumn=MEAS_TIME
table.3.condition=SPINDLE = 10151

# Table 4
table.4.name=DB2SYS.TDS_GRADE
table.4.dateColumn=MEAS_TIME
table.4.condition=SPINDLE = 10151

# Table 5
table.5.name=DB2SYS.TDS_SITEMARK_CLUS
table.5.dateColumn=MEAS_TIME
table.5.condition=SPINDLE = 10151

# Table 6
table.6.name=DB2SYS.TDS_ENG
table.6.dateColumn=MEAS_TIME
table.6.condition=SPINDLE = 10151

# Table 7
table.7.name=DB2SYS.TDS_CLUSTER_DATA
table.7.dateColumn=MEAS_TIME
table.7.condition=SPINDLE = 10151

# Table 8
table.8.name=DB2SYS.TDS_CLUSTER
table.8.dateColumn=MEAS_TIME
table.8.condition=SPINDLE = 10151




package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TDSCHANNEL;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TDSCHANNEL
 */
public interface TDSCHANNEL_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TDS_CHANNEL_STAT AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{measTime},",
        "    #{spindle},",
        "    #{channelId},",
        "    #{headId},",
        "    #{totalTaCnt},",
        "    #{smallTaCnt},",
        "    #{mediumTaCnt},",
        "    #{largeTaCnt},",
        "    #{monsterTaCnt},",
        "    #{positiveTaCnt},",
        "    #{avgClusterMaxAbsAmp},",
        "    #{totalClusterCnt},",
        "    #{qualifiedClusterCnt},",
        "    #{totalCdpCnt},",
        "    #{totalVoidCnt},",
        "    #{smallVoidCnt},",
        "    #{mediumVoidCnt},",
        "    #{largeVoidCnt},",
        "    #{lmsCnt},",
        "    #{cscratchCnt},",
        "    #{patchCnt},",
        "    #{taMaxRad},",
        "    #{taMinRad},",
        "    #{taMaxArea},",
        "    #{smallClusterCnt},",
        "    #{mediumClusterCnt},",
        "    #{largeClusterCnt},",
        "    #{totalContamCnt},",
        "    #{scratchCnt},",
        "    #{corruptFlag},",
        "    #{totalTaCntBin1},",
        "    #{totalTaCntBin2},",
        "    #{totalTaCntBin3},",
        "    #{totalTaCntBin4},",
        "    #{totalTaCntBin5},",
        "    #{totalTaCntBin6},",
        "    #{totalTaCntBin7},",
        "    #{totalTaCntBin8},",
        "    #{totalTaCntBin9},",
        "    #{totalTaCntBin10},",
        "    #{totalTaCntBin11},",
        "    #{totalTaCntBin12},",
        "    #{totalTaCntBin13},",
        "    #{totalTaCntBin14},",
        "    #{totalTaCntBin15},",
        "    #{htaCnt},",
        "    #{ltaCnt},",
        "    #{widetaCnt},",
        "    #{largePosTaCnt},",
        "    #{totalTaPdiCnt},",
        "    #{totalTaHeaddingCnt},",
        "    #{totalTaCirbliscCnt},",
        "    #{totalTaIrrpmrCnt},",
        "    #{totalTaCirblisrpCnt},",
        "    #{totalTaCsurfaceCnt},",
        "    #{totalTaUnknownCnt},",
        "    #{smallTaPdiCnt},",
        "    #{smallTaHeaddingCnt},",
        "    #{smallTaCirbliscCnt},",
        "    #{smallTaIrrpmrCnt},",
        "    #{smallTaCirblisrpCnt},",
        "    #{smallTaCsurfaceCnt},",
        "    #{smallTaUnknownCnt},",
        "    #{mediumTaPdiCnt},",
        "    #{mediumTaHeaddingCnt},",
        "    #{mediumTaCirbliscCnt},",
        "    #{mediumTaIrrpmrCnt},",
        "    #{mediumTaCirblisrpCnt},",
        "    #{mediumTaCsurfaceCnt},",
        "    #{mediumTaUnknownCnt},",
        "    #{largeTaPdiCnt},",
        "    #{largeTaHeaddingCnt},",
        "    #{largeTaCirbliscCnt},",
        "    #{largeTaIrrpmrCnt},",
        "    #{largeTaCirblisrpCnt},",
        "    #{largeTaCsurfaceCnt},",
        "    #{largeTaUnknownCnt},",
        "    #{carbonVoidCnt},",
        "    #{lddCnt},",
        "    #{avgRmsDeltaMv},",
        "    #{patchTaCnt},",
        "    #{patchVoidCnt},",
        "    #{patchContamCnt},",
        "    #{ncorrCnt},",
        "    #{corrCnt}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    HEAD_ID,",
        "    TOTAL_TA_CNT,",
        "    SMALL_TA_CNT,",
        "    MEDIUM_TA_CNT,",
        "    LARGE_TA_CNT,",
        "    MONSTER_TA_CNT,",
        "    POSITIVE_TA_CNT,",
        "    AVG_CLUSTER_MAX_ABS_AMP,",
        "    TOTAL_CLUSTER_CNT,",
        "    QUALIFIED_CLUSTER_CNT,",
        "    TOTAL_CDP_CNT,",
        "    TOTAL_VOID_CNT,",
        "    SMALL_VOID_CNT,",
        "    MEDIUM_VOID_CNT,",
        "    LARGE_VOID_CNT,",
        "    LMS_CNT,",
        "    CSCRATCH_CNT,",
        "    PATCH_CNT,",
        "    TA_MAX_RAD,",
        "    TA_MIN_RAD,",
        "    TA_MAX_AREA,",
        "    SMALL_CLUSTER_CNT,",
        "    MEDIUM_CLUSTER_CNT,",
        "    LARGE_CLUSTER_CNT,",
        "    TOTAL_CONTAM_CNT,",
        "    SCRATCH_CNT,",
        "    CORRUPT_FLAG,",
        "    TOTAL_TA_CNT_BIN1,",
        "    TOTAL_TA_CNT_BIN2,",
        "    TOTAL_TA_CNT_BIN3,",
        "    TOTAL_TA_CNT_BIN4,",
        "    TOTAL_TA_CNT_BIN5,",
        "    TOTAL_TA_CNT_BIN6,",
        "    TOTAL_TA_CNT_BIN7,",
        "    TOTAL_TA_CNT_BIN8,",
        "    TOTAL_TA_CNT_BIN9,",
        "    TOTAL_TA_CNT_BIN10,",
        "    TOTAL_TA_CNT_BIN11,",
        "    TOTAL_TA_CNT_BIN12,",
        "    TOTAL_TA_CNT_BIN13,",
        "    TOTAL_TA_CNT_BIN14,",
        "    TOTAL_TA_CNT_BIN15,",
        "    HTA_CNT,",
        "    LTA_CNT,",
        "    WIDETA_CNT,",
        "    LARGE_POS_TA_CNT,",
        "    TOTAL_TA_PDI_CNT,",
        "    TOTAL_TA_HEADDING_CNT,",
        "    TOTAL_TA_CIRBLISC_CNT,",
        "    TOTAL_TA_IRRPMR_CNT,",
        "    TOTAL_TA_CIRBLISRP_CNT,",
        "    TOTAL_TA_CSURFACE_CNT,",
        "    TOTAL_TA_UNKNOWN_CNT,",
        "    SMALL_TA_PDI_CNT,",
        "    SMALL_TA_HEADDING_CNT,",
        "    SMALL_TA_CIRBLISC_CNT,",
        "    SMALL_TA_IRRPMR_CNT,",
        "    SMALL_TA_CIRBLISRP_CNT,",
        "    SMALL_TA_CSURFACE_CNT,",
        "    SMALL_TA_UNKNOWN_CNT,",
        "    MEDIUM_TA_PDI_CNT,",
        "    MEDIUM_TA_HEADDING_CNT,",
        "    MEDIUM_TA_CIRBLISC_CNT,",
        "    MEDIUM_TA_IRRPMR_CNT,",
        "    MEDIUM_TA_CIRBLISRP_CNT,",
        "    MEDIUM_TA_CSURFACE_CNT,",
        "    MEDIUM_TA_UNKNOWN_CNT,",
        "    LARGE_TA_PDI_CNT,",
        "    LARGE_TA_HEADDING_CNT,",
        "    LARGE_TA_CIRBLISC_CNT,",
        "    LARGE_TA_IRRPMR_CNT,",
        "    LARGE_TA_CIRBLISRP_CNT,",
        "    LARGE_TA_CSURFACE_CNT,",
        "    LARGE_TA_UNKNOWN_CNT,",
        "    CARBON_VOID_CNT,",
        "    LDD_CNT,",
        "    AVG_RMS_DELTA_MV,",
        "    PATCH_TA_CNT,",
        "    PATCH_VOID_CNT,",
        "    PATCH_CONTAM_CNT,",
        "    NCORR_CNT,",
        "    CORR_CNT",
        ")",
        "ON TARGET.MEAS_TIME = SOURCE.MEAS_TIME AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CHANNEL_ID = SOURCE.CHANNEL_ID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.HEAD_ID = SOURCE.HEAD_ID,",
        "    TARGET.TOTAL_TA_CNT = SOURCE.TOTAL_TA_CNT,",
        "    TARGET.SMALL_TA_CNT = SOURCE.SMALL_TA_CNT,",
        "    TARGET.MEDIUM_TA_CNT = SOURCE.MEDIUM_TA_CNT,",
        "    TARGET.LARGE_TA_CNT = SOURCE.LARGE_TA_CNT,",
        "    TARGET.MONSTER_TA_CNT = SOURCE.MONSTER_TA_CNT,",
        "    TARGET.POSITIVE_TA_CNT = SOURCE.POSITIVE_TA_CNT,",
        "    TARGET.AVG_CLUSTER_MAX_ABS_AMP = SOURCE.AVG_CLUSTER_MAX_ABS_AMP,",
        "    TARGET.TOTAL_CLUSTER_CNT = SOURCE.TOTAL_CLUSTER_CNT,",
        "    TARGET.QUALIFIED_CLUSTER_CNT = SOURCE.QUALIFIED_CLUSTER_CNT,",
        "    TARGET.TOTAL_CDP_CNT = SOURCE.TOTAL_CDP_CNT,",
        "    TARGET.TOTAL_VOID_CNT = SOURCE.TOTAL_VOID_CNT,",
        "    TARGET.SMALL_VOID_CNT = SOURCE.SMALL_VOID_CNT,",
        "    TARGET.MEDIUM_VOID_CNT = SOURCE.MEDIUM_VOID_CNT,",
        "    TARGET.LARGE_VOID_CNT = SOURCE.LARGE_VOID_CNT,",
        "    TARGET.LMS_CNT = SOURCE.LMS_CNT,",
        "    TARGET.CSCRATCH_CNT = SOURCE.CSCRATCH_CNT,",
        "    TARGET.PATCH_CNT = SOURCE.PATCH_CNT,",
        "    TARGET.TA_MAX_RAD = SOURCE.TA_MAX_RAD,",
        "    TARGET.TA_MIN_RAD = SOURCE.TA_MIN_RAD,",
        "    TARGET.TA_MAX_AREA = SOURCE.TA_MAX_AREA,",
        "    TARGET.SMALL_CLUSTER_CNT = SOURCE.SMALL_CLUSTER_CNT,",
        "    TARGET.MEDIUM_CLUSTER_CNT = SOURCE.MEDIUM_CLUSTER_CNT,",
        "    TARGET.LARGE_CLUSTER_CNT = SOURCE.LARGE_CLUSTER_CNT,",
        "    TARGET.TOTAL_CONTAM_CNT = SOURCE.TOTAL_CONTAM_CNT,",
        "    TARGET.SCRATCH_CNT = SOURCE.SCRATCH_CNT,",
        "    TARGET.CORRUPT_FLAG = SOURCE.CORRUPT_FLAG,",
        "    TARGET.TOTAL_TA_CNT_BIN1 = SOURCE.TOTAL_TA_CNT_BIN1,",
        "    TARGET.TOTAL_TA_CNT_BIN2 = SOURCE.TOTAL_TA_CNT_BIN2,",
        "    TARGET.TOTAL_TA_CNT_BIN3 = SOURCE.TOTAL_TA_CNT_BIN3,",
        "    TARGET.TOTAL_TA_CNT_BIN4 = SOURCE.TOTAL_TA_CNT_BIN4,",
        "    TARGET.TOTAL_TA_CNT_BIN5 = SOURCE.TOTAL_TA_CNT_BIN5,",
        "    TARGET.TOTAL_TA_CNT_BIN6 = SOURCE.TOTAL_TA_CNT_BIN6,",
        "    TARGET.TOTAL_TA_CNT_BIN7 = SOURCE.TOTAL_TA_CNT_BIN7,",
        "    TARGET.TOTAL_TA_CNT_BIN8 = SOURCE.TOTAL_TA_CNT_BIN8,",
        "    TARGET.TOTAL_TA_CNT_BIN9 = SOURCE.TOTAL_TA_CNT_BIN9,",
        "    TARGET.TOTAL_TA_CNT_BIN10 = SOURCE.TOTAL_TA_CNT_BIN10,",
        "    TARGET.TOTAL_TA_CNT_BIN11 = SOURCE.TOTAL_TA_CNT_BIN11,",
        "    TARGET.TOTAL_TA_CNT_BIN12 = SOURCE.TOTAL_TA_CNT_BIN12,",
        "    TARGET.TOTAL_TA_CNT_BIN13 = SOURCE.TOTAL_TA_CNT_BIN13,",
        "    TARGET.TOTAL_TA_CNT_BIN14 = SOURCE.TOTAL_TA_CNT_BIN14,",
        "    TARGET.TOTAL_TA_CNT_BIN15 = SOURCE.TOTAL_TA_CNT_BIN15,",
        "    TARGET.HTA_CNT = SOURCE.HTA_CNT,",
        "    TARGET.LTA_CNT = SOURCE.LTA_CNT,",
        "    TARGET.WIDETA_CNT = SOURCE.WIDETA_CNT,",
        "    TARGET.LARGE_POS_TA_CNT = SOURCE.LARGE_POS_TA_CNT,",
        "    TARGET.TOTAL_TA_PDI_CNT = SOURCE.TOTAL_TA_PDI_CNT,",
        "    TARGET.TOTAL_TA_HEADDING_CNT = SOURCE.TOTAL_TA_HEADDING_CNT,",
        "    TARGET.TOTAL_TA_CIRBLISC_CNT = SOURCE.TOTAL_TA_CIRBLISC_CNT,",
        "    TARGET.TOTAL_TA_IRRPMR_CNT = SOURCE.TOTAL_TA_IRRPMR_CNT,",
        "    TARGET.TOTAL_TA_CIRBLISRP_CNT = SOURCE.TOTAL_TA_CIRBLISRP_CNT,",
        "    TARGET.TOTAL_TA_CSURFACE_CNT = SOURCE.TOTAL_TA_CSURFACE_CNT,",
        "    TARGET.TOTAL_TA_UNKNOWN_CNT = SOURCE.TOTAL_TA_UNKNOWN_CNT,",
        "    TARGET.SMALL_TA_PDI_CNT = SOURCE.SMALL_TA_PDI_CNT,",
        "    TARGET.SMALL_TA_HEADDING_CNT = SOURCE.SMALL_TA_HEADDING_CNT,",
        "    TARGET.SMALL_TA_CIRBLISC_CNT = SOURCE.SMALL_TA_CIRBLISC_CNT,",
        "    TARGET.SMALL_TA_IRRPMR_CNT = SOURCE.SMALL_TA_IRRPMR_CNT,",
        "    TARGET.SMALL_TA_CIRBLISRP_CNT = SOURCE.SMALL_TA_CIRBLISRP_CNT,",
        "    TARGET.SMALL_TA_CSURFACE_CNT = SOURCE.SMALL_TA_CSURFACE_CNT,",
        "    TARGET.SMALL_TA_UNKNOWN_CNT = SOURCE.SMALL_TA_UNKNOWN_CNT,",
        "    TARGET.MEDIUM_TA_PDI_CNT = SOURCE.MEDIUM_TA_PDI_CNT,",
        "    TARGET.MEDIUM_TA_HEADDING_CNT = SOURCE.MEDIUM_TA_HEADDING_CNT,",
        "    TARGET.MEDIUM_TA_CIRBLISC_CNT = SOURCE.MEDIUM_TA_CIRBLISC_CNT,",
        "    TARGET.MEDIUM_TA_IRRPMR_CNT = SOURCE.MEDIUM_TA_IRRPMR_CNT,",
        "    TARGET.MEDIUM_TA_CIRBLISRP_CNT = SOURCE.MEDIUM_TA_CIRBLISRP_CNT,",
        "    TARGET.MEDIUM_TA_CSURFACE_CNT = SOURCE.MEDIUM_TA_CSURFACE_CNT,",
        "    TARGET.MEDIUM_TA_UNKNOWN_CNT = SOURCE.MEDIUM_TA_UNKNOWN_CNT,",
        "    TARGET.LARGE_TA_PDI_CNT = SOURCE.LARGE_TA_PDI_CNT,",
        "    TARGET.LARGE_TA_HEADDING_CNT = SOURCE.LARGE_TA_HEADDING_CNT,",
        "    TARGET.LARGE_TA_CIRBLISC_CNT = SOURCE.LARGE_TA_CIRBLISC_CNT,",
        "    TARGET.LARGE_TA_IRRPMR_CNT = SOURCE.LARGE_TA_IRRPMR_CNT,",
        "    TARGET.LARGE_TA_CIRBLISRP_CNT = SOURCE.LARGE_TA_CIRBLISRP_CNT,",
        "    TARGET.LARGE_TA_CSURFACE_CNT = SOURCE.LARGE_TA_CSURFACE_CNT,",
        "    TARGET.LARGE_TA_UNKNOWN_CNT = SOURCE.LARGE_TA_UNKNOWN_CNT,",
        "    TARGET.CARBON_VOID_CNT = SOURCE.CARBON_VOID_CNT,",
        "    TARGET.LDD_CNT = SOURCE.LDD_CNT,",
        "    TARGET.AVG_RMS_DELTA_MV = SOURCE.AVG_RMS_DELTA_MV,",
        "    TARGET.PATCH_TA_CNT = SOURCE.PATCH_TA_CNT,",
        "    TARGET.PATCH_VOID_CNT = SOURCE.PATCH_VOID_CNT,",
        "    TARGET.PATCH_CONTAM_CNT = SOURCE.PATCH_CONTAM_CNT,",
        "    TARGET.NCORR_CNT = SOURCE.NCORR_CNT,",
        "    TARGET.CORR_CNT = SOURCE.CORR_CNT",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    MEAS_TIME,",
        "    SPINDLE,",
        "    CHANNEL_ID,",
        "    HEAD_ID,",
        "    TOTAL_TA_CNT,",
        "    SMALL_TA_CNT,",
        "    MEDIUM_TA_CNT,",
        "    LARGE_TA_CNT,",
        "    MONSTER_TA_CNT,",
        "    POSITIVE_TA_CNT,",
        "    AVG_CLUSTER_MAX_ABS_AMP,",
        "    TOTAL_CLUSTER_CNT,",
        "    QUALIFIED_CLUSTER_CNT,",
        "    TOTAL_CDP_CNT,",
        "    TOTAL_VOID_CNT,",
        "    SMALL_VOID_CNT,",
        "    MEDIUM_VOID_CNT,",
        "    LARGE_VOID_CNT,",
        "    LMS_CNT,",
        "    CSCRATCH_CNT,",
        "    PATCH_CNT,",
        "    TA_MAX_RAD,",
        "    TA_MIN_RAD,",
        "    TA_MAX_AREA,",
        "    SMALL_CLUSTER_CNT,",
        "    MEDIUM_CLUSTER_CNT,",
        "    LARGE_CLUSTER_CNT,",
        "    TOTAL_CONTAM_CNT,",
        "    SCRATCH_CNT,",
        "    CORRUPT_FLAG,",
        "    TOTAL_TA_CNT_BIN1,",
        "    TOTAL_TA_CNT_BIN2,",
        "    TOTAL_TA_CNT_BIN3,",
        "    TOTAL_TA_CNT_BIN4,",
        "    TOTAL_TA_CNT_BIN5,",
        "    TOTAL_TA_CNT_BIN6,",
        "    TOTAL_TA_CNT_BIN7,",
        "    TOTAL_TA_CNT_BIN8,",
        "    TOTAL_TA_CNT_BIN9,",
        "    TOTAL_TA_CNT_BIN10,",
        "    TOTAL_TA_CNT_BIN11,",
        "    TOTAL_TA_CNT_BIN12,",
        "    TOTAL_TA_CNT_BIN13,",
        "    TOTAL_TA_CNT_BIN14,",
        "    TOTAL_TA_CNT_BIN15,",
        "    HTA_CNT,",
        "    LTA_CNT,",
        "    WIDETA_CNT,",
        "    LARGE_POS_TA_CNT,",
        "    TOTAL_TA_PDI_CNT,",
        "    TOTAL_TA_HEADDING_CNT,",
        "    TOTAL_TA_CIRBLISC_CNT,",
        "    TOTAL_TA_IRRPMR_CNT,",
        "    TOTAL_TA_CIRBLISRP_CNT,",
        "    TOTAL_TA_CSURFACE_CNT,",
        "    TOTAL_TA_UNKNOWN_CNT,",
        "    SMALL_TA_PDI_CNT,",
        "    SMALL_TA_HEADDING_CNT,",
        "    SMALL_TA_CIRBLISC_CNT,",
        "    SMALL_TA_IRRPMR_CNT,",
        "    SMALL_TA_CIRBLISRP_CNT,",
        "    SMALL_TA_CSURFACE_CNT,",
        "    SMALL_TA_UNKNOWN_CNT,",
        "    MEDIUM_TA_PDI_CNT,",
        "    MEDIUM_TA_HEADDING_CNT,",
        "    MEDIUM_TA_CIRBLISC_CNT,",
        "    MEDIUM_TA_IRRPMR_CNT,",
        "    MEDIUM_TA_CIRBLISRP_CNT,",
        "    MEDIUM_TA_CSURFACE_CNT,",
        "    MEDIUM_TA_UNKNOWN_CNT,",
        "    LARGE_TA_PDI_CNT,",
        "    LARGE_TA_HEADDING_CNT,",
        "    LARGE_TA_CIRBLISC_CNT,",
        "    LARGE_TA_IRRPMR_CNT,",
        "    LARGE_TA_CIRBLISRP_CNT,",
        "    LARGE_TA_CSURFACE_CNT,",
        "    LARGE_TA_UNKNOWN_CNT,",
        "    CARBON_VOID_CNT,",
        "    LDD_CNT,",
        "    AVG_RMS_DELTA_MV,",
        "    PATCH_TA_CNT,",
        "    PATCH_VOID_CNT,",
        "    PATCH_CONTAM_CNT,",
        "    NCORR_CNT,",
        "    CORR_CNT",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.MEAS_TIME,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CHANNEL_ID,",
        "    SOURCE.HEAD_ID,",
        "    SOURCE.TOTAL_TA_CNT,",
        "    SOURCE.SMALL_TA_CNT,",
        "    SOURCE.MEDIUM_TA_CNT,",
        "    SOURCE.LARGE_TA_CNT,",
        "    SOURCE.MONSTER_TA_CNT,",
        "    SOURCE.POSITIVE_TA_CNT,",
        "    SOURCE.AVG_CLUSTER_MAX_ABS_AMP,",
        "    SOURCE.TOTAL_CLUSTER_CNT,",
        "    SOURCE.QUALIFIED_CLUSTER_CNT,",
        "    SOURCE.TOTAL_CDP_CNT,",
        "    SOURCE.TOTAL_VOID_CNT,",
        "    SOURCE.SMALL_VOID_CNT,",
        "    SOURCE.MEDIUM_VOID_CNT,",
        "    SOURCE.LARGE_VOID_CNT,",
        "    SOURCE.LMS_CNT,",
        "    SOURCE.CSCRATCH_CNT,",
        "    SOURCE.PATCH_CNT,",
        "    SOURCE.TA_MAX_RAD,",
        "    SOURCE.TA_MIN_RAD,",
        "    SOURCE.TA_MAX_AREA,",
        "    SOURCE.SMALL_CLUSTER_CNT,",
        "    SOURCE.MEDIUM_CLUSTER_CNT,",
        "    SOURCE.LARGE_CLUSTER_CNT,",
        "    SOURCE.TOTAL_CONTAM_CNT,",
        "    SOURCE.SCRATCH_CNT,",
        "    SOURCE.CORRUPT_FLAG,",
        "    SOURCE.TOTAL_TA_CNT_BIN1,",
        "    SOURCE.TOTAL_TA_CNT_BIN2,",
        "    SOURCE.TOTAL_TA_CNT_BIN3,",
        "    SOURCE.TOTAL_TA_CNT_BIN4,",
        "    SOURCE.TOTAL_TA_CNT_BIN5,",
        "    SOURCE.TOTAL_TA_CNT_BIN6,",
        "    SOURCE.TOTAL_TA_CNT_BIN7,",
        "    SOURCE.TOTAL_TA_CNT_BIN8,",
        "    SOURCE.TOTAL_TA_CNT_BIN9,",
        "    SOURCE.TOTAL_TA_CNT_BIN10,",
        "    SOURCE.TOTAL_TA_CNT_BIN11,",
        "    SOURCE.TOTAL_TA_CNT_BIN12,",
        "    SOURCE.TOTAL_TA_CNT_BIN13,",
        "    SOURCE.TOTAL_TA_CNT_BIN14,",
        "    SOURCE.TOTAL_TA_CNT_BIN15,",
        "    SOURCE.HTA_CNT,",
        "    SOURCE.LTA_CNT,",
        "    SOURCE.WIDETA_CNT,",
        "    SOURCE.LARGE_POS_TA_CNT,",
        "    SOURCE.TOTAL_TA_PDI_CNT,",
        "    SOURCE.TOTAL_TA_HEADDING_CNT,",
        "    SOURCE.TOTAL_TA_CIRBLISC_CNT,",
        "    SOURCE.TOTAL_TA_IRRPMR_CNT,",
        "    SOURCE.TOTAL_TA_CIRBLISRP_CNT,",
        "    SOURCE.TOTAL_TA_CSURFACE_CNT,",
        "    SOURCE.TOTAL_TA_UNKNOWN_CNT,",
        "    SOURCE.SMALL_TA_PDI_CNT,",
        "    SOURCE.SMALL_TA_HEADDING_CNT,",
        "    SOURCE.SMALL_TA_CIRBLISC_CNT,",
        "    SOURCE.SMALL_TA_IRRPMR_CNT,",
        "    SOURCE.SMALL_TA_CIRBLISRP_CNT,",
        "    SOURCE.SMALL_TA_CSURFACE_CNT,",
        "    SOURCE.SMALL_TA_UNKNOWN_CNT,",
        "    SOURCE.MEDIUM_TA_PDI_CNT,",
        "    SOURCE.MEDIUM_TA_HEADDING_CNT,",
        "    SOURCE.MEDIUM_TA_CIRBLISC_CNT,",
        "    SOURCE.MEDIUM_TA_IRRPMR_CNT,",
        "    SOURCE.MEDIUM_TA_CIRBLISRP_CNT,",
        "    SOURCE.MEDIUM_TA_CSURFACE_CNT,",
        "    SOURCE.MEDIUM_TA_UNKNOWN_CNT,",
        "    SOURCE.LARGE_TA_PDI_CNT,",
        "    SOURCE.LARGE_TA_HEADDING_CNT,",
        "    SOURCE.LARGE_TA_CIRBLISC_CNT,",
        "    SOURCE.LARGE_TA_IRRPMR_CNT,",
        "    SOURCE.LARGE_TA_CIRBLISRP_CNT,",
        "    SOURCE.LARGE_TA_CSURFACE_CNT,",
        "    SOURCE.LARGE_TA_UNKNOWN_CNT,",
        "    SOURCE.CARBON_VOID_CNT,",
        "    SOURCE.LDD_CNT,",
        "    SOURCE.AVG_RMS_DELTA_MV,",
        "    SOURCE.PATCH_TA_CNT,",
        "    SOURCE.PATCH_VOID_CNT,",
        "    SOURCE.PATCH_CONTAM_CNT,",
        "    SOURCE.NCORR_CNT,",
        "    SOURCE.CORR_CNT",
        "  )"
    })
    void insertOrUpdate(TDSCHANNEL record);
}











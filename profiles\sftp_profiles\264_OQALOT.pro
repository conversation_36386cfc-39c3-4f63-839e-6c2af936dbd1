*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |         |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  CASSETTE              |  Char        |  11     |  System.String         |  11     |  2      |         |  0      |  2      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |  Y      |  0      |  4      |  
|  PART_NUMBER           |  Char        |  18     |  System.String         |  18     |  5      |         |  0      |  5      |  
|  LOT_SEQNUM            |  Integer     |  4      |  System.Int32          |  4      |  6      |         |  0      |  6      |  
|  BAG_SEQNUM            |  SmallInt    |  2      |  System.Int16          |  2      |  7      |         |  0      |  7      |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  8      |         |  0      |  8      |  

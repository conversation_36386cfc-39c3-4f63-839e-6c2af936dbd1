package wdc.disk.ecs.apps.SFTPSend.thread;

import java.io.File;
import java.io.FileFilter;
import java.io.FilenameFilter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Vector;

import javax.swing.plaf.basic.BasicInternalFrameTitlePane.IconifyAction;

import ibm.disk.cfgreader.ConfigReader;
import ibm.disk.cfgreader.ParameterRecord;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.SFTPSendApp;
import wdc.disk.ecs.apps.SFTPSend.SFTPSendEcsApp;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;
import wdc.disk.ecs.apps.SFTPSend.util.SendMail;
import wdc.disk.ecs.apps.SFTPSend.util.SendMailForTalendWarning;

/**
 * If padFileDir is pending too many files to process and 
 * the last modify time of sentFileDir remains the same, 
 * then will restart SFTP thread. 
 * <AUTHOR>
 *
 */
public class MonitorSftpSendRunnable implements Runnable{
	public int unprocessLimit = 1;
	public boolean isUnprocessPdsQtyExceeded = false;
	public boolean isTimeout = false;
	public boolean isSendingTimeout = false;
	public String pdsFileDirString;
	public String sentDirString ;
	public int timeoutSeconds;
	public int monitorInterval;
	public LogStream log;
	
	public String profileDirString;
	private SFTPSendEcsApp ecsApp;
	private SFTPSendApp sendApp;
	private String sendingDirString;
	private int sendingTimeout;
	
	public MonitorSftpSendRunnable() {
		super();
	}
	
	public MonitorSftpSendRunnable(String profileDirString) {
		super();
		this.profileDirString = profileDirString;
	}
	

	public MonitorSftpSendRunnable(String profileDirString, SFTPSendApp sendApp) {
		super();
		this.profileDirString = profileDirString;
		this.sendApp = sendApp;
	}
	
	public MonitorSftpSendRunnable(String profileDirString, SFTPSendEcsApp ecsApp) {
		super();
		this.profileDirString = profileDirString;
		this.ecsApp = ecsApp;
		this.sendApp = ecsApp.getSendApp();
	}

	public void init() {
		System.out.println("Init for monitor Sftp send.....");
		readProfile(profileDirString, "MonitorSftpAlive");
		int sleepTime = monitorInterval * 1000;
		try {
			Thread.sleep(sleepTime);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		log.write(LogStream.INFORMATION_MESSAGE, "MonitorSftpSend Thread start to sleep " + monitorInterval + " seconds for first run.");
	}

	@Override
	public void run() {
		init();
		
		//Set condition
		while(true) {
			try {
				int sleepTime = monitorInterval * 1000;
				Thread.sleep(sleepTime);
				showAtConsoleAndLog("MonitorSftpSend Thread start to sleep." + monitorInterval + " seconds.");
				
				long lastModifyTime = getDirLastModifyTime(sentDirString);
				isTimeout =  isTimeout(timeoutSeconds, lastModifyTime);
				String isTimeOutMsg = "isTimeout:" + isTimeout + " Sent time:" + new Date(lastModifyTime).toString() + " current:" + new Date().toString();
				showAtConsoleAndLog(isTimeOutMsg);		
				
				int fileQtyOfMonitorFolder = getMonitorFolderFileQty(pdsFileDirString);
				isUnprocessPdsQtyExceeded = isUnprocessPdsQtyExceeded(unprocessLimit, fileQtyOfMonitorFolder);
				String isUnprocessPdsQtyExceededStr = "isUnprocessPdsQtyExceeded:" + isUnprocessPdsQtyExceeded + ".Monitor file qty:" + fileQtyOfMonitorFolder + " control limit:" + unprocessLimit;
				showAtConsoleAndLog(isUnprocessPdsQtyExceededStr);
				
				//Monitor sending dir time out
				String sendingTimeoutStr = "";
				File sendingDir = new File(this.sendingDirString);
				if(!sendingDir.exists()) {
					throw new SFTPSendException("No sending Dir.Dir:" + this.sendingDirString);
				}
				File oldestFile = MonitorRunnableUtil.findOldestLastModifyFile(sendingDir);
				if(oldestFile != null) {
					long oldestLastModified = oldestFile.lastModified();
					showAtConsoleAndLog("Oldest:" + oldestFile.getAbsolutePath() + " .Time:" + new Date(oldestLastModified));
					if(oldestLastModified == 0) {
						isSendingTimeout = false;
					}else {
						isSendingTimeout = MonitorRunnableUtil.isTimeout(this.sendingTimeout, oldestLastModified);						
					}
					sendingTimeoutStr = "isSendingTimeout:" + isSendingTimeout + " File time:" + new Date(oldestLastModified).toString() + " current:" + new Date().toString();
					showAtConsoleAndLog(sendingTimeoutStr);									
				}else {
					showAtConsoleAndLog("Can't find oldest file. Might have been processed.");
					this.isSendingTimeout = false;
				}
				
				//Decide restart or not
				boolean needToCreateNewThread = needToCreateNewThread(isTimeout, isUnprocessPdsQtyExceeded, isSendingTimeout);
				showAtConsoleAndLog("MonitorSftpSend needToCreateNewThread:" + needToCreateNewThread);
				if(needToCreateNewThread) {
					revokeThread();
					SendMailForTalendWarning sendMail = new SendMailForTalendWarning(profileDirString);
					String body = sendMail.generateBodyForECS(isTimeOutMsg, isUnprocessPdsQtyExceededStr, sendingTimeoutStr);
					sendMail.sendWarningMail(body);
				}
			} catch (Exception e) {
				e.printStackTrace();
				log.writeExceptionStack(e);
			}
		}
	
	}

	public long getDirLastModifyTime(String sentDirString) throws SFTPSendException {
		File file = new File(sentDirString);
		if(!file.exists()) {
			throw new SFTPSendException(sentDirString + " is not exist.");
		}
		
		File resultFile = null;
		if(!file.isDirectory()) {
			throw new SFTPSendException("getFileDate(): is not a directory." + sentDirString );
		}else {
			resultFile = findLastestLastModifyFile(file);
		}
		if(resultFile != null) {
			return resultFile.lastModified();
		}else {
			return file.lastModified();
		}
	}

	private File findLastestLastModifyFile(File dir) {
		File resultFile = null;
		if(dir.isDirectory()) {
			long highest = 0;
			File[] listFiles = dir.listFiles();
			for(int i=0; i < listFiles.length; i++) {
//				System.out.println("i:" + i + " file:" + listFiles[i].getName() + " last:" + listFiles[i].lastModified());
				long fileLastModify = listFiles[i].lastModified();
				if(highest < fileLastModify){
					highest = fileLastModify;
					resultFile = listFiles[i];
				}
			}
		}
//		if(resultFile != null) {
//			log.write(LogStream.INFORMATION_MESSAGE,"Result file:" + resultFile.getAbsolutePath() );			
//		}
		return resultFile;
	}

	/**
	 * Scan monitor folder and compare with the unprocess limit.
	 * Since the list number may be less than 996.So try not to set over it.
	 * Return true: file quantity exceed the control limit
	 * @param unprocessLimit
	 * @param monitorFolderNameString
	 * @return
	 * @throws SFTPSendException
	 */
	public boolean isUnprocessPdsQtyExceeded(int unprocessLimit, int dirQty) throws SFTPSendException {
		if(dirQty <= unprocessLimit) {
			return false;
		}else {
			return true;
		}
	}

	private int getMonitorFolderFileQty(String monitorFolderNameString) throws SFTPSendException {
		File monitorFolder = new File(monitorFolderNameString);
		if(!monitorFolder.isDirectory()) {
			String warningString = "isUnprocessPdsQtyExceeded.Not a folder:" + monitorFolderNameString;
			showAtConsoleAndLog("isUnprocessPdsQtyExceeded.Not a folder:" + monitorFolderNameString);
			throw new SFTPSendException(warningString);
		}
		
		FilenameFilter filenameFilter = new FilenameFilter() {
			
			@Override
			public boolean accept(File dir, String name) {
				if("problem".equals(name) || "sending".equals(name) || "sent".equals(name) || "srcPds".equals(name)){
					return false;					
				}else {
					return true;
				}
			}
		};
		
		String[] list = monitorFolder.list(filenameFilter);
		int fileQtyOfFolder = list.length;
		return fileQtyOfFolder;
	}

	/*
	 * Returns if isTimeOut
	 * Return true :   lastModifyTime + seconds < now
	 */
	public boolean isTimeout(int seconds,long lastModifyTime) {
		Date nowDate = new Date();
		long now = nowDate.getTime();
		
		if(lastModifyTime + seconds * 1000 < now) {
			return true;
		}else {
			return false;
		}
	}

	public boolean needToCreateNewThread(boolean isTimeout, boolean isUnprocessPdsQtyExceeded, boolean isSendingTimeout) {
		showAtConsoleAndLog("isTimeOut:" + isTimeout);
		showAtConsoleAndLog("isUnprocessPdsQtyExceeded: " + isUnprocessPdsQtyExceeded );
		showAtConsoleAndLog("isSendingTimeout: " + isSendingTimeout);

		return (isTimeout && isUnprocessPdsQtyExceeded) || isSendingTimeout; 
	}
	
	
    public void revokeThread(){
    	String blockName = "SFTPSend";
        log.write(LogStream.INFORMATION_MESSAGE, "Trying to stop SFTP Send.");
        sendApp.exitApplication();
        
        
        log.write(LogStream.INFORMATION_MESSAGE, "Give sendApp 10s to exit.");
        try {
			Thread.sleep(10*1000);
		} catch (InterruptedException e1) {
			log.writeExceptionStack(e1);
		}
        
        log.write(LogStream.INFORMATION_MESSAGE, "Trying to revoke SFTP Send.");
        sendApp = new SFTPSendApp();
        try {
            sendApp.initialize(profileDirString,blockName);

            if(ecsApp != null) {
            	//fire property change to View 
            	log.write("Set sendApp to ecsApp.");
            	ecsApp.setSendApp(sendApp);
            }
            
            log.write(LogStream.INFORMATION_MESSAGE, "Revoked SFTP Send.");
        } catch (SFTPSendException e) {
        	log.write(LogStream.INFORMATION_MESSAGE, "SFTP SendException in revoking.");
        	log.writeExceptionStack(e);
            return;       
        }
    }
	
	
	public boolean readProfile(String profile, String blockname) {
		System.out.println("MonitorSftpSendThread trying to read config file:" + profile);
		ConfigReader cr = null;
		try {
			cr = new ConfigReader(profile);
		} catch (Exception e) {
			System.out.println("Read config failed.");
			return false;
		}
		
		// Get the general configurations for SFTPSend
		cr.startGroup(blockname);
		Vector mqAttrib = null;
		if (cr.hasMoreGroups()) {
			mqAttrib = cr.nextGroup();
			for (int i = 0; i < mqAttrib.size();i++) {
				ParameterRecord rec = (ParameterRecord)mqAttrib.get(i);
				if (rec.getKey().equals ("FILE_COUNT_LIMIT")) {
					try {
						unprocessLimit = Integer.parseInt(rec.getValue().trim());
					} catch (Exception e) {}   // defaults to log level information
				}else if (rec.getKey().equals ("TIMEOUT")) {
					String timeout = rec.getValue();
					if (timeout != null) {
						timeoutSeconds = Integer.parseInt(rec.getValue().trim());
					}
				}else if (rec.getKey().equals ("PDS_DIR")) {
					String pdsDirString = rec.getValue().trim();
					if(pdsDirString != null && !"".equals(pdsDirString)) {
						pdsFileDirString = pdsDirString;						
					}
				}else if (rec.getKey().equals ("SENT_DIR")) {
					String sentDirString = rec.getValue().trim();
					if(sentDirString != null && !"".equals(sentDirString)) {
						this.sentDirString = sentDirString;						
					}
				}else if (rec.getKey().equals ("MONITOR_INTERVAL")) {
					String intervalString = rec.getValue();
					if (intervalString != null) {
						this.monitorInterval = Integer.parseInt(intervalString.trim());
					}
				}else if (rec.getKey().equals ("LOG_FILE")) {
					String logFile = rec.getValue();
					if (logFile != null) {
						this.log = new LogStream(logFile.trim());
					}
				}else if (rec.getKey().equals ("SENDING_DIR")) {
					String sendingDirString = rec.getValue().trim();
					if(sendingDirString != null && !"".equals(sendingDirString)) {
						this.sendingDirString = sendingDirString;						
					}
				}else if (rec.getKey().equals ("SENDING_TIMEOUT")) {
					String sendingTimeout = rec.getValue();
					if (sendingTimeout != null) {
						this.sendingTimeout = Integer.parseInt(sendingTimeout.trim());
					}
				}
			}
		}
		return(true);
	}

	public int getUnprocessLimit() {
		return unprocessLimit;
	}

	public void setUnprocessLimit(int unprocessLimit) {
		this.unprocessLimit = unprocessLimit;
	}

	public String getPdsFileDirString() {
		return pdsFileDirString;
	}

	public void setPdsFileDirString(String pdsFileDirString) {
		this.pdsFileDirString = pdsFileDirString;
	}

	public String getSentDirString() {
		return sentDirString;
	}

	public void setSentDirString(String sentDirString) {
		this.sentDirString = sentDirString;
	}

	public int getTimeoutSeconds() {
		return timeoutSeconds;
	}

	public void setTimeoutSeconds(int timeoutSeconds) {
		this.timeoutSeconds = timeoutSeconds;
	}

	public int getMonitorInterval() {
		return monitorInterval;
	}

	public void setMonitorInterval(int monitorInterval) {
		this.monitorInterval = monitorInterval;
	}
	
	public void showAtConsoleAndLog(String msg) {
		System.out.println(msg);
		log.write(LogStream.INFORMATION_MESSAGE, msg);
	}
	
	
}



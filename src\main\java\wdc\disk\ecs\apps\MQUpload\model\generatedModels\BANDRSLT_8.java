package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for BANDRSLT_8
 */
public class BANDRSLT_8  {
    private Short spindle;
    private Integer testnum;
    private Short bandnum;
    private Short reqopernum;
    private Double topresult;
    private Double bottomresult;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public BANDRSLT_8() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Short getBandnum() {
        return bandnum;
    }

    public void setBandnum(Short bandnum) {
        this.bandnum = bandnum;
    }

    public Short getReqopernum() {
        return reqopernum;
    }

    public void setReqopernum(Short reqopernum) {
        this.reqopernum = reqopernum;
    }

    public Double getTopresult() {
        return topresult;
    }

    public void setTopresult(Double topresult) {
        this.topresult = topresult;
    }

    public Double getBottomresult() {
        return bottomresult;
    }

    public void setBottomresult(Double bottomresult) {
        this.bottomresult = bottomresult;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



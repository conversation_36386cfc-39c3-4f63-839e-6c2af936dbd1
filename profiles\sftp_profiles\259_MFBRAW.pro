*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  KEY                   |  BigInt      |  8      |  System.Int64          |  8      |  3      |         |  0      |  3      |  
|  DISKSEQ               |  SmallInt    |  2      |  System.Int16          |  2      |  4      |         |  0      |  4      |  
|  FRONTFORCER           |  Decimal     |  9      |  System.Decimal        |  9      |  5      |         |  0      |  5      |  
|  FRONTTENSIONR         |  Decimal     |  9      |  System.Decimal        |  9      |  6      |         |  0      |  6      |  
|  BACKFORCER            |  Decimal     |  9      |  System.Decimal        |  9      |  7      |         |  0      |  7      |  
|  BACKTENSIONR          |  Decimal     |  9      |  System.Decimal        |  9      |  8      |         |  0      |  8      |  
|  FRONTSTEPSR           |  SmallInt    |  2      |  System.Int16          |  2      |  9      |         |  0      |  9      |  
|  FRONTTAPEPOSITIONR    |  Decimal     |  9      |  System.Decimal        |  9      |  10     |         |  0      |  10     |  
|  FRONTTAPESCALEFACTORR  |  Integer     |  4      |  System.Int32          |  4      |  11     |         |  0      |  11     |  
|  BACKSTEPSR            |  Decimal     |  9      |  System.Decimal        |  9      |  12     |         |  0      |  12     |  
|  BACKTAPEPOSITIONR     |  Decimal     |  9      |  System.Decimal        |  9      |  13     |         |  0      |  13     |  
|  BACKTAPESCALEFACTORR  |  Integer     |  4      |  System.Int32          |  4      |  14     |         |  0      |  14     |  
|  FRONTFORCEL           |  Decimal     |  9      |  System.Decimal        |  9      |  15     |         |  0      |  15     |  
|  FRONTTENSIONL         |  Decimal     |  9      |  System.Decimal        |  9      |  16     |         |  0      |  16     |  
|  BACKFORCEL            |  Decimal     |  9      |  System.Decimal        |  9      |  17     |         |  0      |  17     |  
|  BACKTENSIONL          |  Decimal     |  9      |  System.Decimal        |  9      |  18     |         |  0      |  18     |  
|  FRONTSTEPSL           |  Decimal     |  9      |  System.Decimal        |  9      |  19     |         |  0      |  19     |  
|  FRONTTAPEPOSITIONL    |  Decimal     |  9      |  System.Decimal        |  9      |  20     |         |  0      |  20     |  
|  FRONTTAPESCALEFACTORL  |  Integer     |  4      |  System.Int32          |  4      |  21     |         |  0      |  21     |  
|  BACKSTEPSL            |  Integer     |  4      |  System.Int32          |  4      |  22     |         |  0      |  22     |  
|  BACKTAPEPOSITIONL     |  Decimal     |  9      |  System.Decimal        |  9      |  23     |         |  0      |  23     |  
|  BACKTAPESCALEFACTORL  |  Decimal     |  9      |  System.Decimal        |  9      |  24     |         |  0      |  24     |  
|  SPINDLESPEED          |  Decimal     |  7      |  System.Decimal        |  7      |  25     |         |  0      |  25     |  
|  RIGHTPOS              |  Decimal     |  8      |  System.Decimal        |  8      |  26     |         |  0      |  26     |  
|  LEFTPOS               |  Decimal     |  9      |  System.Decimal        |  9      |  27     |         |  0      |  27     |  
|  BULKERASEPOS          |  Decimal     |  9      |  System.Decimal        |  9      |  28     |         |  0      |  28     |  

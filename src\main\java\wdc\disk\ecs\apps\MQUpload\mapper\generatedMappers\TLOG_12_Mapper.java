package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.TLOG_12;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for TLOG_12
 */
public interface TLOG_12_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.TLOG AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{errortype},",
        "    #{errorstring},",
        "    #{testertime},",
        "    #{status},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    ERRORTYPE,",
        "    ERRORSTRING,",
        "    TESTERTIME,",
        "    STATUS,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.DTINSERT = SOURCE.DTINSERT",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.ERRORTYPE = SOURCE.ERRORTYPE,",
        "    TARGET.ERRORSTRING = SOURCE.ERRORSTRING,",
        "    TARGET.TESTERTIME = SOURCE.TESTERTIME,",
        "    TARGET.STATUS = SOURCE.STATUS,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    ERRORTYPE,",
        "    ERRORSTRING,",
        "    TESTERTIME,",
        "    STATUS,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.ERRORTYPE,",
        "    SOURCE.ERRORSTRING,",
        "    SOURCE.TESTERTIME,",
        "    SOURCE.STATUS,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(TLOG_12 record);
}











*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  HFS_MODULE_ALARM      |  Double      |  8      |  System.Double         |  8      |  3      |         |  0      |  3      |  
|  BRUSH_DI_FLOW         |  Double      |  8      |  System.Double         |  8      |  4      |         |  0      |  4      |  
|  HFS_CYCLE_TIME        |  Double      |  8      |  System.Double         |  8      |  5      |         |  0      |  5      |  
|  OUT_TOTAL_TIME        |  Double      |  8      |  System.Double         |  8      |  6      |         |  0      |  6      |  
|  OUT_DEPTH             |  Double      |  8      |  System.Double         |  8      |  7      |         |  0      |  7      |  
|  BRUSH_SPEED_L         |  Double      |  8      |  System.Double         |  8      |  8      |         |  0      |  8      |  
|  BRUSH_SPEED_T         |  Double      |  8      |  System.Double         |  8      |  9      |         |  0      |  9      |  
|  TOTAL_CHEM_TIME       |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  

package wdc.disk.ecs.apps.SFTPSend.properties;

import java.util.StringTokenizer;
import ibm.disk.profiles.ProfileException;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;

/**
 * Properties of SFTP server configured in the block SFTPSend of ecs.ini:
 * PRIMARY_SFTP_SEVER=hostname:port:username:password, // primary SFTP server
 * BACKUP_SFTP_SEVER=hostname:port:username:password,  // backup SFTP server, only when primary cannot be connected
 * @Date 2021-08-09 10:23:56
 * <AUTHOR>
 */
public class SFTPServerProperties {
	private String hostname;
	private int port;
	private String username;
	private String password;
	
	/**
	 * @Date 2021-08-11 10:30:13
	 */
	public SFTPServerProperties() {
		super();
	}
	
	/**
	 * value=hostname:port:username:password
	 * @Date 2021-08-11 10:31:40
	 * @param value
	 * @throws ProfileException 
	 */
	public SFTPServerProperties(String value) throws ProfileException {
		super();
		StringTokenizer st = new StringTokenizer(value.trim(), ":");
		
		int EXPECTED_TOKENS = 4;
		if (st.countTokens() != EXPECTED_TOKENS) {
			throw (new ProfileException("Invalid number of values: Found " + st.countTokens() + ", must be " + EXPECTED_TOKENS));
		}
		
		String fieldName = null, fieldValue = null;
		try {
			fieldName = "hostname";
			fieldValue = "";
			setHostname(st.nextToken().trim());
			fieldName = "port";
			fieldValue = st.nextToken().trim();
			setPort(Integer.parseInt(fieldValue));
			fieldName = "username";
			fieldValue = "";
			setUsername(st.nextToken().trim());
			fieldName = "password";
			fieldValue = "";
			setPassword(st.nextToken().trim());
		} catch (Exception e) {
			throw (new ProfileException("Invalid field: " + fieldName + "=" + fieldValue));
		}
		
	}

	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @return String
	 */
	public String getHostname() {
		return hostname;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @param hostname String
	 */
	public void setHostname(String hostname) {
		this.hostname = hostname;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @return int
	 */
	public int getPort() {
		return port;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @param port int
	 */
	public void setPort(int port) {
		this.port = port;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @return String
	 */
	public String getUsername() {
		return username;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @param username String
	 */
	public void setUsername(String username) {
		this.username = username;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @return String
	 */
	public String getPassword() {
		return password;
	}
	
	/**
	 * Creation date: 2021-08-09 10:30:50
	 * @param password String
	 */
	public void setPassword(String password) {
		this.password = password;
	}

	/* (non-Javadoc)
	 * Creation date: 2021-08-12 16:22:58
	 * @see java.lang.Object#toString()
	 */
	@Override
	public String toString() {
		return hostname + ":" + port;
	}
}

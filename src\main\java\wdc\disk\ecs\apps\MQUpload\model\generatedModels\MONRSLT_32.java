package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for MONRSLT_32
 */
public class MONRSLT_32  {
    private Short spindle;
    private String product;
    private Integer diskcode;
    private Integer disksequence;
    private String side;
    private Integer testnum;
    private Short magrule;
    private String magheadid;
    private java.time.LocalDateTime batchtime;
    private Short dataindex;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;
    private Integer failcode;

    // Default constructor
    public MONRSLT_32() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public String getProduct() {
        return product;
    }

    public void setProduct(String product) {
        this.product = product;
    }

    public Integer getDiskcode() {
        return diskcode;
    }

    public void setDiskcode(Integer diskcode) {
        this.diskcode = diskcode;
    }

    public Integer getDisksequence() {
        return disksequence;
    }

    public void setDisksequence(Integer disksequence) {
        this.disksequence = disksequence;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Short getMagrule() {
        return magrule;
    }

    public void setMagrule(Short magrule) {
        this.magrule = magrule;
    }

    public String getMagheadid() {
        return magheadid;
    }

    public void setMagheadid(String magheadid) {
        this.magheadid = magheadid;
    }

    public java.time.LocalDateTime getBatchtime() {
        return batchtime;
    }

    public void setBatchtime(java.time.LocalDateTime batchtime) {
        this.batchtime = batchtime;
    }

    public Short getDataindex() {
        return dataindex;
    }

    public void setDataindex(Short dataindex) {
        this.dataindex = dataindex;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

    public Integer getFailcode() {
        return failcode;
    }

    public void setFailcode(Integer failcode) {
        this.failcode = failcode;
    }

}



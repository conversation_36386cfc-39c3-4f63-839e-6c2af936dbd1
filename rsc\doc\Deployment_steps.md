# Folder Structure

## Config Folders
Contains configuration files for the application:

* **MQ_Upload.yml** - Main configuration file containing producer, consumer and RabbitMQ settings including directory paths, connection details, and queue configurations

* **message_mappings.json** - Maps message types to their corresponding database tables and defines field mappings for data transformation

* **mybatis-config.xml** - MyBatis configuration file that defines database connection settings and SQL mappings for database operations

* **test-queue-config.csv** - Contains queue routing configurations including message types, routing keys and queue names for message routing
## Profiles Folder
The profiles folder contains numerous .pro files that define the structure of data for different message types. 

## Target Folders
Contains compiled application JAR files.

# Deployment Steps

1. **Environment requirement:** JDK 1.8 or above
2. **Package directory:** \\\\csf-op-genjbx02.wdc.com\Common\CHG0204872_Rabbit MQ Migration for IGC related tables\RabbitMq\MQUpload
2. **Create application folder:** Create a folder for the application (e.g., `/opt/MQUpload` or `D:\RabbitMQ\Consumer`)
3. **Copy required folders:** Copy the following folders to the application folder:
   - **Config:** 
     - MQ_Upload.yml
     - message_mappings.json
     - mybatis-config.xml
     - test-queue-config.csv
   - **Profiles:** All .pro files in the profiles folder
   - **Target:** 
     - SFTPSend-1.0-SNAPSHOT.jar
     - ecs_2000-1.0.jar
     - disk-utility-1.0.jar
4. **Execute application:** 
   - Windows: Run Consumer.bat
   - Linux: Run RabbitMQConsumer.sh
   
   > **Note:** It is highly recommended to run the application as a service or start it as a background process.

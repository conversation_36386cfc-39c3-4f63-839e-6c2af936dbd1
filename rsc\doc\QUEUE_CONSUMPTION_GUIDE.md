# Queue Consumption Guide

This guide explains how to set up and consume content from a RabbitMQ queue in the MQUpload application.

## 1. Queue Configuration

### 1.1 Configure Queue in CSV
First, add your queue configuration in `src\test\resources\test-queue-config.csv`:

```csv
Name,Type,Exchange,ExchangeType,RoutingKey,Durable,Exclusive,AutoDelete,QuorumSize
your_queue,quorum,ecs.direct,direct,your_routing_key,TRUE,FALSE,FALSE,3
```

Parameters explained:
- Name: Unique queue identifier
- Type: 'quorum' or 'classic'
- Exchange: Exchange name (e.g., 'ecs.direct')
- ExchangeType: 'direct', 'topic', or 'fanout'
- RoutingKey: Key for message routing
- Durable: Survives broker restart (TRUE/FALSE)
- QuorumSize: For quorum queues (0 for classic queues)

### 1.2 Add Consumer Configuration
Add your queue configuration in `src\test\resources\MQ_Upload.yml` under the consumer block:

```yaml
consumer:
  mybatis_config_path: "path/to/mybatis-config.xml"
  parser_class_name: "wdc.disk.ecs.apps.MQUpload.parser.AsciiMessageParser"
  mappings_path: "mapper/message_mappings.json"
  profiles_path: "profiles"
  queues:
    - name: "your_queue"
      description: "Description of your queue"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 200
      batch_size: 20
```

## 2. Start the Consumer

### 2.1 Using Shell Script
Run the consumer using the provided shell script:
```bash
./scripts/RabbitMQConsumer.sh
```

### 2.2 Manual Start
Or start manually with Java:
```bash
java -Dconfig.block=MQUpload_test -Drabbitmq.block=RabbitMQ_Configurations -Dconsumer.block=Consumer_test \
     -cp "${CLASSPATH}" wdc.disk.ecs.apps.MQUpload.consumer.RabbitMQConsumer "${CONFIG_PROFILE}" "${LOG_FILE}"
```





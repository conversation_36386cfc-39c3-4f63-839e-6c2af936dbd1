## issue
when trying to run consumer job for tabcode of BANDRSLT_STRING_27, there's an issue.
there's a set method: setProperty(pojo, "dtinsert", LocalDateTime.now()); which will set the datetime to this column.

### currently throws an exception,below is the stack:
~0 14:43:03 Sep 4, 2025 Message processing failed: Error processing message with tabCode BANDRSLT_STRING_27. Message requeued
~0 14:43:03 Sep 4, 2025 Exception java.lang.RuntimeException occurred, message = Failed to map data to POJO
java.lang.RuntimeException: Failed to map data to POJO
	at wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper.mapToPojo(MessageDataMapper.java:154)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.processDeliveryListOneByOne(BatchMessageProcessor.java:124)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.handleBatchFailure(BatchMessageProcessor.java:414)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.processBatchDeliveriesBulkTransaction(BatchMessageProcessor.java:330)
	at wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor.lambda$startConsuming$0(BatchMessageProcessor.java:496)
	at java.base/java.lang.Thread.run(Thread.java:1575)
Caused by: java.lang.RuntimeException: Failed to set property: dtinsert
	at wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper.setProperty(MessageDataMapper.java:164)
	at wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper.mapToPojo(MessageDataMapper.java:115)
	... 5 more
Caused by: java.lang.IllegalArgumentException: argument type mismatch
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:107)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper.setProperty(MessageDataMapper.java:162)
	... 6 more

## i should tell you, in other message jobs, there's no such issue setting this field.
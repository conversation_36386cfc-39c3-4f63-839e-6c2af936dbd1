package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.GAUGERUN_33;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for GAUGERUN_33
 */
public interface GAUGERUN_33_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.GAUGERUN AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{typenum},",
        "    #{headid},",
        "    #{prodNum},",
        "    #{runTime},",
        "    #{disksequence},",
        "    #{recsent},",
        "    #{testnum},",
        "    #{hcf1f},",
        "    #{hcf2f},",
        "    #{hcf3f},",
        "    #{noise0},",
        "    #{noise1},",
        "    #{noise2},",
        "    #{noise3},",
        "    #{hcfNoise},",
        "    #{hcfSignal},",
        "    #{monStat},",
        "    #{dtinsert},",
        "    #{correlid},",
        "    #{failcode}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    TYPENUM,",
        "    HEADID,",
        "    PROD_NUM,",
        "    RUN_TIME,",
        "    DISKSEQUENCE,",
        "    RECSENT,",
        "    TESTNUM,",
        "    HCF1F,",
        "    HCF2F,",
        "    HCF3F,",
        "    NOISE0,",
        "    NOISE1,",
        "    NOISE2,",
        "    NOISE3,",
        "    HCF_NOISE,",
        "    HCF_SIGNAL,",
        "    MON_STAT,",
        "    DTINSERT,",
        "    CORRELID,",
        "    FAILCODE",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TYPENUM = SOURCE.TYPENUM AND TARGET.HEADID = SOURCE.HEADID AND TARGET.PROD_NUM = SOURCE.PROD_NUM AND TARGET.RUN_TIME = SOURCE.RUN_TIME",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DISKSEQUENCE = SOURCE.DISKSEQUENCE,",
        "    TARGET.RECSENT = SOURCE.RECSENT,",
        "    TARGET.TESTNUM = SOURCE.TESTNUM,",
        "    TARGET.HCF1F = SOURCE.HCF1F,",
        "    TARGET.HCF2F = SOURCE.HCF2F,",
        "    TARGET.HCF3F = SOURCE.HCF3F,",
        "    TARGET.NOISE0 = SOURCE.NOISE0,",
        "    TARGET.NOISE1 = SOURCE.NOISE1,",
        "    TARGET.NOISE2 = SOURCE.NOISE2,",
        "    TARGET.NOISE3 = SOURCE.NOISE3,",
        "    TARGET.HCF_NOISE = SOURCE.HCF_NOISE,",
        "    TARGET.HCF_SIGNAL = SOURCE.HCF_SIGNAL,",
        "    TARGET.MON_STAT = SOURCE.MON_STAT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID,",
        "    TARGET.FAILCODE = SOURCE.FAILCODE",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    TYPENUM,",
        "    HEADID,",
        "    PROD_NUM,",
        "    RUN_TIME,",
        "    DISKSEQUENCE,",
        "    RECSENT,",
        "    TESTNUM,",
        "    HCF1F,",
        "    HCF2F,",
        "    HCF3F,",
        "    NOISE0,",
        "    NOISE1,",
        "    NOISE2,",
        "    NOISE3,",
        "    HCF_NOISE,",
        "    HCF_SIGNAL,",
        "    MON_STAT,",
        "    DTINSERT,",
        "    CORRELID,",
        "    FAILCODE",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TYPENUM,",
        "    SOURCE.HEADID,",
        "    SOURCE.PROD_NUM,",
        "    SOURCE.RUN_TIME,",
        "    SOURCE.DISKSEQUENCE,",
        "    SOURCE.RECSENT,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.HCF1F,",
        "    SOURCE.HCF2F,",
        "    SOURCE.HCF3F,",
        "    SOURCE.NOISE0,",
        "    SOURCE.NOISE1,",
        "    SOURCE.NOISE2,",
        "    SOURCE.NOISE3,",
        "    SOURCE.HCF_NOISE,",
        "    SOURCE.HCF_SIGNAL,",
        "    SOURCE.MON_STAT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.FAILCODE",
        "  )"
    })
    void insertOrUpdate(GAUGERUN_33 record);
}











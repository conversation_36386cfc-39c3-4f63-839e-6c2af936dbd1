package wdc.testBinary;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;

import org.junit.jupiter.api.Test;

import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.ProfileException;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryService;
import wdc.disk.ecs.apps.SFTPSend.binary.SingleMessage;

public class TestSHGMONDATA2 {

	@Test
	public void testSHG() throws ProfileException {
		String rawData = "2d00 0000 0f95 9903 0200 0000 110d 0000 aec7 365f 4600 bc04 4235 3637 3800 0000 0000 0000 6666 6666 6666 1040 5454 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0000 0098 8a40 0000 0000 001e b440 2c00 0000 cdcc cccc cccc 0c40 a5f0 a5f0 0100 5739 3735 3331 3234 0000";
		rawData = rawData.replaceAll(" ","");
		BinaryService bService = new BinaryService();
		System.out.println(bService.generatePdsStringForIndividualMsg(rawData));
		
	}
	
	@Test
	public void testParseFile() throws IOException, ProfileException {
		BinaryService service = new BinaryService();
		String path = getClass().getResource("/210825141748.5804").getPath();
		File file = new java.io.File(path);
		FileInputStream fis = new FileInputStream(file);
		int data;
		StringBuffer sBuffer = new StringBuffer();
		while((data = fis.read()) != -1) {
			sBuffer.append(StringMethods.pad(Integer.toHexString(data), 2, '0', true));
		}
		fis.close();
		ArrayList<SingleMessage> list = service.splitPackedMsgToArrayList(sBuffer.toString());
		for(int i=0; i<list.size(); i++) {
			System.out.println(service.generatePdsStringForSingleMsg(list.get(i)));
		}
	}

//	@Test
//	public void testReadHexFile() throws IOException{
//		String fileName = getClass().getResource("/210825141748.5804").getPath();
//		try (FileInputStream fis = new FileInputStream(fileName)) {
//            // A variable to hold a single byte of the file data
//            int i = 0;
//
//            // A counter to print a new line every 16 bytes read.
//            int count = 0;
//
//            // Read till the end of the file and print the byte in hexadecimal
//            // valueS.
//            while ((i = fis.read()) != -1) {
//                System.out.printf("%02X ", i);
//                count++;
//
//                if (count == 16) {
//                    System.out.println();
//                    count = 0;
//                }
//            }
//        }
//    }
}

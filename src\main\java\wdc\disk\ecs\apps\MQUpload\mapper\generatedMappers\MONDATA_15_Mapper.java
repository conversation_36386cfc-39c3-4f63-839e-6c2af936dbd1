package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.MONDATA_15;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for MONDATA_15
 */
public interface MONDATA_15_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.MONDATA AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{dataindex},",
        "    #{bandnum},",
        "    #{reqopernum},",
        "    #{result},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    DATAINDEX,",
        "    BANDNUM,",
        "    REQOPERNUM,",
        "    RESULT,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.DATAINDEX = SOURCE.DATAINDEX AND TARGET.BANDNUM = SOURCE.BANDNUM AND TARGET.REQOPERNUM = SOURCE.REQOPERNUM",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.RESULT = SOURCE.RESULT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    DATAINDEX,",
        "    BANDNUM,",
        "    REQOPERNUM,",
        "    RESULT,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.DATAINDEX,",
        "    SOURCE.BANDNUM,",
        "    SOURCE.REQOPERNUM,",
        "    SOURCE.RESULT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(MONDATA_15 record);
}











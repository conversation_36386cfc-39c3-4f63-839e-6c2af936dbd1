package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for HEADLOG_10
 */
public class HEADLOG_10  {
    private String channel;
    private Short spindle;
    private String side;
    private Integer testnum;
    private java.time.LocalDateTime insttime;
    private Short typenum;
    private String headid;
    private String reason;
    private java.time.LocalDateTime dtinsert;
    private Integer product;
    private Integer correlid;

    // Default constructor
    public HEADLOG_10() {
    }

    public String getChannel() {
        return channel;
    }

    public void setChannel(String channel) {
        this.channel = channel;
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public String getSide() {
        return side;
    }

    public void setSide(String side) {
        this.side = side;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public java.time.LocalDateTime getInsttime() {
        return insttime;
    }

    public void setInsttime(java.time.LocalDateTime insttime) {
        this.insttime = insttime;
    }

    public Short getTypenum() {
        return typenum;
    }

    public void setTypenum(Short typenum) {
        this.typenum = typenum;
    }

    public String getHeadid() {
        return headid;
    }

    public void setHeadid(String headid) {
        this.headid = headid;
    }

    public String getReason() {
        return reason;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getProduct() {
        return product;
    }

    public void setProduct(Integer product) {
        this.product = product;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



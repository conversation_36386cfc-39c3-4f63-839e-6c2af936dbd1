*| ColumnName            |  DBColumnType|UniqueIndex |  DBColumnLength  |  SystemType      |BinaryLength|  ColumnNo|  NULLS  |StartIndex| ValueIndex|  
|  DTINSERT              |  Timestamp   |            |  4               |  System.DateTime |  4         |  0       |  Y      |  0       |  0        |  
|  DT_ENTRY              |  Timestamp   |    Y       |  4               |  System.DateTime |  4         |  1       |         |  0       |  1        |  
|  CASSETTE              |  Char        |            |  11              |  System.String   |  11        |  2       |         |  0       |  2        |  
|  LOT                   |  Char        |    Y       |  10              |  System.String   |  10        |  3       |         |  0       |  3        |  
|  PRODUCT               |  Char        |            |  6               |  System.String   |  6         |  4       |         |  0       |  4        |  
|  VARIETY               |  Char        |            |  14              |  System.String   |  14        |  5       |         |  0       |  5        |  
|  XID                   |  Char        |            |  10              |  System.String   |  10        |  6       |         |  0       |  6        |  
|  RESOURCE              |  Char        |            |  10              |  System.String   |  10        |  7       |         |  0       |  7        |  
|  UN_LIDDING_TIME       |  Double      |            |  8               |  System.Double   |  8         |  8       |         |  0       |  8        |  
|  BOTTOM_COVER_ASSY_TIME|  Double      |            |  8               |  System.Double   |  8         |  9       |         |  0       |  9        |  
|  LIDDING_TIME          |  Double      |            |  8               |  System.Double   |  8         |  10      |         |  0       |  10       |  
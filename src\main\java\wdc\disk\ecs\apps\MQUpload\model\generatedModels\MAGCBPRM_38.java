package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for MAGCBPRM_38
 */
public class MAGCBPRM_38  {
    private String elemname;
    private Short channel;
    private String half;
    private Double freq;
    private Short gain;
    private Short parmnum;
    private Double slope;
    private Double offset;
    private Double corrcoeff;
    private Double maxdev;
    private Double avgdev;
    private Short spindle;
    private java.time.LocalDateTime calibtime;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public MAGCBPRM_38() {
    }

    public String getElemname() {
        return elemname;
    }

    public void setElemname(String elemname) {
        this.elemname = elemname;
    }

    public Short getChannel() {
        return channel;
    }

    public void setChannel(Short channel) {
        this.channel = channel;
    }

    public String getHalf() {
        return half;
    }

    public void setHalf(String half) {
        this.half = half;
    }

    public Double getFreq() {
        return freq;
    }

    public void setFreq(Double freq) {
        this.freq = freq;
    }

    public Short getGain() {
        return gain;
    }

    public void setGain(Short gain) {
        this.gain = gain;
    }

    public Short getParmnum() {
        return parmnum;
    }

    public void setParmnum(Short parmnum) {
        this.parmnum = parmnum;
    }

    public Double getSlope() {
        return slope;
    }

    public void setSlope(Double slope) {
        this.slope = slope;
    }

    public Double getOffset() {
        return offset;
    }

    public void setOffset(Double offset) {
        this.offset = offset;
    }

    public Double getCorrcoeff() {
        return corrcoeff;
    }

    public void setCorrcoeff(Double corrcoeff) {
        this.corrcoeff = corrcoeff;
    }

    public Double getMaxdev() {
        return maxdev;
    }

    public void setMaxdev(Double maxdev) {
        this.maxdev = maxdev;
    }

    public Double getAvgdev() {
        return avgdev;
    }

    public void setAvgdev(Double avgdev) {
        this.avgdev = avgdev;
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public java.time.LocalDateTime getCalibtime() {
        return calibtime;
    }

    public void setCalibtime(java.time.LocalDateTime calibtime) {
        this.calibtime = calibtime;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



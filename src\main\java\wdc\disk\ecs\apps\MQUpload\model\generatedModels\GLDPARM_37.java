package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for GLDPARM_37
 */
public class GLDPARM_37  {
    private Short gate;
    private String elemname;
    private Short id;
    private String half;
    private Double centerfreq;
    private Short parmnum;
    private Double slope;
    private Double offset;
    private Double corrcoeff;
    private Double maxdev;
    private Double avgdev;
    private Double polyc3;
    private Double polyc2;
    private Double polyc1;
    private Double polyc0;
    private Double polyr2;
    private Short spindle;
    private java.time.LocalDateTime calibtime;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public GLDPARM_37() {
    }

    public Short getGate() {
        return gate;
    }

    public void setGate(Short gate) {
        this.gate = gate;
    }

    public String getElemname() {
        return elemname;
    }

    public void setElemname(String elemname) {
        this.elemname = elemname;
    }

    public Short getId() {
        return id;
    }

    public void setId(Short id) {
        this.id = id;
    }

    public String getHalf() {
        return half;
    }

    public void setHalf(String half) {
        this.half = half;
    }

    public Double getCenterfreq() {
        return centerfreq;
    }

    public void setCenterfreq(Double centerfreq) {
        this.centerfreq = centerfreq;
    }

    public Short getParmnum() {
        return parmnum;
    }

    public void setParmnum(Short parmnum) {
        this.parmnum = parmnum;
    }

    public Double getSlope() {
        return slope;
    }

    public void setSlope(Double slope) {
        this.slope = slope;
    }

    public Double getOffset() {
        return offset;
    }

    public void setOffset(Double offset) {
        this.offset = offset;
    }

    public Double getCorrcoeff() {
        return corrcoeff;
    }

    public void setCorrcoeff(Double corrcoeff) {
        this.corrcoeff = corrcoeff;
    }

    public Double getMaxdev() {
        return maxdev;
    }

    public void setMaxdev(Double maxdev) {
        this.maxdev = maxdev;
    }

    public Double getAvgdev() {
        return avgdev;
    }

    public void setAvgdev(Double avgdev) {
        this.avgdev = avgdev;
    }

    public Double getPolyc3() {
        return polyc3;
    }

    public void setPolyc3(Double polyc3) {
        this.polyc3 = polyc3;
    }

    public Double getPolyc2() {
        return polyc2;
    }

    public void setPolyc2(Double polyc2) {
        this.polyc2 = polyc2;
    }

    public Double getPolyc1() {
        return polyc1;
    }

    public void setPolyc1(Double polyc1) {
        this.polyc1 = polyc1;
    }

    public Double getPolyc0() {
        return polyc0;
    }

    public void setPolyc0(Double polyc0) {
        this.polyc0 = polyc0;
    }

    public Double getPolyr2() {
        return polyr2;
    }

    public void setPolyr2(Double polyr2) {
        this.polyr2 = polyr2;
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public java.time.LocalDateTime getCalibtime() {
        return calibtime;
    }

    public void setCalibtime(java.time.LocalDateTime calibtime) {
        this.calibtime = calibtime;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



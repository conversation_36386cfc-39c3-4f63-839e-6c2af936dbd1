package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.WASH_BATH_LIFE;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for WASH_BATH_LIFE
 */
public interface WASH_BATH_LIFE_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.WASH_BATH_LIFE AS TARGET",
        "USING (VALUES (",
        "    #{dtinsert},",
        "    #{dtEntry},",
        "    #{resource},",
        "    #{interval},",
        "    #{preoxidePriorityLow},",
        "    #{preoxidePriorityHigh},",
        "    #{loaderDiwLife},",
        "    #{loaderUsLife},",
        "    #{usBathlife},",
        "    #{msBathlife},",
        "    #{loaderFilters},",
        "    #{scrubBrushesLife},",
        "    #{scrubFilters},",
        "    #{usFilters},",
        "    #{qdr1Filters},",
        "    #{msFilters},",
        "    #{rinseFilters},",
        "    #{scrubBrushesDisk},",
        "    #{preoxidePriorityLowLimit},",
        "    #{preoxidePriorityHighLimit},",
        "    #{loaderDiwLifeLimit},",
        "    #{loaderUsLifeLimit},",
        "    #{usBathlifeLimit},",
        "    #{msBathlifeLimit},",
        "    #{loaderFiltersLimit},",
        "    #{scrubBrushesLifeLimit},",
        "    #{scrubFiltersLimit},",
        "    #{usFiltersLimit},",
        "    #{qdr1FiltersLimit},",
        "    #{msFiltersLimit},",
        "    #{rinseFiltersLimit},",
        "    #{scrubBrushesDiskLimit},",
        "    #{preoxidePriorityLowLastupdateTs},",
        "    #{preoxidePriorityHighLastupdateTs},",
        "    #{loaderDiwLifeLastupdateTs},",
        "    #{loaderUsLifeLastupdateTs},",
        "    #{usBathlifeLastupdateTs},",
        "    #{msBathlifeLastupdateTs},",
        "    #{loaderFiltersLastupdateTs},",
        "    #{scrubBrushesLifeLastupdateTs},",
        "    #{scrubFiltersLastupdateTs},",
        "    #{usFiltersLastupdateTs},",
        "    #{qdr1FiltersLastupdateTs},",
        "    #{msFiltersLastupdateTs},",
        "    #{rinseFiltersLastupdateTs},",
        "    #{comments}",
        ")) AS SOURCE (",
        "    DTINSERT,",
        "    DT_ENTRY,",
        "    RESOURCE,",
        "    INTERVAL,",
        "    PREOXIDE_PRIORITY_LOW,",
        "    PREOXIDE_PRIORITY_HIGH,",
        "    LOADER_DIW_LIFE,",
        "    LOADER_US_LIFE,",
        "    US_BATHLIFE,",
        "    MS_BATHLIFE,",
        "    LOADER_FILTERS,",
        "    SCRUB_BRUSHES_LIFE,",
        "    SCRUB_FILTERS,",
        "    US_FILTERS,",
        "    QDR1_FILTERS,",
        "    MS_FILTERS,",
        "    RINSE_FILTERS,",
        "    SCRUB_BRUSHES_DISK,",
        "    PREOXIDE_PRIORITY_LOW_LIMIT,",
        "    PREOXIDE_PRIORITY_HIGH_LIMIT,",
        "    LOADER_DIW_LIFE_LIMIT,",
        "    LOADER_US_LIFE_LIMIT,",
        "    US_BATHLIFE_LIMIT,",
        "    MS_BATHLIFE_LIMIT,",
        "    LOADER_FILTERS_LIMIT,",
        "    SCRUB_BRUSHES_LIFE_LIMIT,",
        "    SCRUB_FILTERS_LIMIT,",
        "    US_FILTERS_LIMIT,",
        "    QDR1_FILTERS_LIMIT,",
        "    MS_FILTERS_LIMIT,",
        "    RINSE_FILTERS_LIMIT,",
        "    SCRUB_BRUSHES_DISK_LIMIT,",
        "    PREOXIDE_PRIORITY_LOW_LASTUPDATE_TS,",
        "    PREOXIDE_PRIORITY_HIGH_LASTUPDATE_TS,",
        "    LOADER_DIW_LIFE_LASTUPDATE_TS,",
        "    LOADER_US_LIFE_LASTUPDATE_TS,",
        "    US_BATHLIFE_LASTUPDATE_TS,",
        "    MS_BATHLIFE_LASTUPDATE_TS,",
        "    LOADER_FILTERS_LASTUPDATE_TS,",
        "    SCRUB_BRUSHES_LIFE_LASTUPDATE_TS,",
        "    SCRUB_FILTERS_LASTUPDATE_TS,",
        "    US_FILTERS_LASTUPDATE_TS,",
        "    QDR1_FILTERS_LASTUPDATE_TS,",
        "    MS_FILTERS_LASTUPDATE_TS,",
        "    RINSE_FILTERS_LASTUPDATE_TS,",
        "    COMMENTS",
        ")",
        "ON TARGET.DT_ENTRY = SOURCE.DT_ENTRY AND TARGET.RESOURCE = SOURCE.RESOURCE AND TARGET.INTERVAL = SOURCE.INTERVAL",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.PREOXIDE_PRIORITY_LOW = SOURCE.PREOXIDE_PRIORITY_LOW,",
        "    TARGET.PREOXIDE_PRIORITY_HIGH = SOURCE.PREOXIDE_PRIORITY_HIGH,",
        "    TARGET.LOADER_DIW_LIFE = SOURCE.LOADER_DIW_LIFE,",
        "    TARGET.LOADER_US_LIFE = SOURCE.LOADER_US_LIFE,",
        "    TARGET.US_BATHLIFE = SOURCE.US_BATHLIFE,",
        "    TARGET.MS_BATHLIFE = SOURCE.MS_BATHLIFE,",
        "    TARGET.LOADER_FILTERS = SOURCE.LOADER_FILTERS,",
        "    TARGET.SCRUB_BRUSHES_LIFE = SOURCE.SCRUB_BRUSHES_LIFE,",
        "    TARGET.SCRUB_FILTERS = SOURCE.SCRUB_FILTERS,",
        "    TARGET.US_FILTERS = SOURCE.US_FILTERS,",
        "    TARGET.QDR1_FILTERS = SOURCE.QDR1_FILTERS,",
        "    TARGET.MS_FILTERS = SOURCE.MS_FILTERS,",
        "    TARGET.RINSE_FILTERS = SOURCE.RINSE_FILTERS,",
        "    TARGET.SCRUB_BRUSHES_DISK = SOURCE.SCRUB_BRUSHES_DISK,",
        "    TARGET.PREOXIDE_PRIORITY_LOW_LIMIT = SOURCE.PREOXIDE_PRIORITY_LOW_LIMIT,",
        "    TARGET.PREOXIDE_PRIORITY_HIGH_LIMIT = SOURCE.PREOXIDE_PRIORITY_HIGH_LIMIT,",
        "    TARGET.LOADER_DIW_LIFE_LIMIT = SOURCE.LOADER_DIW_LIFE_LIMIT,",
        "    TARGET.LOADER_US_LIFE_LIMIT = SOURCE.LOADER_US_LIFE_LIMIT,",
        "    TARGET.US_BATHLIFE_LIMIT = SOURCE.US_BATHLIFE_LIMIT,",
        "    TARGET.MS_BATHLIFE_LIMIT = SOURCE.MS_BATHLIFE_LIMIT,",
        "    TARGET.LOADER_FILTERS_LIMIT = SOURCE.LOADER_FILTERS_LIMIT,",
        "    TARGET.SCRUB_BRUSHES_LIFE_LIMIT = SOURCE.SCRUB_BRUSHES_LIFE_LIMIT,",
        "    TARGET.SCRUB_FILTERS_LIMIT = SOURCE.SCRUB_FILTERS_LIMIT,",
        "    TARGET.US_FILTERS_LIMIT = SOURCE.US_FILTERS_LIMIT,",
        "    TARGET.QDR1_FILTERS_LIMIT = SOURCE.QDR1_FILTERS_LIMIT,",
        "    TARGET.MS_FILTERS_LIMIT = SOURCE.MS_FILTERS_LIMIT,",
        "    TARGET.RINSE_FILTERS_LIMIT = SOURCE.RINSE_FILTERS_LIMIT,",
        "    TARGET.SCRUB_BRUSHES_DISK_LIMIT = SOURCE.SCRUB_BRUSHES_DISK_LIMIT,",
        "    TARGET.PREOXIDE_PRIORITY_LOW_LASTUPDATE_TS = SOURCE.PREOXIDE_PRIORITY_LOW_LASTUPDATE_TS,",
        "    TARGET.PREOXIDE_PRIORITY_HIGH_LASTUPDATE_TS = SOURCE.PREOXIDE_PRIORITY_HIGH_LASTUPDATE_TS,",
        "    TARGET.LOADER_DIW_LIFE_LASTUPDATE_TS = SOURCE.LOADER_DIW_LIFE_LASTUPDATE_TS,",
        "    TARGET.LOADER_US_LIFE_LASTUPDATE_TS = SOURCE.LOADER_US_LIFE_LASTUPDATE_TS,",
        "    TARGET.US_BATHLIFE_LASTUPDATE_TS = SOURCE.US_BATHLIFE_LASTUPDATE_TS,",
        "    TARGET.MS_BATHLIFE_LASTUPDATE_TS = SOURCE.MS_BATHLIFE_LASTUPDATE_TS,",
        "    TARGET.LOADER_FILTERS_LASTUPDATE_TS = SOURCE.LOADER_FILTERS_LASTUPDATE_TS,",
        "    TARGET.SCRUB_BRUSHES_LIFE_LASTUPDATE_TS = SOURCE.SCRUB_BRUSHES_LIFE_LASTUPDATE_TS,",
        "    TARGET.SCRUB_FILTERS_LASTUPDATE_TS = SOURCE.SCRUB_FILTERS_LASTUPDATE_TS,",
        "    TARGET.US_FILTERS_LASTUPDATE_TS = SOURCE.US_FILTERS_LASTUPDATE_TS,",
        "    TARGET.QDR1_FILTERS_LASTUPDATE_TS = SOURCE.QDR1_FILTERS_LASTUPDATE_TS,",
        "    TARGET.MS_FILTERS_LASTUPDATE_TS = SOURCE.MS_FILTERS_LASTUPDATE_TS,",
        "    TARGET.RINSE_FILTERS_LASTUPDATE_TS = SOURCE.RINSE_FILTERS_LASTUPDATE_TS,",
        "    TARGET.COMMENTS = SOURCE.COMMENTS",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    DTINSERT,",
        "    DT_ENTRY,",
        "    RESOURCE,",
        "    INTERVAL,",
        "    PREOXIDE_PRIORITY_LOW,",
        "    PREOXIDE_PRIORITY_HIGH,",
        "    LOADER_DIW_LIFE,",
        "    LOADER_US_LIFE,",
        "    US_BATHLIFE,",
        "    MS_BATHLIFE,",
        "    LOADER_FILTERS,",
        "    SCRUB_BRUSHES_LIFE,",
        "    SCRUB_FILTERS,",
        "    US_FILTERS,",
        "    QDR1_FILTERS,",
        "    MS_FILTERS,",
        "    RINSE_FILTERS,",
        "    SCRUB_BRUSHES_DISK,",
        "    PREOXIDE_PRIORITY_LOW_LIMIT,",
        "    PREOXIDE_PRIORITY_HIGH_LIMIT,",
        "    LOADER_DIW_LIFE_LIMIT,",
        "    LOADER_US_LIFE_LIMIT,",
        "    US_BATHLIFE_LIMIT,",
        "    MS_BATHLIFE_LIMIT,",
        "    LOADER_FILTERS_LIMIT,",
        "    SCRUB_BRUSHES_LIFE_LIMIT,",
        "    SCRUB_FILTERS_LIMIT,",
        "    US_FILTERS_LIMIT,",
        "    QDR1_FILTERS_LIMIT,",
        "    MS_FILTERS_LIMIT,",
        "    RINSE_FILTERS_LIMIT,",
        "    SCRUB_BRUSHES_DISK_LIMIT,",
        "    PREOXIDE_PRIORITY_LOW_LASTUPDATE_TS,",
        "    PREOXIDE_PRIORITY_HIGH_LASTUPDATE_TS,",
        "    LOADER_DIW_LIFE_LASTUPDATE_TS,",
        "    LOADER_US_LIFE_LASTUPDATE_TS,",
        "    US_BATHLIFE_LASTUPDATE_TS,",
        "    MS_BATHLIFE_LASTUPDATE_TS,",
        "    LOADER_FILTERS_LASTUPDATE_TS,",
        "    SCRUB_BRUSHES_LIFE_LASTUPDATE_TS,",
        "    SCRUB_FILTERS_LASTUPDATE_TS,",
        "    US_FILTERS_LASTUPDATE_TS,",
        "    QDR1_FILTERS_LASTUPDATE_TS,",
        "    MS_FILTERS_LASTUPDATE_TS,",
        "    RINSE_FILTERS_LASTUPDATE_TS,",
        "    COMMENTS",
        "  )",
        "  VALUES (",
        "    SOURCE.DTINSERT,",
        "    SOURCE.DT_ENTRY,",
        "    SOURCE.RESOURCE,",
        "    SOURCE.INTERVAL,",
        "    SOURCE.PREOXIDE_PRIORITY_LOW,",
        "    SOURCE.PREOXIDE_PRIORITY_HIGH,",
        "    SOURCE.LOADER_DIW_LIFE,",
        "    SOURCE.LOADER_US_LIFE,",
        "    SOURCE.US_BATHLIFE,",
        "    SOURCE.MS_BATHLIFE,",
        "    SOURCE.LOADER_FILTERS,",
        "    SOURCE.SCRUB_BRUSHES_LIFE,",
        "    SOURCE.SCRUB_FILTERS,",
        "    SOURCE.US_FILTERS,",
        "    SOURCE.QDR1_FILTERS,",
        "    SOURCE.MS_FILTERS,",
        "    SOURCE.RINSE_FILTERS,",
        "    SOURCE.SCRUB_BRUSHES_DISK,",
        "    SOURCE.PREOXIDE_PRIORITY_LOW_LIMIT,",
        "    SOURCE.PREOXIDE_PRIORITY_HIGH_LIMIT,",
        "    SOURCE.LOADER_DIW_LIFE_LIMIT,",
        "    SOURCE.LOADER_US_LIFE_LIMIT,",
        "    SOURCE.US_BATHLIFE_LIMIT,",
        "    SOURCE.MS_BATHLIFE_LIMIT,",
        "    SOURCE.LOADER_FILTERS_LIMIT,",
        "    SOURCE.SCRUB_BRUSHES_LIFE_LIMIT,",
        "    SOURCE.SCRUB_FILTERS_LIMIT,",
        "    SOURCE.US_FILTERS_LIMIT,",
        "    SOURCE.QDR1_FILTERS_LIMIT,",
        "    SOURCE.MS_FILTERS_LIMIT,",
        "    SOURCE.RINSE_FILTERS_LIMIT,",
        "    SOURCE.SCRUB_BRUSHES_DISK_LIMIT,",
        "    SOURCE.PREOXIDE_PRIORITY_LOW_LASTUPDATE_TS,",
        "    SOURCE.PREOXIDE_PRIORITY_HIGH_LASTUPDATE_TS,",
        "    SOURCE.LOADER_DIW_LIFE_LASTUPDATE_TS,",
        "    SOURCE.LOADER_US_LIFE_LASTUPDATE_TS,",
        "    SOURCE.US_BATHLIFE_LASTUPDATE_TS,",
        "    SOURCE.MS_BATHLIFE_LASTUPDATE_TS,",
        "    SOURCE.LOADER_FILTERS_LASTUPDATE_TS,",
        "    SOURCE.SCRUB_BRUSHES_LIFE_LASTUPDATE_TS,",
        "    SOURCE.SCRUB_FILTERS_LASTUPDATE_TS,",
        "    SOURCE.US_FILTERS_LASTUPDATE_TS,",
        "    SOURCE.QDR1_FILTERS_LASTUPDATE_TS,",
        "    SOURCE.MS_FILTERS_LASTUPDATE_TS,",
        "    SOURCE.RINSE_FILTERS_LASTUPDATE_TS,",
        "    SOURCE.COMMENTS",
        "  )"
    })
    void insertOrUpdate(WASH_BATH_LIFE record);
}











package wdc.disk.ecs.apps.MQUpload.model;

public class RabbitMQProperties {
    
    private String[] rabbitMQHosts; 
    private int rabbitMQPort;
    private String rabbitMQUsername;
    private String rabbitMQPassword;
    private String rabbitMQExchange;
    private String rabbitMQQueueConfigPath;
   

    public String[] getRabbitMQHosts() {
        return rabbitMQHosts;
    }
    public void setRabbitMQHosts(String[] rabbitMQHosts) {
        this.rabbitMQHosts = rabbitMQHosts;
    }
    public int getRabbitMQPort() {
        return rabbitMQPort;
    }
    public void setRabbitMQPort(int rabbitMQPort) {
        this.rabbitMQPort = rabbitMQPort;
    }
    public String getRabbitMQUsername() {
        return rabbitMQUsername;
    }
    public void setRabbitMQUsername(String rabbitMQUsername) {
        this.rabbitMQUsername = rabbitMQUsername;
    }
    public String getRabbitMQPassword() {
        return rabbitMQPassword;
    }
    public void setRabbitMQPassword(String rabbitMQPassword) {
        this.rabbitMQPassword = rabbitMQPassword;
    }
    public String getRabbitMQExchange() {
        return rabbitMQExchange;
    }
    public void setRabbitMQExchange(String rabbitMQExchange) {
        this.rabbitMQExchange = rabbitMQExchange;
    }
    public void setRabbitMQQueueConfigPath(String rabbitMQQueueConfigPath) {
        this.rabbitMQQueueConfigPath = rabbitMQQueueConfigPath;
    }
    public String getRabbitMQQueueConfigPath() {
        return rabbitMQQueueConfigPath;
    }

}

package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.MAGCBPRM_38;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for MAGCBPRM_38
 */
public interface MAGCBPRM_38_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.MAGCBPRM AS TARGET",
        "USING (VALUES (",
        "    #{elemname},",
        "    #{channel},",
        "    #{half},",
        "    #{freq},",
        "    #{gain},",
        "    #{parmnum},",
        "    #{slope},",
        "    #{offset},",
        "    #{corrcoeff},",
        "    #{maxdev},",
        "    #{avgdev},",
        "    #{spindle},",
        "    #{calibtime},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    ELEMNAME,",
        "    CHANNEL,",
        "    HALF,",
        "    FREQ,",
        "    GAIN,",
        "    PARMNUM,",
        "    SLOPE,",
        "    OFFSET,",
        "    CORRCOEFF,",
        "    MAXDEV,",
        "    AVGDEV,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.ELEMNAME = SOURCE.ELEMNAME AND TARGET.CHANNEL = SOURCE.CHANNEL AND TARGET.HALF = SOURCE.HALF AND TARGET.FREQ = SOURCE.FREQ AND TARGET.GAIN = SOURCE.GAIN AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CALIBTIME = SOURCE.CALIBTIME",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.PARMNUM = SOURCE.PARMNUM,",
        "    TARGET.SLOPE = SOURCE.SLOPE,",
        "    TARGET.OFFSET = SOURCE.OFFSET,",
        "    TARGET.CORRCOEFF = SOURCE.CORRCOEFF,",
        "    TARGET.MAXDEV = SOURCE.MAXDEV,",
        "    TARGET.AVGDEV = SOURCE.AVGDEV,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    ELEMNAME,",
        "    CHANNEL,",
        "    HALF,",
        "    FREQ,",
        "    GAIN,",
        "    PARMNUM,",
        "    SLOPE,",
        "    OFFSET,",
        "    CORRCOEFF,",
        "    MAXDEV,",
        "    AVGDEV,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.ELEMNAME,",
        "    SOURCE.CHANNEL,",
        "    SOURCE.HALF,",
        "    SOURCE.FREQ,",
        "    SOURCE.GAIN,",
        "    SOURCE.PARMNUM,",
        "    SOURCE.SLOPE,",
        "    SOURCE.OFFSET,",
        "    SOURCE.CORRCOEFF,",
        "    SOURCE.MAXDEV,",
        "    SOURCE.AVGDEV,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CALIBTIME,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(MAGCBPRM_38 record);
}











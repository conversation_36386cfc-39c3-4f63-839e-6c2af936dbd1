#Fat Jar Configuration File
#Tue May 31 14:52:30 PDT 2011
onejar.license.required=true
manifest.classpath=
manifest.removesigners=true
onejar.checkbox=false
jarname=/opt/ecs/ecs_SFTPSend.jar
manifest.mergeall=true
manifest.mainclass=
manifest.file=<createnew>
jarname.isextern=true
onejar.expand=
excludes=<po|IBM Disk Mesa>;<po|IBM Disk Manufacturing Objects>;<po|IBM Disk Utility>;<po|IBM Disk ECS2000 Utility>;<po|IBM Disk ECS2000 Serialmanager>;<po|IBM Disk ECS2000 Runsheet>;<po|IBM Disk ECS2000>;<po|IBM Disk Substrate Utilities>;<po|AutomationAccess Java Client>;<po|IBM Disk PersistentQueue>;<jar|jt400.jar>;<jar|comm.jar>;<jar|db2java.zip>;<jar|servlet.jar>;<jar|db2jcc_javax.jar>;<jar|db2jcc_license_cu.jar>;<jar|db2jcc.jar>;<jar|poi-3.0.1-FINAL-20070705.jar>;<jar|poi-contrib-3.0.1-FINAL-20070705.jar>;<jar|poi-scratchpad-3.0.1-FINAL-20070705.jar>;<jar|jcifs-1.3.15.jar>;<jar|classes.zip>;<jar|com.ms.mtx_dnld.zip>
includes=

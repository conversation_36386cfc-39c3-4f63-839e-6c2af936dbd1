package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for GEOMRSLT_25
 */
public class GEOMRSLT_25  {
    private Short spindle;
    private Integer glidenum;
    private Short bandnum;
    private Double tohvelTop;
    private Double tohvelBot;
    private Double tohnmTop;
    private Double tohnmBot;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public GEOMRSLT_25() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getGlidenum() {
        return glidenum;
    }

    public void setGlidenum(Integer glidenum) {
        this.glidenum = glidenum;
    }

    public Short getBandnum() {
        return bandnum;
    }

    public void setBandnum(Short bandnum) {
        this.bandnum = bandnum;
    }

    public Double getTohvelTop() {
        return tohvelTop;
    }

    public void setTohvelTop(Double tohvelTop) {
        this.tohvelTop = tohvelTop;
    }

    public Double getTohvelBot() {
        return tohvelBot;
    }

    public void setTohvelBot(Double tohvelBot) {
        this.tohvelBot = tohvelBot;
    }

    public Double getTohnmTop() {
        return tohnmTop;
    }

    public void setTohnmTop(Double tohnmTop) {
        this.tohnmTop = tohnmTop;
    }

    public Double getTohnmBot() {
        return tohnmBot;
    }

    public void setTohnmBot(Double tohnmBot) {
        this.tohnmBot = tohnmBot;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



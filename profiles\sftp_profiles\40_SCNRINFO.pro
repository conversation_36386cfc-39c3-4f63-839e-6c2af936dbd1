*| ColumnName       |  DBColumnType  |  DBColumnLength  |  SystemType       |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  SCNRNAME         |  Char        |  9      |  System.String    |  9      |  0      |  Y      |  0      |  0      |  
|  BOARDNUM         |  SmallInt    |  2      |  System.Int16     |  2      |  1      |         |  20     |  1      |  
|  AXIS             |  Char        |  1      |  System.String    |  1      |  2      |         |  24     |  2      |  
|  CALIBVALUE       |  Double      |  8      |  System.Double    |  8      |  3      |         |  32     |  3      |  
|  MAXPOSERR        |  SmallInt    |  2      |  System.Int16     |  2      |  4      |         |  48     |  4      |  
|  IDLIMIT          |  Double      |  8      |  System.Double    |  8      |  5      |         |  56     |  5      |  
|  ODLIMIT          |  Double      |  8      |  System.Double    |  8      |  6      |         |  72     |  6      |  
|  SCNRTYPE         |  Char        |  4      |  System.String    |  4      |  7      |         |  88     |  7      |  
|  HGALOADER        |  Char        |  1      |  System.String    |  1      |  8      |         |  96     |  8      |  
|  SERIALNUM        |  Char        |  11     |  System.String    |  11     |  9      |         |  98     |  9      |  
|  PARMS            |  Char        |  131    |  System.String    |  131    |  10     |         |  120    |  10     |  
|  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  11     |  Y      |  384    |  11     |  
|  CALIBTIME        |  Timestamp   |  4      |  System.DateTime  |  4      |  12     |  Y      |  392    |  12     |  
|  DTINSERT         |  Timestamp   |  4      |  System.DateTime  |  4      |  13     |  Y      |  0      |  13     |  
|  CORRELID         |  Integer     |  4      |  System.Int32     |  4      |  14     |         |  0      |  14     |  

package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSHEADSTAT
 */
public class TDSHEADSTAT  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private Integer spindle;
    private String headId;
    private Integer tdsActive;
    private Integer mrActive;
    private Double mrAmpMv;
    private Double tdsRmsMv;
    private Double prescanTdsCount;
    private Double touchdown;
    private Double mrGain;
    private Double mrBias;
    private Double wrResistanceOhm;
    private Double mrResistanceOhm;
    private Double tdsResistanceOhm;
    private Double dfhResistanceOhm;
    private Double tdsResistancePreScanOhm;
    private String hsaIdAuto;
    private Double touchdownInitial;
    private String tdsBiasMode;
    private Double tdsBias;
    private Double tdsPositiveThresholdAvalanche;
    private Double tdsNegativeThresholdAvalanche;
    private Double psatLoGrassThresholdOffset;
    private Double psatHiGrassThresholdOffset;

    // Default constructor
    public TDSHEADSTAT() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getHeadId() {
        return headId;
    }

    public void setHeadId(String headId) {
        this.headId = headId;
    }

    public Integer getTdsActive() {
        return tdsActive;
    }

    public void setTdsActive(Integer tdsActive) {
        this.tdsActive = tdsActive;
    }

    public Integer getMrActive() {
        return mrActive;
    }

    public void setMrActive(Integer mrActive) {
        this.mrActive = mrActive;
    }

    public Double getMrAmpMv() {
        return mrAmpMv;
    }

    public void setMrAmpMv(Double mrAmpMv) {
        this.mrAmpMv = mrAmpMv;
    }

    public Double getTdsRmsMv() {
        return tdsRmsMv;
    }

    public void setTdsRmsMv(Double tdsRmsMv) {
        this.tdsRmsMv = tdsRmsMv;
    }

    public Double getPrescanTdsCount() {
        return prescanTdsCount;
    }

    public void setPrescanTdsCount(Double prescanTdsCount) {
        this.prescanTdsCount = prescanTdsCount;
    }

    public Double getTouchdown() {
        return touchdown;
    }

    public void setTouchdown(Double touchdown) {
        this.touchdown = touchdown;
    }

    public Double getMrGain() {
        return mrGain;
    }

    public void setMrGain(Double mrGain) {
        this.mrGain = mrGain;
    }

    public Double getMrBias() {
        return mrBias;
    }

    public void setMrBias(Double mrBias) {
        this.mrBias = mrBias;
    }

    public Double getWrResistanceOhm() {
        return wrResistanceOhm;
    }

    public void setWrResistanceOhm(Double wrResistanceOhm) {
        this.wrResistanceOhm = wrResistanceOhm;
    }

    public Double getMrResistanceOhm() {
        return mrResistanceOhm;
    }

    public void setMrResistanceOhm(Double mrResistanceOhm) {
        this.mrResistanceOhm = mrResistanceOhm;
    }

    public Double getTdsResistanceOhm() {
        return tdsResistanceOhm;
    }

    public void setTdsResistanceOhm(Double tdsResistanceOhm) {
        this.tdsResistanceOhm = tdsResistanceOhm;
    }

    public Double getDfhResistanceOhm() {
        return dfhResistanceOhm;
    }

    public void setDfhResistanceOhm(Double dfhResistanceOhm) {
        this.dfhResistanceOhm = dfhResistanceOhm;
    }

    public Double getTdsResistancePreScanOhm() {
        return tdsResistancePreScanOhm;
    }

    public void setTdsResistancePreScanOhm(Double tdsResistancePreScanOhm) {
        this.tdsResistancePreScanOhm = tdsResistancePreScanOhm;
    }

    public String getHsaIdAuto() {
        return hsaIdAuto;
    }

    public void setHsaIdAuto(String hsaIdAuto) {
        this.hsaIdAuto = hsaIdAuto;
    }

    public Double getTouchdownInitial() {
        return touchdownInitial;
    }

    public void setTouchdownInitial(Double touchdownInitial) {
        this.touchdownInitial = touchdownInitial;
    }

    public String getTdsBiasMode() {
        return tdsBiasMode;
    }

    public void setTdsBiasMode(String tdsBiasMode) {
        this.tdsBiasMode = tdsBiasMode;
    }

    public Double getTdsBias() {
        return tdsBias;
    }

    public void setTdsBias(Double tdsBias) {
        this.tdsBias = tdsBias;
    }

    public Double getTdsPositiveThresholdAvalanche() {
        return tdsPositiveThresholdAvalanche;
    }

    public void setTdsPositiveThresholdAvalanche(Double tdsPositiveThresholdAvalanche) {
        this.tdsPositiveThresholdAvalanche = tdsPositiveThresholdAvalanche;
    }

    public Double getTdsNegativeThresholdAvalanche() {
        return tdsNegativeThresholdAvalanche;
    }

    public void setTdsNegativeThresholdAvalanche(Double tdsNegativeThresholdAvalanche) {
        this.tdsNegativeThresholdAvalanche = tdsNegativeThresholdAvalanche;
    }

    public Double getPsatLoGrassThresholdOffset() {
        return psatLoGrassThresholdOffset;
    }

    public void setPsatLoGrassThresholdOffset(Double psatLoGrassThresholdOffset) {
        this.psatLoGrassThresholdOffset = psatLoGrassThresholdOffset;
    }

    public Double getPsatHiGrassThresholdOffset() {
        return psatHiGrassThresholdOffset;
    }

    public void setPsatHiGrassThresholdOffset(Double psatHiGrassThresholdOffset) {
        this.psatHiGrassThresholdOffset = psatHiGrassThresholdOffset;
    }

}



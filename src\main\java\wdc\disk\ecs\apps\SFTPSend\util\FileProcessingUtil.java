package wdc.disk.ecs.apps.SFTPSend.util;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.SFTPSend.util.CompareTwoFileSequence;
import java.io.*;
import java.nio.file.*;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

public class FileProcessingUtil {
    private final LogStream log;
    private final int maxReadFiles;
    private long fileCount = 0;
    
    public FileProcessingUtil(LogStream log, int maxReadFiles) {
        this.log = log;
        this.maxReadFiles = maxReadFiles;
    }
    
    public File[] getSortedFiles(File dir) {
        try {
            return Files.walk(dir.toPath())
                .filter(path -> {
                    File file = path.toFile();
                    return file.isFile() && ++fileCount < maxReadFiles;
                })
                .map(Path::toFile)
                .sorted(Comparator.comparingLong(File::lastModified)
                    .thenComparing((f1, f2) -> {
                        if (f1.lastModified() == f2.lastModified()) {
                            return CompareTwoFileSequence.compareTwoFileIfSameLastModify(
                                f1.getName(), f2.getName()) ? -1 : 1;
                        }
                        return 0;
                    }))
                .toArray(File[]::new);
        } catch (Exception e) {
            log.writeExceptionStack(e);
            return new File[0];
        } finally {
            fileCount = 0;
        }
    }
    
    public void processFilesInBatches(File dir, Consumer<List<File>> processor) {
        int batchSize = 1000;
        try {
            Files.walk(dir.toPath())
                .filter(path -> {
                    File file = path.toFile();
                    return file.isFile() && ++fileCount < maxReadFiles;
                })
                .map(Path::toFile)
                .sorted(Comparator.comparingLong(File::lastModified))
                .collect(Collectors.groupingBy(file -> 
                    fileCount / batchSize))
                .values()
                .forEach(processor);
        } catch (Exception e) {
            log.writeExceptionStack(e);
        } finally {
            fileCount = 0;
        }
    }
}
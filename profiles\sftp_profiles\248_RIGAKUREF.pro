*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |         |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  3      |         |  0      |  3      |  
|  REFNO                 |  Char        |  11     |  System.String         |  11     |  4      |         |  0      |  4      |  
|  RECIPE                |  Char        |  15     |  System.String         |  15     |  5      |         |  0      |  5      |  
|  MAGAVG                |  Double      |  8      |  System.Double         |  8      |  6      |         |  0      |  6      |  
|  RUAVG                 |  Double      |  8      |  System.Double         |  8      |  7      |         |  0      |  7      |  
|  NIWAVG                |  Double      |  8      |  System.Double         |  8      |  8      |         |  0      |  8      |  
|  SULAVG                |  Double      |  8      |  System.Double         |  8      |  9      |         |  0      |  9      |  
|  TIAVG                 |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  MAGSTDEV              |  Double      |  8      |  System.Double         |  8      |  11     |         |  0      |  11     |  
|  RUSTDEV               |  Double      |  8      |  System.Double         |  8      |  12     |         |  0      |  12     |  
|  NIWSTDEV              |  Double      |  8      |  System.Double         |  8      |  13     |         |  0      |  13     |  
|  SULSTDEV              |  Double      |  8      |  System.Double         |  8      |  14     |         |  0      |  14     |  
|  TISTDEV               |  Double      |  8      |  System.Double         |  8      |  15     |         |  0      |  15     |  
|  MAGRANGE              |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  RURANGE               |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  NIWRANGE              |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  SULRANGE              |  Double      |  8      |  System.Double         |  8      |  19     |         |  0      |  19     |  
|  TIRANGE               |  Double      |  8      |  System.Double         |  8      |  20     |         |  0      |  20     |  
|  CAPAVG                |  Double      |  8      |  System.Double         |  8      |  21     |         |  0      |  21     |  
|  OXMAGAVG              |  Double      |  8      |  System.Double         |  8      |  22     |         |  0      |  22     |  
|  CRTIAVG               |  Double      |  8      |  System.Double         |  8      |  23     |         |  0      |  23     |  
|  ALTIAVG               |  Double      |  8      |  System.Double         |  8      |  24     |         |  0      |  24     |  
|  NIWCRAVG              |  Double      |  8      |  System.Double         |  8      |  25     |         |  0      |  25     |  
|  ELFAVG                |  Double      |  8      |  System.Double         |  8      |  26     |         |  0      |  26     |  
|  CAPSTDEV              |  Double      |  8      |  System.Double         |  8      |  27     |         |  0      |  27     |  
|  OXMAGSTDEV            |  Double      |  8      |  System.Double         |  8      |  28     |         |  0      |  28     |  
|  CRTISTDEV             |  Double      |  8      |  System.Double         |  8      |  29     |         |  0      |  29     |  
|  ALTISTDEV             |  Double      |  8      |  System.Double         |  8      |  30     |         |  0      |  30     |  
|  NIWCRSTDEV            |  Double      |  8      |  System.Double         |  8      |  31     |         |  0      |  31     |  
|  ELFSTDEV              |  Double      |  8      |  System.Double         |  8      |  32     |         |  0      |  32     |  
|  CAPRANGE              |  Double      |  8      |  System.Double         |  8      |  33     |         |  0      |  33     |  
|  OXMAGRANGE            |  Double      |  8      |  System.Double         |  8      |  34     |         |  0      |  34     |  
|  CRTIRANGE             |  Double      |  8      |  System.Double         |  8      |  35     |         |  0      |  35     |  
|  ALTIRANGE             |  Double      |  8      |  System.Double         |  8      |  36     |         |  0      |  36     |  
|  NIWCRRANGE            |  Double      |  8      |  System.Double         |  8      |  37     |         |  0      |  37     |  
|  ELFRANGE              |  Double      |  8      |  System.Double         |  8      |  38     |         |  0      |  38     |  
|  NITAAVG               |  Double      |  8      |  System.Double         |  8      |  39     |         |  0      |  39     |  
|  NITASTDEV             |  Double      |  8      |  System.Double         |  8      |  40     |         |  0      |  40     |  

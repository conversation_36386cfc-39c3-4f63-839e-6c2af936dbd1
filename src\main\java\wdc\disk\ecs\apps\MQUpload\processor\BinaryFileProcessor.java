package wdc.disk.ecs.apps.MQUpload.processor;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import ibm.disk.extensions.StringMethods;
import ibm.disk.profiles.ProfileException;
import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.exception.MessageProcessingException;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;
import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;
import wdc.disk.ecs.apps.SFTPSend.binary.BinaryMsgFieldLength;
import wdc.disk.ecs.apps.SFTPSend.binary.PackedMsgIndex;
import wdc.disk.ecs.apps.SFTPSend.binary.SingleMessage;
import wdc.disk.ecs.apps.SFTPSend.common.pds.conversion.ConvertorUtility;


/**
 * Implementation of FileProcessor for handling binary files.
 * This class reads and processes binary-encoded files, performing message parsing
 * and transformations based on profile configurations.
 */
public class BinaryFileProcessor implements FileProcessor {
    private final LogStream log;
    private final TableSchemaManager tableSchemaManager;

    public BinaryFileProcessor(LogStream log, 
            TableSchemaManager tableSchemaManager) {
        this.log = log;
        this.tableSchemaManager = tableSchemaManager;
    }

    @Override
    public List<RabbitMessage> processFile(File file) throws IOException, MessageProcessingException {
        try (FileInputStream fis = new FileInputStream(file)) {
            return readBinaryFileContent(fis, file);
        }
    }

    /**
     * Reads and processes the content of a binary file.
     * @throws MessageProcessingException 
     */
    private List<RabbitMessage> readBinaryFileContent(FileInputStream fis, File file) throws IOException, MessageProcessingException {
        List<RabbitMessage> messages = new ArrayList<>();
        
        // Read entire file into hex string
        StringBuffer hexBuffer = new StringBuffer();
        int data;
        while ((data = fis.read()) != -1) {
            hexBuffer.append(StringMethods.pad(Integer.toHexString(data), 2, '0', true));
        }
        
        String rawHexData = hexBuffer.toString();
        if (rawHexData.length() < 16) {
            log.write(LogStream.ERROR_MESSAGE, "Binary file too short: " + file.getName());
            return messages;
        }

        try {
            if (isMultiMessage(rawHexData)) {
                // Process packed message
                List<SingleMessage> singleMessages = splitPackedMsgToArrayList(rawHexData);
                for (SingleMessage singleMsg : singleMessages) {
                    String pdsString = generatePDSFormatStringForSingleMsg(singleMsg);
                    if (pdsString != null && !pdsString.isEmpty()) {
                        RabbitMessage message = new RabbitMessage(
                            singleMsg.getMsgId(),
                            (pdsString + "\n").getBytes("UTF-8")
                        );
                        messages.add(message);
                    }
                }
            } else {
                // Process individual message
                String pdsString = generatePDSFormatStringForIndividualMsg(rawHexData);
                String msgId = getMsgIdForIndividualMsg(rawHexData);
                if (pdsString != null && !pdsString.isEmpty()) {
                    RabbitMessage message = new RabbitMessage(
                        msgId,
                        (pdsString + "\n").getBytes("UTF-8")
                    );
                    messages.add(message);
                }
            }
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Error processing binary file: " + file.getName() + " - " + e.getMessage());
            throw new MessageProcessingException(rawHexData, MessageProcessingException.ErrorType.PROFILE_ERROR, e);
        }

        return messages;
    }

    /**
     * Check if the message is a multi-message (packed message)
     */
    private boolean isMultiMessage(String rawStr) {
        String rawMsg = rawStr.substring(0, 8);
        return "e6030000".equals(rawMsg);
    }

    /**
     * Get message ID from individual message
     */
    private String getMsgIdForIndividualMsg(String rawStr) {
        String rawMsgId = rawStr.substring(0, 8);
        int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawMsgId, true);
        return Integer.valueOf(msgId).toString();
    }

    /**
     * Generate PDS string for individual message
     */
    private String generatePDSFormatStringForIndividualMsg(String rawStr) throws ProfileException {
        String rawMsgId = rawStr.substring(0, 8);
        int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawMsgId, true);
        
        String rawCorrelId = rawStr.substring(8, 16);
        int correlId = ConvertorUtility.ConvertHexBytesToInteger(rawCorrelId, true);
        
        // Skip header 
        String rawMsg = rawStr.substring(16);
        
        return generatePDSFormatString(Integer.valueOf(msgId).toString(), 
                                     Integer.valueOf(correlId).toString(), 
                                     rawMsg);
    }

    /**
     * Generate PDS string for single message
     */
    private String generatePDSFormatStringForSingleMsg(SingleMessage singleMessage) throws ProfileException {
        return generatePDSFormatString(singleMessage.getMsgId(), 
                                     singleMessage.getCorrelid(), 
                                     singleMessage.getMsgContent());
    }

    /**
     * Core PDS generation logic shared by both individual and single message processing
     */
    private String generatePDSFormatString(String msgId, String correlId, String rawMsg) throws ProfileException {
        TableConfigParser.TableDefinition tableDef = tableSchemaManager.getTableDefinitionFromMessageId(msgId);
        int columnCount = tableDef.getColumns().size();
        
        StringBuffer sb = new StringBuffer();
        sb.append(dataInsertTime());
        sb.append(" ");
        sb.append(tableDef.getTabCode());
        sb.append(" ");
        sb.append(correlId);
        sb.append(" ");
        sb.append(columnCount);
        sb.append(" ");
        sb.append("*");
        String parsedStr = parseMsg(rawMsg, correlId, tableDef);
        sb.append(parsedStr);
        
        return sb.toString();
    }

    /**
     * Split packed message to array list
     */
    private List<SingleMessage> splitPackedMsgToArrayList(String rawData) {
        // Skip Msg Id and Correl Id
        rawData = rawData.substring(BinaryMsgFieldLength.MSG_ID + BinaryMsgFieldLength.CORREL_ID);
        
        List<SingleMessage> packedMsgList = new ArrayList<>();
        boolean isInLoop = true;
        while (isInLoop) {
            SingleMessage singleMessage = new SingleMessage();
            int msgId = ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.MSG_ID, PackedMsgIndex.MSG_ID + BinaryMsgFieldLength.MSG_ID), true);
            int correlid = ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.CORRELID, PackedMsgIndex.CORRELID + BinaryMsgFieldLength.CORREL_ID), true);
            int msgLength = 2 * ConvertorUtility.ConvertHexBytesToInteger(rawData.substring(PackedMsgIndex.MSG_LEN, PackedMsgIndex.MSG_LEN + BinaryMsgFieldLength.MSG_LENGTH), true);
            String rawMsgContent = rawData.substring(PackedMsgIndex.MSG_CONTENT, PackedMsgIndex.MSG_CONTENT + msgLength);
            singleMessage.setMsgId(Integer.valueOf(msgId).toString());
            singleMessage.setCorrelid(Integer.valueOf(correlid).toString());
            singleMessage.setMsgLength(Integer.valueOf(msgLength).toString());
            singleMessage.setMsgContent(rawMsgContent);
            rawData = rawData.substring(PackedMsgIndex.MSG_CONTENT + msgLength);
            
            packedMsgList.add(singleMessage);
            
            if ("".equals(rawData.trim())) {
                isInLoop = false;
            }
        }
        
        return packedMsgList;
    }


    /**
     * Parse message according to profile
     */
    private String parseMsg(String msg, String correlid, TableConfigParser.TableDefinition tableDef) {
        StringBuffer sb = new StringBuffer();
        List<TableConfigParser.ColumnDefinition> columns = tableDef.getColumns();
        columns.sort(java.util.Comparator.comparingInt(TableConfigParser.ColumnDefinition::getValueIndex));
        
        for (TableConfigParser.ColumnDefinition column : columns) {
            String value = "";
            
            if ("DTINSERT".equals(column.getColumnName().toUpperCase())) {
                value = "";
            } else if ("CORRELID".equals(column.getColumnName().toUpperCase())) {
                value = correlid;
            } else {
                int startIndex = column.getStartIndex();
                int endIndex = column.getStartIndex() + column.getBinaryLength() * 2;
                try {
                    String splitData = msg.substring(startIndex, endIndex);
                    value = parse(column.getDbColumnType(), splitData);
                } catch (StringIndexOutOfBoundsException e) {
                    log.write(LogStream.WARNING_MESSAGE, e.getMessage());
                    log.write(LogStream.WARNING_MESSAGE, "Column:" + column.getColumnName() + ".Start index:" + startIndex + ".End:" + endIndex);
                    return sb.toString();
                }
            }
            sb.append(tableDef.getSplitter());
            sb.append(value);
        }
        return sb.toString();
    }

    /**
     * Parse individual field data based on column type
     */
    private String parse(String dbColumnType, String data) {
        if (dbColumnType == null || "".equals(dbColumnType.trim())) {
            return "";
        }
        
        if ("SMALLINT".equals(dbColumnType.trim().toUpperCase())) {
            return Integer.valueOf(ConvertorUtility.ConvertHexBytesToShort(data, true)).toString();
        }
        if ("INTEGER".equals(dbColumnType.trim().toUpperCase())) {
            return Integer.valueOf(ConvertorUtility.ConvertHexBytesToInteger(data, true)).toString();
        }
        if ("DOUBLE".equals(dbColumnType.trim().toUpperCase())) {
            Double double1 = Double.valueOf(ConvertorUtility.ConvertHexBytesToDouble(data, true));
            return double1.toString();
        }
        if ("TIMESTAMP".equals(dbColumnType.trim().toUpperCase())) {
            Date date = ConvertorUtility.ConvertHexBytesToDateTime(data, true);
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
            return simpleDateFormat.format(date);
        }
        
        if ("CHAR".equals(dbColumnType.trim().toUpperCase()) || "VARCHAR".equals(dbColumnType.trim().toUpperCase())) {
            String strResultString = data;
            int iStart = 0;
            int iEnd = 0;
            boolean isTruncated = false;
            for (int i = 0; i <= data.length() - 2; i = i + 2) {
                String tmpStr = data.substring(i, i + 2);
                if (tmpStr.toUpperCase().compareTo("1F") < 0 
                        || tmpStr.toUpperCase().compareTo("7F") > 0) {
                    if (isTruncated) {
                        iEnd = i;
                        strResultString = data.substring(iStart, iEnd);
                        break;
                    }
                } else {
                    if (!isTruncated) {
                        iStart = i;
                    }
                    isTruncated = true;
                }
            }
            
            if (!isTruncated) {
                strResultString = "";
            }
            String str = ConvertorUtility.ConvertHexBytesToString(strResultString);
            return str;
        }
        
        return "dbtype:" + dbColumnType + " data:" + data;
    }

    /**
     * Generate current timestamp for data insert
     */
    private String dataInsertTime() {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd-HH.mm.ss.SSSSSS");
        Date date = new Date();
        return simpleDateFormat.format(date);
    }
}







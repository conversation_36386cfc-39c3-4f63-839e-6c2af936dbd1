package wdc.disk.ecs.apps.MQUpload.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import org.junit.jupiter.api.io.TempDir;
import wdc.disk.ecs.apps.SFTPSend.util.TestLogStream;

public class FileOperationServiceTest {

    private TestLogStream logStream; 
    private FileOperationService service;
    private File sendingDir;
    private File sentDir;
    private File problemDir;
    
    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        logStream = new TestLogStream();  // Use TestLogStream directly
        service = new FileOperationService(tempDir.toString(), null, logStream, 1000L, 100);
        sendingDir = new File(tempDir.toString(), "sending");
        sentDir = new File(tempDir.toString(), "sent");
        problemDir = new File(tempDir.toString(), "problem");
    }

    @Test
    void testDirectoryInitialization() {
        assertTrue(sendingDir.exists());
        assertTrue(sentDir.exists());
        assertTrue(problemDir.exists());
    }

    @Test
    void testValidateDestination() {
        assertTrue(service.validateDestination(sendingDir));
        
        // Test with null directory
        assertFalse(service.validateDestination(null));
        
        // Test with non-existent directory
        File nonExistentDir = new File(tempDir.toString(), "nonexistent");
        assertFalse(service.validateDestination(nonExistentDir));
    }

    @Test
    void testMoveToSending() throws IOException {
        File testFile = new File(tempDir.toFile(), "test.txt");
        testFile.createNewFile();

        service.moveFilesToSendingDirectory();

        assertFalse(testFile.exists());
        assertTrue(new File(sendingDir, "test.txt").exists());
    }

    @Test
    void testMoveToSent() throws IOException {
        File testFile = new File(sendingDir, "test.txt");
        testFile.getParentFile().mkdirs();
        testFile.createNewFile();

        service.moveFile(testFile, sentDir.getAbsolutePath());

        assertFalse(testFile.exists());
        assertTrue(new File(sentDir, "test.txt").exists());
    }

    @Test
    void testMoveToProblem() throws IOException {
        File testFile = new File(sendingDir, "test.txt");
        testFile.getParentFile().mkdirs();
        testFile.createNewFile();

        service.moveFile(testFile, problemDir.getAbsolutePath());

        assertFalse(testFile.exists());
        assertTrue(new File(problemDir, "test.txt").exists());
    }

    @Test
    void testMoveWithDuplicateFileName() throws IOException {
        // Create a test file
        File testFile = new File(sendingDir, "test.txt");
        testFile.getParentFile().mkdirs();
        testFile.createNewFile();

        // Create a file with the same name in sent directory
        File sentFile = new File(sentDir, "test.txt");
        sentFile.getParentFile().mkdirs();
        sentFile.createNewFile();

        service.moveFile(testFile, sentDir.getAbsolutePath());

        assertFalse(testFile.exists());
        assertTrue(sentFile.exists());
        // Verify the new file was created with timestamp
        assertEquals(1, sentDir.list((dir, name) -> 
            name.matches("test\\.txt\\.\\d+")).length);
    }

    @Test
    void testIsValidFile() throws IOException {
        File validFile = new File(tempDir.toFile(), "valid.txt");
        validFile.createNewFile();
        // Write some content to make file size >= MINIMUM_FILE_SIZE
        Files.write(validFile.toPath(), "123456789".getBytes());
        
        File invalidFile = new File(tempDir.toFile(), "invalid.txt");
        invalidFile.createNewFile();
        
        assertTrue(FileOperationService.isValidFile(validFile));
        assertFalse(FileOperationService.isValidFile(invalidFile));
        assertFalse(FileOperationService.isValidFile(null));
    }

}
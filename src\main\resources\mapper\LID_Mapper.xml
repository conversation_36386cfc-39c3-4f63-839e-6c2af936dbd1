<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="wdc.disk.ecs.apps.MQUpload.mapper.LID_Mapper">
    <insert id="insertOrUpdateData">
        MERGE INTO DB2SYS.LID AS TARGET
        USING (VALUES (
            <foreach collection="data" index="key" item="value" separator=",">
                #{value}
            </foreach>
        )) AS SOURCE (
            <foreach collection="data" index="key" item="value" separator=",">
                ${key}
            </foreach>
        )
        ON TARGET.LOT = SOURCE.LOT AND TARGET.DT_ENTRY = SOURCE.DT_ENTRY

        WHEN MATCHED THEN
            UPDATE SET 
            <foreach collection="data" index="key" item="value" separator=",">
                <if test="key != 'LOT' and != 'DT_ENTRY'">
                    TARGET.${key} = SOURCE.${key}
                </if>
            </foreach>
            

        WHEN NOT MATCHED THEN
            INSERT (
                <foreach collection="data" index="key" item="value" separator=",">
                    ${key}
                </foreach>
            )
            VALUES (
                <foreach collection="data" index="key" item="value" separator=",">
                    SOURCE.${key}
                </foreach>
            )
    </insert>
</mapper>

package wdc.disk.ecs.apps.MQUpload.model;

import java.util.ArrayList;
import java.util.List;

public class ConsumerProperties {
    private List<QueueConfig> queues;
    private String mybatisConfigPath = "mybatis-config.xml";
    private String parserClassName;
    private String mappingsPath;
    private String profilesPath;

    public ConsumerProperties() {
        this.queues = new ArrayList<>();
    }

    public List<QueueConfig> getQueues() {
        return queues;
    }

    public void setQueues(List<QueueConfig> queues) {
        this.queues = queues;
    }

    public String getMybatisConfigPath() {
        return mybatisConfigPath;
    }
    public void setMybatisConfigPath(String mybatisConfigPath) {
        this.mybatisConfigPath = mybatisConfigPath;
    }

    public String getParserClassName() {
        return parserClassName;
    }

    public void setParserClassName(String parserClassName) {
        this.parserClassName = parserClassName;
    }

    public String getMappingsPath() {
        return mappingsPath;
    }

    public void setMappingsPath(String mappingsPath) {
        this.mappingsPath = mappingsPath;
    }

    public String getProfilesPath() {
        return profilesPath;
    }

    public void setProfilesPath(String profilesPath) {
        this.profilesPath = profilesPath;
    }

    public static class QueueConfig {
        private String name;
        private String description;
        private boolean enabled;
        private int retryCount;
        private long retryDelay;
        private int prefetchCount;
        private int batchSize;
        private long processingInterval;

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getRetryCount() {
            return retryCount;
        }

        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }

        public long getRetryDelay() {
            return retryDelay;
        }

        public void setRetryDelay(long retryDelay) {
            this.retryDelay = retryDelay;
        }

        public int getPrefetchCount() {
            return prefetchCount;
        }

        public void setPrefetchCount(int prefetchCount) {
            this.prefetchCount = prefetchCount;
        }

        public int getBatchSize() {
            return batchSize;
        }

        public void setBatchSize(int batchSize) {
            this.batchSize = batchSize;
        }

        public long getProcessingInterval() {
            return processingInterval;
        }

        public void setProcessingInterval(long processingInterval) {
            this.processingInterval = processingInterval;
        }

       
    }
}

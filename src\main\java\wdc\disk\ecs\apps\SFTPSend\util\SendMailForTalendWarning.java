package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.List;

public class SendMailForTalendWarning extends SendMail {
	
	public SendMailForTalendWarning(String profile, String blockName) {
		super(profile, blockName);
	}

	public SendMailForTalendWarning(String profile) {
		super(profile);
	}

	public void sendTalendWarningMail(List<SendWarningMsgEntity> list) {
		showAtTerminalAndLog("Trying to Write to Mail file");
		File emailFile = null;
		try {
			String body = generateBodyForTalend(list);
			String emailPathDir = getLocalEmailPath();
			File emailDir = new File(emailPathDir);
			if(!emailDir.exists()) {
				emailDir.mkdir();
			}
	
			emailFile = new File(emailDir, getHostName() + "_" + getBlockName() + ".mail");
			writeEmailMsgToFile(emailFile, getToWho(), getSubject(), body);
			showAtTerminalAndLog("Completed write to file.");
		} catch (IOException e) {
			e.printStackTrace();
			getLog().writeExceptionStack(e);
		}
		
		if(!isDebug()) {
			showAtTerminalAndLog("Trying to sent to server.");
			try {
				sendFileToMailServer(emailFile);				
			} catch (Exception e) {
				getLog().writeExceptionStack(e);
				showAtTerminalAndLog("Completed sent to server.");
			}
		}
	}

	private String generateBodyForTalend(List<SendWarningMsgEntity> list) {
		StringBuilder sBuilder = new StringBuilder();
//		sBuilder.append("Host name :" + getHostName() + " \n");
//		sBuilder.append("Mail time:" + (new Date().toString()) + " \n");
		sBuilder.append(String.format("%-20s     %-20s     %-20s", "Folder", "Type", "Reason"));
		sBuilder.append("\n");
		for(int i=0; i<list.size(); i++) {
			sBuilder.append(list.get(i));
			sBuilder.append("\n");
		}
		return sBuilder.toString();
	}

	public String generateBodyForECS(String timeOutMsg,String unprocessQty,String sendingTimeout) {
		StringBuilder sBuilder = new StringBuilder();
//		sBuilder.append("Host name :" + getHostName() + " \n");
//		sBuilder.append("Mail time:" + (new Date().toString()) + " \n");
		sBuilder.append(timeOutMsg);
		sBuilder.append("\n");
		sBuilder.append(unprocessQty);
		sBuilder.append("\n");
		sBuilder.append(sendingTimeout);
		sBuilder.append("\n");
		return sBuilder.toString();
	}
}

package wdc.disk.ecs.apps.MQUpload.rabbitmq;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;


import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;
import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;

import java.io.File;
import java.io.IOException;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

class QueueConfigTest {
    
    private String testConfigPath;
    
    @BeforeEach
    void setUp() throws IOException {
        testConfigPath = System.getProperty("user.dir") + 
            "\\src\\test\\resources\\test-queue-config.csv";
        createTestConfigFile();
    }
 
    
    @Test
    void testLoadFromCsvWithInvalidFile() {
        assertThrows(IOException.class, () -> 
            RabbitMQMetadataLoader.loadFromCsv("non-existent-file.csv")
        );
    }
    
    @Test
    void testLoadFromCsvWithInvalidFormat() throws IOException {
        // Create file with invalid format
        String invalidConfig = "InvalidHeader\naudit_logs,invalid,data";
        File configFile = new File(testConfigPath + ".invalid");
        java.nio.file.Files.write(configFile.toPath(), invalidConfig.getBytes());
        
        RabbitMQMetadataLoader config = RabbitMQMetadataLoader.loadFromCsv(configFile.getPath());
        assertTrue(config.getEntries().isEmpty());
    }
    
    @Test
    void testLoadFromCsv() throws IOException {
        // Add debug logging
        File file = new File(testConfigPath);
        System.out.println("File exists: " + file.exists());
        System.out.println("File length: " + file.length());
        
        // When
        RabbitMQMetadataLoader config = RabbitMQMetadataLoader.loadFromCsv(testConfigPath);
        
        // Debug print entries
        System.out.println("Loaded entries: " + config.getEntries().size());
        for (QueueMetadataEntry entry : config.getEntries()) {
            System.out.println("Entry: " + entry.getName());
        }
        
        // Then
        List<QueueMetadataEntry> entries = config.getEntries();
        assertNotNull(entries);
        assertEquals(2, entries.size());
    }
    
    private void createTestConfigFile() throws IOException {
    	String testConfig = 
    			"Name,Type,Exchange,ExchangeType,RoutingKey,Durable,Exclusive,AutoDelete,QuorumSize\n" +
    					"audit_logs,quorum,ecs.direct,direct,dev_demo,true,false,false,3\n" +
    					"error_logs,classic,ecs.topic,topic,error.*,true,false,false,0";
    	
    	// Add debug logging
    	System.out.println("Creating test file at: " + testConfigPath);
    	System.out.println("Content:\n" + testConfig);
    	
    	File configFile = new File(testConfigPath);
    	configFile.getParentFile().mkdirs();
    	// Always write the file to ensure latest content
    	java.nio.file.Files.write(configFile.toPath(), testConfig.getBytes());
    }
}
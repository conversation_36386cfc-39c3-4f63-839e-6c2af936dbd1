# Overrides for TV Linux development environment
mq_storage_root: '/u/tsd/misc/mq_send/sent'
app_root: '/u/MQUpload'


# Producer Configuration (overrides for TV environment)
producer:
  pds_dir:
    base: mq_storage_root
    relative: ""
  binary_format: true
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  to_dir:
    base: mq_storage_root
    relative: "sent"
  db_msg_id_profile:
    base: app_root
    relative: "profiles/pdsMsgId.pro"
  monitor_interval: 10
  housekeeping:
      enabled: true
      interval_hours: 24
      retention_days: 3
      directories:
        - sent
        - problem

# Environment-specific RabbitMQ configurations
rabbitmq:
    host: "csf-md-rabitmq1.ad.shared"
    port: 5672
    username: "mqupload"
    password: "upload66"
    exchange: "ecs.direct"
    queue_config_path:
      base: app_root
      relative: "config/test-queue-config.csv"
table_schema:
  message_mappings_path:
    base: app_root
    relative: "config/message_mappings.json"
  profile_base_path:
    base: app_root
    relative: "profiles"

consumer:
  mybatis_config_path:
    base: app_root
    relative: "config/mybatis-config.xml"
  mappings_path:
    base: app_root
    relative: "config/message_mappings.json"
  profiles_path:
    base: app_root
    relative: "profiles/generatedProfiles"
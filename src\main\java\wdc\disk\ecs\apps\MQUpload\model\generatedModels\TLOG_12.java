package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TLOG_12
 */
public class TLOG_12  {
    private Short spindle;
    private String errortype;
    private String errorstring;
    private java.time.LocalDateTime testertime;
    private Integer status;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public TLOG_12() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public String getErrortype() {
        return errortype;
    }

    public void setErrortype(String errortype) {
        this.errortype = errortype;
    }

    public String getErrorstring() {
        return errorstring;
    }

    public void setErrorstring(String errorstring) {
        this.errorstring = errorstring;
    }

    public java.time.LocalDateTime getTestertime() {
        return testertime;
    }

    public void setTestertime(java.time.LocalDateTime testertime) {
        this.testertime = testertime;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



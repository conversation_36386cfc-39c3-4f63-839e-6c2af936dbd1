package wdc.disk.ecs.apps.MQUpload.generator;

import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;

import wdc.disk.ecs.apps.MQUpload.schema.TableConfigParser;
import wdc.disk.ecs.apps.MQUpload.schema.TableSchemaManager;

import java.io.*;
import java.util.*;
import java.util.stream.Collectors;

public class PojoAndMapperGenerator {
    private final VelocityEngine ve;
    private final String basePackage;
    private final String messageMappingsPath;

    public PojoAndMapperGenerator(String basePackage, String messageMappingsPath) {
        this.basePackage = basePackage;
        this.messageMappingsPath = messageMappingsPath;
        this.ve = new VelocityEngine();
        Properties props = new Properties();
        props.setProperty("resource.loaders", "classpath");
        props.setProperty("resource.loader.classpath.class", ClasspathResourceLoader.class.getName());
        ve.init(props);
    }

    public void generateFromProfile(String profilePath, String outputDir) throws IOException {
        List<TableConfigParser.ColumnDefinition> columns = TableConfigParser.parseProfileFile(profilePath);
        String rawClassName = extractClassNameFromProfile(profilePath);
        String className = normalizeClassName(rawClassName);
        
        // Generate POJO
        generatePojo(columns, className, outputDir);
        
        // Generate Mapper Interface with columns
        generateMapperInterface(className, outputDir, columns);
    }

    private String normalizeClassName(String rawClassName) {
        return rawClassName.replaceAll("^\\d+", "");
    }

    

    private void generatePojo(List<TableConfigParser.ColumnDefinition> columns, String className, String outputDir) {
        try {
            Template template = ve.getTemplate("templates/Pojo.vm");
            VelocityContext context = new VelocityContext();
            
            context.put("package", basePackage + ".model.generatedModels");
            context.put("className", className);
            context.put("columns", columns);
            
            File dir = new File(outputDir + "/model/generatedModels/");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            String outputPath = outputDir + "/model/generatedModels/" + className + ".java";
            try (Writer writer = new FileWriter(outputPath)) {
                template.merge(context, writer);
            }
            System.out.println("Generated POJO: " + outputPath);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate POJO", e);
        }
    }


        /**
     * Generates a MyBatis Mapper interface from a table definition using a Velocity template.
     * <p>
     * This method orchestrates the creation of a Java interface file (e.g., {@code FTPRAW_Mapper.java})
     * that defines database operations for a specific table. It retrieves the table's schema
     * information, including its name and column details, from the {@link TableSchemaManager}.
     * </p><p>
     * The method separates the table's columns into three lists for use in the Velocity template:
     * <ul>
     *     <li><b>uniqueColumns:</b> Columns that are part of a unique index, typically used for the
     *     {@code ON} clause in a {@code MERGE} or {@code ON DUPLICATE KEY UPDATE} statement.</li>
     *     <li><b>updateColumns:</b> Columns that are not part of the unique index, typically used for
     *     the {@code SET} clause in an {@code UPDATE} statement.</li>
     *     <li><b>allColumns:</b> A complete list of all columns in the table.</li>
     * </ul>
     * It then populates the {@code templates/Mapper.vm} template with this data and writes the
     * resulting Java code to the specified output directory.
     * </p>
     * <p>
     * Note: This method currently uses a hardcoded path ({@code "profiles\\generatedProfiles"}) for locating
     * profile files and ignores the {@code columns} parameter, re-fetching the column data internally.
     * </p>
     *
     * @param className The base name for the generated class (e.g., "FTPRAW"), which is also used
     *                  as the {@code tabCode} to look up the table definition.
     * @param outputDir The root directory where the generated source code will be placed. The mapper
     *                  will be created in a {@code /mapper/generatedMappers/} subdirectory.
     * @param columns   This parameter is currently ignored. The column definitions are fetched directly
     *                  from the {@link TableSchemaManager} using the {@code className}.
     * @throws RuntimeException if the table definition for the given {@code className} cannot be found,
     *                          if the Velocity template cannot be loaded, or if a file I/O error occurs
     *                          during generation.
     */
    private void generateMapperInterface(String className, String outputDir, List<TableConfigParser.ColumnDefinition> columns) {
        try {
            // Initialize TableSchemaManager with the correct paths
            TableSchemaManager schemaManager = TableSchemaManager.getInstance(
                messageMappingsPath,  // message_mappings.json path
                "profiles\\generatedProfiles"  // profile base path
            );
            
            // Get TableDefinition using tabCode (className)
            // This will trigger lazy loading of columns
            TableConfigParser.TableDefinition tableDef = schemaManager.getTableDefinitionFromTabCode(className);
            
            if (tableDef == null) {
                throw new RuntimeException("No table definition found for " + className);
            }
            
            Template template = ve.getTemplate("templates/Mapper.vm");
            VelocityContext context = new VelocityContext();
            
            context.put("package", basePackage + ".mapper.generatedMappers");
            context.put("className", className);
            context.put("modelPackage", basePackage + ".model.generatedModels");
            context.put("tableName", tableDef.getTableName());
            
            // Get unique columns (for ON clause)
            List<TableConfigParser.ColumnDefinition> uniqueColumns = tableDef.getColumns().stream()
                .filter(TableConfigParser.ColumnDefinition::isUniqueIndex)
                .collect(Collectors.toList());
            
            // Get non-unique columns (for UPDATE SET clause)
            List<TableConfigParser.ColumnDefinition> updateColumns = tableDef.getColumns().stream()
                .filter(col -> !col.isUniqueIndex())
                .collect(Collectors.toList());
            
            context.put("uniqueColumns", uniqueColumns);
            context.put("updateColumns", updateColumns);
            context.put("allColumns", tableDef.getColumns());
            
            File dir = new File(outputDir + "/mapper/generatedMappers/");
            if (!dir.exists()) {
                dir.mkdirs();
            }
            
            String outputPath = outputDir + "/mapper/generatedMappers/" + className + "_Mapper.java";
            try (Writer writer = new FileWriter(outputPath)) {
                template.merge(context, writer);
            }
            System.out.println("Generated Mapper: " + outputPath);
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate Mapper Interface", e);
        }
    }

    private String extractClassNameFromProfile(String profilePath) {
        File file = new File(profilePath);
        String fileName = file.getName();
        int firstUnderscoreIndex = fileName.indexOf('_');
        return fileName.substring(firstUnderscoreIndex + 1, fileName.lastIndexOf('.'));
    }

   

    public static void main(String[] args) {
        //"profiles/222_FTPRAW.pro"
        String messageMappingsPath = "src/test/resources/mapper/message_mappings.json";
        List<String> profilePaths = Arrays.asList(
        "profiles/309_LID.pro",
            "profiles/222_FTPRAW.pro",
            "profiles/223_FTPSUM.pro"
        );

        for(String profilePath : profilePaths) {
            try {
                PojoAndMapperGenerator generator = new PojoAndMapperGenerator(
                    "wdc.disk.ecs.apps.MQUpload",
                    messageMappingsPath
                );
                generator.generateFromProfile(profilePath, "src/main/java/wdc/disk/ecs/apps/MQUpload");
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        System.out.println("Generation complete.");
    }
}









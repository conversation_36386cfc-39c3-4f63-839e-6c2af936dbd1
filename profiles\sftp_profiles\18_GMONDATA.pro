*| ColumnName       |  DBColumnType  |  DBColumnLength  |  SystemType       |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  0      |  Y      |  0      |  0      |  
|  DISKID           |  Integer     |  4      |  System.Int32     |  4      |  1      |  Y      |  8      |  1      |  
|  BATCHTIME        |  Timestamp   |  4      |  System.DateTime  |  4      |  2      |  Y      |  16     |  2      |  
|  SURFACE          |  Char        |  1      |  System.String    |  1      |  3      |  Y      |  24     |  3      |  
|  RULE             |  SmallInt    |  2      |  System.Int16     |  2      |  4      |  Y      |  28     |  4      |  
|  GLIDEHEADID      |  Char        |  10     |  System.String    |  10     |  5      |  Y      |  32     |  5      |  
|  HCF              |  Double      |  8      |  System.Double    |  8      |  6      |         |  56     |  6      |  
|  METHOD           |  Char        |  20     |  System.String    |  20     |  7      |         |  72     |  7      |  
|  AMP              |  Double      |  8      |  System.Double    |  8      |  8      |         |  112    |  8      |  
|  DEV              |  Double      |  8      |  System.Double    |  8      |  9      |         |  128    |  9      |  
|  GLIDEHITS        |  Integer     |  4      |  System.Int32     |  4      |  10     |         |  144    |  10     |  
|  BUMPHCF          |  Double      |  8      |  System.Double    |  8      |  11     |         |  152    |  11     |  
|  QCODE            |  Integer     |  4      |  System.Int32     |  4      |  12     |         |  168    |  12     |  
|  RECSENT          |  SmallInt    |  2      |  System.Int16     |  2      |  13     |         |  176    |  13     |  
|  DTINSERT         |  Timestamp   |  4      |  System.DateTime  |  4      |  14     |  Y      |  0      |  14     |  
|  CORRELID         |  Integer     |  4      |  System.Int32     |  4      |  15     |         |  0      |  15     |  


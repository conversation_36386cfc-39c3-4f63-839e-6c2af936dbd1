package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.GLDPARM_37;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for GLDPARM_37
 */
public interface GLDPARM_37_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.GLDPARM AS TARGET",
        "USING (VALUES (",
        "    #{gate},",
        "    #{elemname},",
        "    #{id},",
        "    #{half},",
        "    #{centerfreq},",
        "    #{parmnum},",
        "    #{slope},",
        "    #{offset},",
        "    #{corrcoeff},",
        "    #{maxdev},",
        "    #{avgdev},",
        "    #{polyc3},",
        "    #{polyc2},",
        "    #{polyc1},",
        "    #{polyc0},",
        "    #{polyr2},",
        "    #{spindle},",
        "    #{calibtime},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    GATE,",
        "    ELEMNAME,",
        "    ID,",
        "    HALF,",
        "    CENTERFREQ,",
        "    PARMNUM,",
        "    SLOPE,",
        "    OFFSET,",
        "    CORRCOEFF,",
        "    MAXDEV,",
        "    AVGDEV,",
        "    POLYC3,",
        "    POLYC2,",
        "    POLYC1,",
        "    POLYC0,",
        "    POLYR2,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.GATE = SOURCE.GATE AND TARGET.ELEMNAME = SOURCE.ELEMNAME AND TARGET.ID = SOURCE.ID AND TARGET.HALF = SOURCE.HALF AND TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.CALIBTIME = SOURCE.CALIBTIME",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.CENTERFREQ = SOURCE.CENTERFREQ,",
        "    TARGET.PARMNUM = SOURCE.PARMNUM,",
        "    TARGET.SLOPE = SOURCE.SLOPE,",
        "    TARGET.OFFSET = SOURCE.OFFSET,",
        "    TARGET.CORRCOEFF = SOURCE.CORRCOEFF,",
        "    TARGET.MAXDEV = SOURCE.MAXDEV,",
        "    TARGET.AVGDEV = SOURCE.AVGDEV,",
        "    TARGET.POLYC3 = SOURCE.POLYC3,",
        "    TARGET.POLYC2 = SOURCE.POLYC2,",
        "    TARGET.POLYC1 = SOURCE.POLYC1,",
        "    TARGET.POLYC0 = SOURCE.POLYC0,",
        "    TARGET.POLYR2 = SOURCE.POLYR2,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    GATE,",
        "    ELEMNAME,",
        "    ID,",
        "    HALF,",
        "    CENTERFREQ,",
        "    PARMNUM,",
        "    SLOPE,",
        "    OFFSET,",
        "    CORRCOEFF,",
        "    MAXDEV,",
        "    AVGDEV,",
        "    POLYC3,",
        "    POLYC2,",
        "    POLYC1,",
        "    POLYC0,",
        "    POLYR2,",
        "    SPINDLE,",
        "    CALIBTIME,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.GATE,",
        "    SOURCE.ELEMNAME,",
        "    SOURCE.ID,",
        "    SOURCE.HALF,",
        "    SOURCE.CENTERFREQ,",
        "    SOURCE.PARMNUM,",
        "    SOURCE.SLOPE,",
        "    SOURCE.OFFSET,",
        "    SOURCE.CORRCOEFF,",
        "    SOURCE.MAXDEV,",
        "    SOURCE.AVGDEV,",
        "    SOURCE.POLYC3,",
        "    SOURCE.POLYC2,",
        "    SOURCE.POLYC1,",
        "    SOURCE.POLYC0,",
        "    SOURCE.POLYR2,",
        "    SOURCE.SPINDLE,",
        "    SOURCE.CALIBTIME,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(GLDPARM_37 record);
}











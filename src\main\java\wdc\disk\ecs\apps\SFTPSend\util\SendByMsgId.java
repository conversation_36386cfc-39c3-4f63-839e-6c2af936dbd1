package wdc.disk.ecs.apps.SFTPSend.util;

import com.jcraft.jsch.SftpException;

import ibm.disk.utility.LogStream;

public class SendByMsgId implements SendMethod {

	@Override
	public void process(SendPdsToServerService service, String serverDirName,String msgId, int fileIndex,
			String srcFilePath,LogStream log) throws SftpException {
		String fileSeparater = "/";
		String serverDir_msgId_dir = serverDirName + fileSeparater + msgId;
		service.makeDirFromChannel(serverDir_msgId_dir);
		service.getChannel().put(srcFilePath, serverDir_msgId_dir);
	}

}

*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_START              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  DT_END                |  Timestamp   |  4      |  System.DateTime       |  4      |  2      |         |  0      |  2      |  
|  INCASSETTE            |  Char        |  11     |  System.String         |  11     |  3      |         |  0      |  3      |  
|  OUTCASSETTE           |  Char        |  11     |  System.String         |  11     |  4      |         |  0      |  4      |  
|  INLOT                 |  Char        |  10     |  System.String         |  10     |  5      |         |  0      |  5      |  
|  OUTLOT                |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  INSLOT                |  Char        |  2      |  System.String         |  2      |  7      |         |  0      |  7      |  
|  OUTSLOT               |  Char        |  2      |  System.String         |  2      |  8      |         |  0      |  8      |  
|  UNITID                |  Char        |  12     |  System.String         |  12     |  9      |         |  0      |  9      |  
|  CELL_ID               |  Integer     |  4      |  System.Int32          |  4      |  10     |         |  0      |  10     |  
|  TESTER_ID             |  Integer     |  4      |  System.Int32          |  4      |  11     |         |  0      |  11     |  
|  RULE                  |  Integer     |  4      |  System.Int32          |  4      |  12     |         |  0      |  12     |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  13     |         |  0      |  13     |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  14     |         |  0      |  14     |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  15     |         |  0      |  15     |  
|  EMPLOYEE              |  Char        |  10     |  System.String         |  10     |  16     |         |  0      |  16     |  
|  GRADE                 |  Char        |  2      |  System.String         |  2      |  17     |         |  0      |  17     |  
|  ROBOT_CLASS           |  Char        |  1      |  System.String         |  1      |  18     |         |  0      |  18     |  
|  RQ_HOSTNAME           |  Char        |  10     |  System.String         |  10     |  19     |         |  0      |  19     |  
|  RQ_IP                 |  Char        |  15     |  System.String         |  15     |  20     |         |  0      |  20     |  
|  DISK_IMAGE_TOP        |  VarChar     |  80     |  System.String         |  80     |  21     |         |  0      |  21     |  
|  DISK_IMAGE_BOT        |  VarChar     |  80     |  System.String         |  80     |  22     |         |  0      |  22     |  
|  DISK_LOG              |  VarChar     |  80     |  System.String         |  80     |  23     |         |  0      |  23     |  

package wdc.disk.ecs.apps.MQUpload.schema;

import static org.junit.jupiter.api.Assertions.*;


import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;

import java.util.List;


import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;


class TableSchemaManagerTest {
    
    @TempDir
    Path tempDir;
    
    private TableSchemaManager schemaManager;
    private String messageMappingsPath;
    private String profileBasePath;
    
    @BeforeEach
    void setUp() throws IOException {
        // Create temporary test files
        messageMappingsPath = tempDir.resolve("message_mappings.json").toString();
        profileBasePath = tempDir.resolve("profiles").toString();
        Files.createDirectories(Path.of(profileBasePath));
        
        // Create sample message mappings JSON
        String jsonContent = "{"
            + "\"messageProfiles\": ["
            + "    {"
            + "        \"tabCode\": \"CODE1\","
            + "        \"mqMessageId\": \"MSG1\","
            + "        \"msgType\": \"ASCII\","
            + "        \"tableName\": \"TABLE1\""
            + "    },"
            + "    {"
            + "        \"tabCode\": \"CODE2\","
            + "        \"mqMessageId\": \"MSG2\","
            + "        \"msgType\": \"ASCII\","
            + "        \"tableName\": \"TABLE2\""
            + "    }"
            + "]"
            + "}";
        
        Files.writeString(Path.of(messageMappingsPath), jsonContent);
        
        // Create profile files for column definitions
        createProfileFile("MSG1_CODE1", Arrays.asList(
            "| ColumnName | DBColumnType | DBColumnLength | SystemType | BinaryLength | ColumnNo | NULLS | StartIndex | ValueIndex |",
            "|-----------+-------------+---------------+-----------+-------------+---------+--------+------------+------------|",
            "| COL1      | String      | 10            | String    | 10          | 1       | Y      | 0          | 0          |",
            "| COL2      | Integer     | 4             | Int32     | 4           | 2       | N      | 10         | 1          |"
        ));
        
        createProfileFile("MSG2_CODE2", Arrays.asList(
            "| ColumnName | DBColumnType | DBColumnLength | SystemType | BinaryLength | ColumnNo | NULLS | StartIndex | ValueIndex |",
            "|-----------+-------------+---------------+-----------+-------------+---------+--------+------------+------------|",
            "| COL3      | DateTime    | 8             | DateTime  | 8           | 1       | Y      | 0          | 0          |"
        ));
        
        // Initialize schema manager
        schemaManager = TableSchemaManager.getInstance(messageMappingsPath, profileBasePath);
    }
    
    private void createProfileFile(String filename, List<String> content) throws IOException {
        Path filePath = Path.of(profileBasePath, filename + ".pro");
        Files.write(filePath, content);
    }
    
    @Test
    void testGetInstance() {
        // Test singleton pattern
        TableSchemaManager instance1 = TableSchemaManager.getInstance(messageMappingsPath, profileBasePath);
        TableSchemaManager instance2 = TableSchemaManager.getInstance(messageMappingsPath, profileBasePath);
        
        assertSame(instance1, instance2, "getInstance should return the same instance");
    }
    
    @Test
    void testTableDefinitionsLoadedAtStartup() {
        // Verify table definitions are loaded during initialization
        assertDoesNotThrow(() -> schemaManager.getTableDefinitionFromTabCode("CODE1"));
        assertDoesNotThrow(() -> schemaManager.getTableDefinitionFromTabCode("CODE2"));
    }
    
    @Test
    void testColumnsLazyLoading() {
        // Get table definition first time - should trigger column loading
        TableConfigParser.TableDefinition def1 = schemaManager.getTableDefinitionFromTabCode("CODE1");
        assertNotNull(def1.getColumns());
        assertFalse(def1.getColumns().isEmpty());
        assertEquals(2, def1.getColumns().size());
        
        // Get same definition again - should use cached columns
        TableConfigParser.TableDefinition def2 = schemaManager.getTableDefinitionFromTabCode("CODE1");
        assertSame(def1, def2);
        assertSame(def1.getColumns(), def2.getColumns());
    }
    
    @Test
    void testNonExistentTabCode() {
        SchemaException exception = assertThrows(SchemaException.class,
            () -> schemaManager.getTableDefinitionFromTabCode("NONEXISTENT"));
        assertEquals("No schema found for tabCode: NONEXISTENT", exception.getMessage());
    }
    
    @Test
    void testInvalidProfileFormat() throws IOException {
        // Create invalid profile file
        createProfileFile("MSG1_CODE1", Arrays.asList(
            "Invalid format",
            "No header line"
        ));
        
        SchemaException exception = assertThrows(SchemaException.class,
            () -> schemaManager.getTableDefinitionFromTabCode("CODE1"));
        assertTrue(exception.getMessage().contains("Failed to load column definitions"));
    }
    
    @Test
    void testConcurrentColumnLoading() throws InterruptedException {
        // Test concurrent access to column loading
        Runnable task = () -> {
            for (int i = 0; i < 100; i++) {
                TableConfigParser.TableDefinition def = 
                    schemaManager.getTableDefinitionFromTabCode("CODE1");
                assertNotNull(def.getColumns());
                assertFalse(def.getColumns().isEmpty());
            }
        };
        
        Thread thread1 = new Thread(task);
        Thread thread2 = new Thread(task);
        
        thread1.start();
        thread2.start();
        
        thread1.join();
        thread2.join();
    }
    
    @Test
    void testColumnDefinitionContent() {
        TableConfigParser.TableDefinition def = schemaManager.getTableDefinitionFromTabCode("CODE1");
        List<TableConfigParser.ColumnDefinition> columns = def.getColumns();
        
        assertEquals(2, columns.size());
        assertEquals("COL1", columns.get(0).getColumnName());
        assertEquals("String", columns.get(0).getDbColumnType());
        assertEquals("COL2", columns.get(1).getColumnName());
        assertEquals("Integer", columns.get(1).getDbColumnType());
    }
}






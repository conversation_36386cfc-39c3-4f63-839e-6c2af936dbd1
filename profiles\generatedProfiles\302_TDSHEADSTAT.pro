* Table: DB2SYS.TDS_HEAD_STAT (ASCII format)
*| ColumnName       | DBColType |DBColLen|  SysType        |BinLength|ColNo|NULLS |StartIdx  |ValueIdx| UniIdx | 
|  DTINSERT         | TIMESTAMP | 10     | System.DateTime |  0      | 0   | N    |  0       |  0     |        |
|  MEAS_TIME        | TIMESTAMP | 10     | System.DateTime |  0      | 1   | N    |  0       |  1     | Y      |
|  SPINDLE          | INTEGER   | 4      | System.Int32    |  0      | 2   | N    |  0       |  2     | Y      |
|  HEAD_ID          | VARCHAR   | 10     | System.String   |  0      | 3   | Y    |  0       |  3     | Y      |
|  TDS_ACTIVE       | INTEGER   | 4      | System.Int32    |  0      | 4   | Y    |  0       |  4     |        |
|  MR_ACTIVE        | INTEGER   | 4      | System.Int32    |  0      | 5   | Y    |  0       |  5     |        |
|  MR_AMP_MV        | REAL      | 4      | System.Double   |  0      | 6   | Y    |  0       |  6     |        |
|  TDS_RMS_MV       | REAL      | 4      | System.Double   |  0      | 7   | Y    |  0       |  7     |        |
|  PRESCAN_TDS_COUNT | REAL      | 4      | System.Double   |  0      | 8   | Y    |  0       |  8     |        |
|  TOUCHDOWN        | REAL      | 4      | System.Double   |  0      | 9   | Y    |  0       |  9     |        |
|  MR_GAIN          | REAL      | 4      | System.Double   |  0      | 10  | Y    |  0       |  10    |        |
|  MR_BIAS          | REAL      | 4      | System.Double   |  0      | 11  | Y    |  0       |  11    |        |
|  WR_RESISTANCE_OHM | REAL      | 4      | System.Double   |  0      | 12  | Y    |  0       |  12    |        |
|  MR_RESISTANCE_OHM | REAL      | 4      | System.Double   |  0      | 13  | Y    |  0       |  13    |        |
|  TDS_RESISTANCE_OHM | REAL      | 4      | System.Double   |  0      | 14  | Y    |  0       |  14    |        |
|  DFH_RESISTANCE_OHM | REAL      | 4      | System.Double   |  0      | 15  | Y    |  0       |  15    |        |
|  TDS_RESISTANCE_PRE_SCAN_OHM | REAL      | 4      | System.Double   |  0      | 16  | Y    |  0       |  16    |        |
|  HSA_ID_AUTO      | VARCHAR   | 25     | System.String   |  0      | 17  | Y    |  0       |  17    |        |
|  TOUCHDOWN_INITIAL | REAL      | 4      | System.Double   |  0      | 18  | Y    |  0       |  18    |        |
|  TDS_BIAS_MODE    | CHARACTER | 2      | System.String   |  0      | 19  | Y    |  0       |  19    |        |
|  TDS_BIAS         | REAL      | 4      | System.Double   |  0      | 20  | Y    |  0       |  20    |        |
|  TDS_POSITIVE_THRESHOLD_AVALANCHE | REAL      | 4      | System.Double   |  0      | 21  | Y    |  0       |  21    |        |
|  TDS_NEGATIVE_THRESHOLD_AVALANCHE | REAL      | 4      | System.Double   |  0      | 22  | Y    |  0       |  22    |        |
|  PSAT_LO_GRASS_THRESHOLD_OFFSET | REAL      | 4      | System.Double   |  0      | 23  | Y    |  0       |  23    |        |
|  PSAT_HI_GRASS_THRESHOLD_OFFSET | REAL      | 4      | System.Double   |  0      | 24  | Y    |  0       |  24    |        |

// package wdc.disk.ecs.apps.MQUpload.consumer;
// import static org.junit.jupiter.api.Assertions.*;
// import static org.mockito.ArgumentMatchers.*;
// import static org.mockito.Mockito.*;

// import java.io.IOException;
// import java.util.ArrayList;
// import java.util.Arrays;
// import java.util.Collections;
// import java.util.List;
// import java.util.concurrent.TimeUnit;
// import java.util.concurrent.TimeoutException;

// import org.junit.jupiter.api.AfterEach;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.junit.jupiter.api.Timeout;
// import org.junit.jupiter.api.extension.ExtendWith;
// import org.mockito.Mock;
// import org.mockito.MockedStatic;
// import org.mockito.MockitoAnnotations;
// import org.mockito.junit.jupiter.MockitoExtension;
// import org.mockito.ArgumentCaptor;
// import org.mockito.InOrder;

// import com.rabbitmq.client.*;

// import ibm.disk.utility.LogStream;
// import wdc.disk.ecs.apps.MQUpload.model.ConsumerProperties;
// import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;
// import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;
// import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
// import wdc.disk.ecs.apps.MQUpload.processor.BatchMessageProcessor;

// @ExtendWith(MockitoExtension.class)
// class RabbitMQConsumerTest {

//     @Mock
//     private ConnectionFactory connectionFactory;
    
//     @Mock
//     private Connection connection;
    
//     @Mock
//     private Channel channel;
    
//     @Mock
//     private LogStream logStream;
    
//     private RabbitMQProperties mqProps;
//     private ConsumerProperties consumerProps;
//     private RabbitMQConsumer consumer;
//     private MockedStatic<RabbitMQMetadataLoader> queueConfigMock;
//     private List<QueueMetadataEntry> entries;
//     private ShutdownSignalException mockShutdownSignal;
//     private static final int TEST_TIMEOUT = 5000; // 5 seconds timeout for tests

//     @BeforeEach
//     void setUp() throws IOException, TimeoutException {
//         MockitoAnnotations.openMocks(this);

//         // Setup RabbitMQ properties
//         mqProps = new RabbitMQProperties();
//         mqProps.setRabbitMQHost("localhost");
//         mqProps.setRabbitMQPort(5672);
//         mqProps.setRabbitMQUsername("guest");
//         mqProps.setRabbitMQPassword("guest");
//         mqProps.setRabbitMQQueueConfigPath("test-queue-config.csv");
        
//         // Setup consumer properties with retry configuration
//         consumerProps = new ConsumerProperties();
//         ConsumerProperties.QueueConfig queueConfig = new ConsumerProperties.QueueConfig();
//         queueConfig.setName("test-queue");
//         queueConfig.setEnabled(true);
//         queueConfig.setRetryCount(3);
//         queueConfig.setRetryDelay(100); // Short delay for tests
//         consumerProps.setQueues(Collections.singletonList(queueConfig));

//         // Setup queue entries
//         entries = new ArrayList<>();
//         QueueMetadataEntry entry = new QueueMetadataEntry();
//         entry.setName("test-queue");
//         entry.setType("classic");
//         entry.setExchange("test-exchange");
//         entry.setExchangeType("direct");
//         entry.setRoutingKey("test.key");
//         entry.setDurable(true);
//         entry.setExclusive(false);
//         entry.setAutoDelete(false);
//         entries.add(entry);
        
//         // Setup shutdown signal mock
//         mockShutdownSignal = mock(ShutdownSignalException.class);
//     }

//     private void setupSuccessfulConnectionMocks() throws IOException, TimeoutException {
//         // Setup connection factory mock for successful connection
//         when(connectionFactory.newConnection()).thenReturn(connection);
//         when(connection.createChannel()).thenReturn(channel);
//     }

//     private void setupQueueMetadataLoaderMocks() {
//         queueConfigMock = mockStatic(RabbitMQMetadataLoader.class);
//         RabbitMQMetadataLoader metadataLoader = mock(RabbitMQMetadataLoader.class);
//         when(metadataLoader.getEntries()).thenReturn(entries);
//         queueConfigMock.when(() -> RabbitMQMetadataLoader.loadFromCsv(anyString()))
//                 .thenReturn(metadataLoader);
//     }

//     @Test
//     @Timeout(value = TEST_TIMEOUT, unit = TimeUnit.MILLISECONDS)
//     void testAutoRecoveryConfiguration() throws IOException, TimeoutException {
//         setupQueueMetadataLoaderMocks();
//         setupSuccessfulConnectionMocks();
        
//         try (RabbitMQConsumer consumer = new RabbitMQConsumer(mqProps, consumerProps, logStream, connectionFactory, mock(BatchMessageProcessor.class))) {
//             // Verify auto recovery settings
//             verify(connectionFactory).setAutomaticRecoveryEnabled(true);
//             verify(connectionFactory).setNetworkRecoveryInterval(anyLong());
//             verify(connectionFactory).setConnectionTimeout(anyInt());
            
//             // Verify connection was created
//             verify(connectionFactory).newConnection();
//             verify(connection).createChannel();
//         } catch (Exception e) {
//             throw new RuntimeException("Error during consumer operation or closing", e);
//         }
//     }

//     /**
//      * Tests the RabbitMQ connection failure and automatic retry mechanism.
//      * 
//      * This test verifies that:
//      * 1. The consumer successfully establishes initial connection
//      * 2. When connection fails, the consumer detects it
//      * 3. The consumer attempts to reconnect
//      * 4. The reconnection is successful
//      * 
//      * Test sequence:
//      * - Sets up initial successful connection
//      * - Simulates connection failure using isOpen() state changes
//      * - Triggers shutdown event
//      * - Verifies reconnection attempts and logging
//      * 
//      * @throws IOException if there are I/O errors during connection
//      * @throws TimeoutException if connection timeout occurs
//      */
//     @Test
//     @Timeout(value = TEST_TIMEOUT, unit = TimeUnit.MILLISECONDS)
//     void testConnectionFailureWithRetry() throws IOException, TimeoutException {
//         setupQueueMetadataLoaderMocks();
//         setupSuccessfulConnectionMocks();
        
//         // Setup connection mock to simulate failure after initial connection
//         when(connection.isOpen())
//             .thenReturn(true)
//             .thenReturn(false)  // simulate connection failure
//             .thenReturn(true);  // connection restored
            
//         try (RabbitMQConsumer consumer = new RabbitMQConsumer(mqProps, consumerProps, logStream, connectionFactory, mock(BatchMessageProcessor.class))) {
//             try {
//                 // Capture the shutdown listener
//                 ArgumentCaptor<ShutdownListener> shutdownListenerCaptor = ArgumentCaptor.forClass(ShutdownListener.class);
//                 verify(connection).addShutdownListener(shutdownListenerCaptor.capture());
                
//                 // Simulate connection failure
//                 ShutdownSignalException shutdownSignal = mock(ShutdownSignalException.class);
//                 when(shutdownSignal.isInitiatedByApplication()).thenReturn(false);
//                 shutdownListenerCaptor.getValue().shutdownCompleted(shutdownSignal);
                
//                 // Verify retry behavior
//                 verify(logStream).write(eq(LogStream.WARNING_MESSAGE), contains("Attempting to reconnect"));
//                 verify(logStream).write(eq(LogStream.INFORMATION_MESSAGE), contains("Successfully reconnected"));
//                 verify(connectionFactory, atLeast(2)).newConnection();
//             } finally {
//                 consumer.shutdown();
//             }
//         } catch (Exception e) {
//             throw new RuntimeException("Error during consumer operation or closing", e);
//         }
//     }

//     @Test
//     void testConnectionFailureMaxRetries() throws IOException, TimeoutException {
//         // Setup connection factory to fail consistently
//         when(connectionFactory.newConnection())
//             .thenThrow(new IOException("Connection failed"));

//         // Expect RuntimeException on constructor
//         RuntimeException exception = assertThrows(RuntimeException.class, () -> {
//             consumer = new RabbitMQConsumer(mqProps, consumerProps, logStream, connectionFactory, mock(BatchMessageProcessor.class));
//         });

//         // Verify the exception message
//         assertEquals("Failed to create RabbitMQ connection", exception.getMessage());
        
//         // Verify connection was attempted once
//         verify(connectionFactory, times(1)).newConnection();
//         verify(logStream).write(eq(LogStream.ERROR_MESSAGE), contains("Failed to create connection"));
//     }

//     @Test
//     void testShutdownListenerHandling() throws IOException, TimeoutException {
//         setupQueueMetadataLoaderMocks();
//         setupSuccessfulConnectionMocks();
//         // Capture the shutdown listener
//         ArgumentCaptor<ShutdownListener> shutdownListenerCaptor = ArgumentCaptor.forClass(ShutdownListener.class);
        
//         consumer = new RabbitMQConsumer(mqProps, consumerProps, logStream, connectionFactory, mock(BatchMessageProcessor.class));
//         verify(connection).addShutdownListener(shutdownListenerCaptor.capture());
        
//         // Simulate unexpected shutdown
//         when(mockShutdownSignal.isInitiatedByApplication()).thenReturn(false);
//         shutdownListenerCaptor.getValue().shutdownCompleted(mockShutdownSignal);
        
//         verify(logStream).write(eq(LogStream.ERROR_MESSAGE), contains("Connection was closed unexpectedly"));
//     }

//     @Test
//     void testCloseWithResources() throws Exception {
//         setupQueueMetadataLoaderMocks();
//         setupSuccessfulConnectionMocks();

//         when(connection.isOpen()).thenReturn(true);
//         when(channel.isOpen()).thenReturn(true);

//         try {
//             // Create consumer and ensure channel is created
//             consumer = new RabbitMQConsumer(mqProps, consumerProps, logStream, connectionFactory, mock(BatchMessageProcessor.class));
            
//             verify(channel).addShutdownListener(any(ShutdownListener.class));
            
//             // Close the consumer
//             consumer.close();
            
//             // Verify resources are closed in correct order
//             InOrder inOrder = inOrder(channel, connection);
//             inOrder.verify(channel).close();
//             inOrder.verify(connection).close();
//             verify(logStream).write(eq(LogStream.INFORMATION_MESSAGE), contains("RabbitMQ consumer closed"));
//         } catch (Exception e) {
//             fail("Exception should not be thrown during normal close operation: " + e.getMessage());
//         }
//     }

//     @Test
//     @Timeout(value = TEST_TIMEOUT, unit = TimeUnit.MILLISECONDS)
//     void testMessageConsumption() throws IOException, TimeoutException {
//         setupQueueMetadataLoaderMocks();
//         setupSuccessfulConnectionMocks();
        
//         // Capture the DeliverCallback
//         ArgumentCaptor<DeliverCallback> deliverCallbackCaptor = ArgumentCaptor.forClass(DeliverCallback.class);

//         try (RabbitMQConsumer consumer = new RabbitMQConsumer(mqProps, consumerProps, logStream, connectionFactory, mock(BatchMessageProcessor.class))) {
//             // Verify basicConsume was called
//             verify(channel).basicConsume(
//                 anyString(),
//                 eq(false),
//                 deliverCallbackCaptor.capture(),
//                 any(CancelCallback.class)
//             );

//             // Simulate message delivery
//             DeliverCallback callback = deliverCallbackCaptor.getValue();
//             Envelope envelope = mock(Envelope.class);
//             when(envelope.getDeliveryTag()).thenReturn(1L);
            
//             callback.handle("consumerTag", 
//                 new Delivery(envelope, null, "test message".getBytes()));

//             // Verify message processing
//             verify(channel).basicAck(eq(1L), eq(false));
//         } catch (Exception e) {
//             throw new RuntimeException("Error during consumer operation or closing", e);
//         }
//     }

//     @AfterEach
//     void tearDown() {
//         if (queueConfigMock != null) {
//             queueConfigMock.close();
//         }
//     }

//     private Delivery createDelivery(String message) {
//         Envelope envelope = new Envelope(1L, false, "", "");
//         AMQP.BasicProperties properties = new AMQP.BasicProperties();
//         return new Delivery(envelope, properties, message.getBytes());
//     }
// }

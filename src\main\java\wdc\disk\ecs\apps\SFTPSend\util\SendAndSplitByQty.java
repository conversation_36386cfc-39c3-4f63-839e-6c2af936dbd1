package wdc.disk.ecs.apps.SFTPSend.util;

import com.jcraft.jsch.SftpException;

import ibm.disk.utility.LogStream;

public class SendAndSplitByQty implements SendMethod {

	//Mode 3
	@Override
	public void process(SendPdsToServerService service, String serverDirName,String msgId, int fileIndex,
			String srcFilePath, LogStream log) throws SftpException {
		log.write("Enter SendAndSplitByQty.");
		String fileSeparater = "/";
		String serverDir_current = "";
		String folderNameString = service.getFolderNameString();
		if(folderNameString != null && !folderNameString.isEmpty()) {
			 serverDir_current = serverDirName;
			 String[] split = folderNameString.split(fileSeparater);
			 for(int i=0; i< split.length; i++) {
				 serverDir_current += fileSeparater + split[i];
				 service.makeDirFromChannel(serverDir_current);
			 }
		}else {
			serverDir_current = serverDirName + fileSeparater + "current";	
			service.makeDirFromChannel(serverDir_current);			
		}
		log.write("MakeDirFromChannel completed." + serverDir_current);
		service.checkSessionAndChannelAlive();
		service.getChannel().put(srcFilePath, serverDir_current);
		log.write("Put file " + srcFilePath + "completed.");
		if(fileIndex % SendPdsToServerService.RenameRate == 0) {
			boolean exceedFileQty = service.compareProfileQtyAndServerFileQty(msgId, serverDir_current);
			if(exceedFileQty) {
				log.write("Exceed file quantity.");
				if(folderNameString != null && !folderNameString.isEmpty()) {
					service.renameMsgCurrentDir(serverDirName,serverDir_current,fileSeparater, folderNameString);					
				}else {
					service.renameMsgCurrentDir(serverDirName,serverDir_current,fileSeparater, msgId);	
				}
				log.write("Rename dir completed.");
			}			
		}

	}

}

package wdc.disk.ecs.apps.MQUpload.generator;


import java.io.FileWriter;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

import wdc.disk.ecs.apps.MQUpload.util.DataTypeMapper;

import java.util.ArrayList;
import java.util.List;

/**
 * Utility class to generate message profiles from DB2 table definitions
 */
public class MsgProfileGenerator {
    // DB Connection properties 
    private static final String DB_DRIVER = "com.ibm.db2.jcc.DB2Driver";
    private static final String DB_URL = "***************************************************";
    private static final String DB_USERNAME = "ndutld";
    private static final String DB_PASSWORD = "Itc@me123";
    

    
    /**
     * Generate profile for a specific table
     * @param schema Schema name
     * @param tableName Table name
     * @param outputFilePath Output file path
     * @throws Exception If any error occurs
     */
    public void generateBinaryProfile(String schema, String tableName, String outputFilePath) throws Exception {
        try (Connection conn = getConnection();
             FileWriter writer = new FileWriter(outputFilePath)) {
            
            // Write table information
            writer.write(String.format("* Table: %s.%s (Binary format)\n", schema, tableName));          
            // Write header
            writer.write("*| ColumnName       | DBColType |DBColLen|  SysType        |BinLength |  ColNo  |  NULLS  |StartIdx  |ValueIdx  | UniIdx | \n");
            
            // Query column definitions
            String sql = "SELECT * FROM SYSCAT.COLUMNS WHERE TABSCHEMA = ? AND TABNAME = ? ORDER BY COLNO";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, schema);
                stmt.setString(2, tableName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    int columnNo = 0;
                    int startIndex = 0;
                    List<String> uniqueIndexes = getUniqueIndexes(schema, tableName); // Get unique indexes for the table
                    
                    while (rs.next()) {
                        String columnName = rs.getString("COLNAME");
                        String dbType = rs.getString("TYPENAME").toUpperCase();
                        int dbLength = rs.getInt("LENGTH");
                        String nulls = rs.getString("NULLS");
                        
                        // Map to system type
                        String systemType = DataTypeMapper.getSystemType(dbType);
                        
                        // Special case for DISKSEQUENCE
                        int binaryLength;
                        if (columnName.equals("DISKSEQUENCE") && dbType.equals("INTEGER")) {
                            systemType = "System.Int64";
                            binaryLength = 2;
                        } else {
                            binaryLength = DataTypeMapper.getBinaryLength(dbType, dbLength);
                        }
                        
                        // Check if the column is part of a unique index and add a flag if so
                        String uniIdx = uniqueIndexes.contains(columnName) ? "Y" : "N"; // Y if column is part of a unique index, N otherwise
                        
                        // Format and write the column definition
                        //"*                             | ColumnNa | Type |DBColLen|SysType|BinLengt| ColNo |NULLS  |StartIdx  |ValueIdx  | UniIdx |
                        writer.write(String.format("|  %-16s | %-9s | %-4d   | %-15s |  %-6d  |  %-6d |  %-2s |  %-6d  |  %-6d  | %-6s |\n",
                                columnName, dbType, dbLength, systemType, binaryLength, columnNo, nulls, startIndex, columnNo, uniIdx));
                        
                        // Update startIndex for next column
                        startIndex += binaryLength;
                        columnNo++;
                    }
                }
            }
            
            System.out.println("Profile generated successfully: " + outputFilePath);
        }
    }

    /**
     * Generate profile for a specific table
     * @param schema Schema name
     * @param tableName Table name
     * @param outputFilePath Output file path
     * @throws Exception If any error occurs
     */
    public void generateAsciiProfile(String schema, String tableName, String outputFilePath) throws Exception {
        try (Connection conn = getConnection();
             FileWriter writer = new FileWriter(outputFilePath)) {
            
            // Write table information
            writer.write(String.format("* Table: %s.%s (ASCII format)\n", schema, tableName));
            // Write header
            writer.write("*| ColumnName       | DBColType |DBColLen|  SysType        |BinLength|ColNo|NULLS |StartIdx  |ValueIdx| UniIdx | \n");

            // Query column definitions
            String sql = "SELECT * FROM SYSCAT.COLUMNS WHERE TABSCHEMA = ? AND TABNAME = ? ORDER BY COLNO";
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, schema);
                stmt.setString(2, tableName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    int columnNo = 0;
                    int startIndex = 0;
                    List<String> uniqueIndexes = getUniqueIndexes(schema, tableName); // Get unique indexes for the table
                    
                    while (rs.next()) {
                        String columnName = rs.getString("COLNAME");
                        String dbType = rs.getString("TYPENAME").toUpperCase();
                        int dbLength = rs.getInt("LENGTH");
                        String nulls = rs.getString("NULLS");
                        
                        // Map to system type
                        String systemType = DataTypeMapper.getSystemType(dbType);
                        
                        //AscII not required
                        int binaryLength = 0;
                        startIndex = 0;
                        
                        // Check if the column is part of a unique index and add a flag if so
                        String uniIdx = uniqueIndexes.contains(columnName) ? "Y" : " "; // Y if column is part of a unique index, space otherwise
                        
                        // Format and write the column definition
                        
                        writer.write(String.format("|  %-16s | %-9s | %-4d   | %-15s |  %-6d | %-3d | %-4s |  %-6d  |  %-5d | %-6s |\n",
                                columnName, dbType, dbLength, systemType, binaryLength, columnNo, nulls, startIndex, columnNo, uniIdx));
                        
                      
                        columnNo++;
                    }
                }
            }
            
            System.out.println("Profile generated successfully: " + outputFilePath);
        }
    }

    
    public void generateProfiles(String schema, String tableName, String outputFilePath, boolean isBinary) throws Exception {
        if (isBinary) {
            generateBinaryProfile(schema, tableName, outputFilePath);
        } else {
            generateAsciiProfile(schema, tableName, outputFilePath);
        }
    }

    
    /**
     * Fetches unique indexes for a specific table
     * @param schema Schema name
     * @param tableName Table name
     * @return List of unique index column names
     * @throws Exception If any error occurs or no unique indexes found
     */
    public List<String> getUniqueIndexes(String schema, String tableName) throws Exception {
        List<String> uniqueIndexes = new ArrayList<>();
        List<List<String>> allIndexes = new ArrayList<>();
        
        try (Connection conn = getConnection()) {
            String sql = "SELECT tabname, colnames FROM SYSCAT.INDEXES WHERE TABSCHEMA = ? AND TABNAME = ? AND UNIQUERULE = 'U' WITH UR";
            
            try (PreparedStatement stmt = conn.prepareStatement(sql)) {
                stmt.setString(1, schema);
                stmt.setString(2, tableName);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    boolean hasResults = false;
                    
                    while (rs.next()) {
                        hasResults = true;
                        String colNames = rs.getString("COLNAMES");
                        
                        // Print all indexes to console for reference
                        System.out.println("Found unique index for " + tableName + ": " + colNames);
                        
                        // Parse the column names (format is like "+COLUMN1+COLUMN2" or "-COLUMN1")
                        List<String> parsedColumns = parseIndexColumns(colNames);
                        
                        
                        // Add to all indexes list
                        allIndexes.add(parsedColumns);
                    }
                    
                    if (!hasResults) {
                        System.out.println("No unique indexes found for table " + schema + "." + tableName);
                        return uniqueIndexes;
                    }
                    
                    // Decision logic based on the number of indexes
                    if (allIndexes.size() == 1) {
                        // If only one index, use it regardless of whether it's DTINSERT-only
                        uniqueIndexes.addAll(allIndexes.get(0));
                        System.out.println("Using the only available index: " + uniqueIndexes);
                    } else {
                        // Multiple indexes - use non-DTINSERT-only indexes
                        for (List<String> index : allIndexes) {
                            if (!(index.size() == 1 && index.get(0).equals("DTINSERT"))) {
                                uniqueIndexes.addAll(index);
                                System.out.println("Using non-DTINSERT index: " + index);
                            }
                        }
                    }
                }
            }
        }
        
        return uniqueIndexes;
    }
    
    /**
     * Parse index column names from DB2 COLNAMES format
     * @param colNames Column names string from DB2 (format: "+COL1+COL2" or "-COL1")
     * @return List of column names
     */
    private List<String> parseIndexColumns(String colNames) {
        List<String> columns = new ArrayList<>();
        
        // Remove leading/trailing whitespace
        colNames = colNames.trim();
        
        // Split by + or - (but keep the delimiter)
        String[] parts = colNames.split("(?=[-+])");
        
        for (String part : parts) {
            if (part.isEmpty()) continue;
            
            // Remove the +/- prefix
            String columnName = part.substring(1).trim();
            if (!columnName.isEmpty()) {
                columns.add(columnName);
            }
        }
        
        return columns;
    }
    
    /**
     * Get database connection
     * @return Connection object
     * @throws SQLException If connection fails
     * @throws ClassNotFoundException If driver not found
     */
    private Connection getConnection() throws SQLException, ClassNotFoundException {
        Class.forName(DB_DRIVER);
        return DriverManager.getConnection(DB_URL, DB_USERNAME, DB_PASSWORD);
    }
    
    /**
     * Main method to run the generator
     * @param args Command line arguments
     */
    public static void main(String[] args) {
        try {
            if (args.length < 3) {
                System.out.println("Usage: java MsgProfileGenerator <schema> <tableName> <outputFilePath> <isBinary>");
                System.out.println("Example: java MsgProfileGenerator NDUTLD SPINDLE_TEST profiles/SPINDLE_TEST.pro true");
                return;
            }
            
            String schema = args[0].trim();
            String tableName = args[1].trim();
            String outputFilePath = args[2].trim();
            boolean isBinary = Boolean.parseBoolean(args[3].trim());
            MsgProfileGenerator generator = new MsgProfileGenerator();
            generator.generateProfiles(schema, tableName , outputFilePath, isBinary);
            
        } catch (Exception e) {
            System.err.println("Error generating profile: " + e.getMessage());
            e.printStackTrace();
        }
    }
}

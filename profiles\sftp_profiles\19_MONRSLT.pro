*| ColumnName       |  DBColumnType  |  DBColumnLength  |  SystemType       |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  0      |  Y      |  0      |  0      |  
|  PRODUCT          |  Char        |  21     |  System.String    |  21     |  1      |  Y      |  4      |  1      |  
|  DISKCODE         |  Integer     |  4      |  System.Int32     |  4      |  2      |  Y      |  48     |  2      |  
|  DISKSEQUENCE     |  Integer     |  4      |  System.Int32     |  4      |  3      |  Y      |  56     |  3      |  
|  SIDE             |  Char        |  1      |  System.String    |  1      |  4      |  Y      |  64     |  4      |  
|  TESTNUM          |  Integer     |  4      |  System.Int32     |  4      |  5      |  Y      |  72     |  5      |  
|  MAGRULE          |  SmallInt    |  2      |  System.Int16     |  2      |  6      |         |  80     |  6      |  
|  MAGHEADID        |  Char        |  20     |  System.String    |  11     |  7      |         |  84     |  7      |  
|  BATCHTIME        |  Timestamp   |  4      |  System.DateTime  |  4      |  8      |         |  112    |  8      |  
|  DATAINDEX        |  SmallInt    |  2      |  System.Int16     |  2      |  9      |         |  120    |  9      |  
|  DTINSERT         |  Timestamp   |  4      |  System.DateTime  |  4      |  10     |  Y      |  0      |  10     |  
|  CORRELID         |  Integer     |  4      |  System.Int32     |  4      |  11     |         |  0      |  11     |  

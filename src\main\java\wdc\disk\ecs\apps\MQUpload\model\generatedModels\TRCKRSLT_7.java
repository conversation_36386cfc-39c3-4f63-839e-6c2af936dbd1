package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TRCKRSLT_7
 */
public class TRCKRSLT_7  {
    private Short spindle;
    private Integer testnum;
    private Short bandnum;
    private Double track;
    private Short reqopernum;
    private Double topresult;
    private Double bottomresult;
    private Double realtrack;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;
    private Short avalclip;

    // Default constructor
    public TRCKRSLT_7() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Short getBandnum() {
        return bandnum;
    }

    public void setBandnum(Short bandnum) {
        this.bandnum = bandnum;
    }

    public Double getTrack() {
        return track;
    }

    public void setTrack(Double track) {
        this.track = track;
    }

    public Short getReqopernum() {
        return reqopernum;
    }

    public void setReqopernum(Short reqopernum) {
        this.reqopernum = reqopernum;
    }

    public Double getTopresult() {
        return topresult;
    }

    public void setTopresult(Double topresult) {
        this.topresult = topresult;
    }

    public Double getBottomresult() {
        return bottomresult;
    }

    public void setBottomresult(Double bottomresult) {
        this.bottomresult = bottomresult;
    }

    public Double getRealtrack() {
        return realtrack;
    }

    public void setRealtrack(Double realtrack) {
        this.realtrack = realtrack;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

    public Short getAvalclip() {
        return avalclip;
    }

    public void setAvalclip(Short avalclip) {
        this.avalclip = avalclip;
    }

}



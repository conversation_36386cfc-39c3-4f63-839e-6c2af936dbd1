*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |         |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  3      |         |  0      |  3      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  4      |         |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  RIGAKUNAME            |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  DT_MSRMT              |  Timestamp   |  4      |  System.DateTime       |  4      |  8      |         |  0      |  8      |  
|  RECIPE                |  Char        |  15     |  System.String         |  15     |  9      |         |  0      |  9      |  
|  AMAGAVG               |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  ARUAVG                |  Double      |  8      |  System.Double         |  8      |  11     |         |  0      |  11     |  
|  ANIWAVG               |  Double      |  8      |  System.Double         |  8      |  12     |         |  0      |  12     |  
|  ASULAVG               |  Double      |  8      |  System.Double         |  8      |  13     |         |  0      |  13     |  
|  ATIAVG                |  Double      |  8      |  System.Double         |  8      |  14     |         |  0      |  14     |  
|  AMAGSTDEV             |  Double      |  8      |  System.Double         |  8      |  15     |         |  0      |  15     |  
|  ARUSTDEV              |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  ANIWSTDEV             |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  ASULSTDEV             |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  ATISTDEV              |  Double      |  8      |  System.Double         |  8      |  19     |         |  0      |  19     |  
|  AMAGRANGE             |  Double      |  8      |  System.Double         |  8      |  20     |         |  0      |  20     |  
|  ARURANGE              |  Double      |  8      |  System.Double         |  8      |  21     |         |  0      |  21     |  
|  ANIWRANGE             |  Double      |  8      |  System.Double         |  8      |  22     |         |  0      |  22     |  
|  ASULRANGE             |  Double      |  8      |  System.Double         |  8      |  23     |         |  0      |  23     |  
|  ATIRANGE              |  Double      |  8      |  System.Double         |  8      |  24     |         |  0      |  24     |  
|  BMAGAVG               |  Double      |  8      |  System.Double         |  8      |  25     |         |  0      |  25     |  
|  BRUAVG                |  Double      |  8      |  System.Double         |  8      |  26     |         |  0      |  26     |  
|  BNIWAVG               |  Double      |  8      |  System.Double         |  8      |  27     |         |  0      |  27     |  
|  BSULAVG               |  Double      |  8      |  System.Double         |  8      |  28     |         |  0      |  28     |  
|  BTIAVG                |  Double      |  8      |  System.Double         |  8      |  29     |         |  0      |  29     |  
|  BMAGSTDEV             |  Double      |  8      |  System.Double         |  8      |  30     |         |  0      |  30     |  
|  BRUSTDEV              |  Double      |  8      |  System.Double         |  8      |  31     |         |  0      |  31     |  
|  BNIWSTDEV             |  Double      |  8      |  System.Double         |  8      |  32     |         |  0      |  32     |  
|  BSULSTDEV             |  Double      |  8      |  System.Double         |  8      |  33     |         |  0      |  33     |  
|  BTISTDEV              |  Double      |  8      |  System.Double         |  8      |  34     |         |  0      |  34     |  
|  BMAGRANGE             |  Double      |  8      |  System.Double         |  8      |  35     |         |  0      |  35     |  
|  BRURANGE              |  Double      |  8      |  System.Double         |  8      |  36     |         |  0      |  36     |  
|  BNIWRANGE             |  Double      |  8      |  System.Double         |  8      |  37     |         |  0      |  37     |  
|  BSULRANGE             |  Double      |  8      |  System.Double         |  8      |  38     |         |  0      |  38     |  
|  BTIRANGE              |  Double      |  8      |  System.Double         |  8      |  39     |         |  0      |  39     |  
|  ACAPAVG               |  Double      |  8      |  System.Double         |  8      |  40     |         |  0      |  40     |  
|  AOXMAGAVG             |  Double      |  8      |  System.Double         |  8      |  41     |         |  0      |  41     |  
|  ACRTIAVG              |  Double      |  8      |  System.Double         |  8      |  42     |         |  0      |  42     |  
|  AALTIAVG              |  Double      |  8      |  System.Double         |  8      |  43     |         |  0      |  43     |  
|  ANIWCRAVG             |  Double      |  8      |  System.Double         |  8      |  44     |         |  0      |  44     |  
|  AELFAVG               |  Double      |  8      |  System.Double         |  8      |  45     |         |  0      |  45     |  
|  ACAPSTDEV             |  Double      |  8      |  System.Double         |  8      |  46     |         |  0      |  46     |  
|  AOXMAGSTDEV           |  Double      |  8      |  System.Double         |  8      |  47     |         |  0      |  47     |  
|  ACRTISTDEV            |  Double      |  8      |  System.Double         |  8      |  48     |         |  0      |  48     |  
|  AALTISTDEV            |  Double      |  8      |  System.Double         |  8      |  49     |         |  0      |  49     |  
|  ANIWCRSTDEV           |  Double      |  8      |  System.Double         |  8      |  50     |         |  0      |  50     |  
|  AELFSTDEV             |  Double      |  8      |  System.Double         |  8      |  51     |         |  0      |  51     |  
|  ACAPRANGE             |  Double      |  8      |  System.Double         |  8      |  52     |         |  0      |  52     |  
|  AOXMAGRANGE           |  Double      |  8      |  System.Double         |  8      |  53     |         |  0      |  53     |  
|  ACRTIRANGE            |  Double      |  8      |  System.Double         |  8      |  54     |         |  0      |  54     |  
|  AALTIRANGE            |  Double      |  8      |  System.Double         |  8      |  55     |         |  0      |  55     |  
|  ANIWCRRANGE           |  Double      |  8      |  System.Double         |  8      |  56     |         |  0      |  56     |  
|  AELFRANGE             |  Double      |  8      |  System.Double         |  8      |  57     |         |  0      |  57     |  
|  BCAPAVG               |  Double      |  8      |  System.Double         |  8      |  58     |         |  0      |  58     |  
|  BOXMAGAVG             |  Double      |  8      |  System.Double         |  8      |  59     |         |  0      |  59     |  
|  BCRTIAVG              |  Double      |  8      |  System.Double         |  8      |  60     |         |  0      |  60     |  
|  BALTIAVG              |  Double      |  8      |  System.Double         |  8      |  61     |         |  0      |  61     |  
|  BNIWCRAVG             |  Double      |  8      |  System.Double         |  8      |  62     |         |  0      |  62     |  
|  BELFAVG               |  Double      |  8      |  System.Double         |  8      |  63     |         |  0      |  63     |  
|  BCAPSTDEV             |  Double      |  8      |  System.Double         |  8      |  64     |         |  0      |  64     |  
|  BOXMAGSTDEV           |  Double      |  8      |  System.Double         |  8      |  65     |         |  0      |  65     |  
|  BCRTISTDEV            |  Double      |  8      |  System.Double         |  8      |  66     |         |  0      |  66     |  
|  BALTISTDEV            |  Double      |  8      |  System.Double         |  8      |  67     |         |  0      |  67     |  
|  BNIWCRSTDEV           |  Double      |  8      |  System.Double         |  8      |  68     |         |  0      |  68     |  
|  BELFSTDEV             |  Double      |  8      |  System.Double         |  8      |  69     |         |  0      |  69     |  
|  BCAPRANGE             |  Double      |  8      |  System.Double         |  8      |  70     |         |  0      |  70     |  
|  BOXMAGRANGE           |  Double      |  8      |  System.Double         |  8      |  71     |         |  0      |  71     |  
|  BCRTIRANGE            |  Double      |  8      |  System.Double         |  8      |  72     |         |  0      |  72     |  
|  BALTIRANGE            |  Double      |  8      |  System.Double         |  8      |  73     |         |  0      |  73     |  
|  BNIWCRRANGE           |  Double      |  8      |  System.Double         |  8      |  74     |         |  0      |  74     |  
|  BELFRANGE             |  Double      |  8      |  System.Double         |  8      |  75     |         |  0      |  75     |  
|  ANITAAVG              |  Double      |  8      |  System.Double         |  8      |  76     |         |  0      |  76     |  
|  ANITASTDEV            |  Double      |  8      |  System.Double         |  8      |  77     |         |  0      |  77     |  
|  ANITARANGE            |  Double      |  8      |  System.Double         |  8      |  78     |         |  0      |  78     |  
|  BNITAAVG              |  Double      |  8      |  System.Double         |  8      |  79     |         |  0      |  79     |  
|  BNITASTDEV            |  Double      |  8      |  System.Double         |  8      |  80     |         |  0      |  80     |  

*| ColumnName       |  DBColumnType  |  DBColumnLength  |  SystemType       |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  SPINDLE          |  SmallInt    |  2      |  System.Int16     |  2      |  0      |  Y      |  0      |  0      |  
|  GLIDENUM         |  Integer     |  4      |  System.Int32     |  4      |  1      |  Y      |  8      |  1      |  
|  DATATYPE         |  Integer     |  8      |  System.Int64     |  2      |  2      |  Y      |  16     |  2      |  
|  DATA1            |  Double      |  8      |  System.Double    |  8      |  3      |         |  24     |  3      |  
|  DATA2            |  Double      |  8      |  System.Double    |  8      |  4      |         |  40     |  4      |  
|  DATA3            |  Double      |  8      |  System.Double    |  8      |  5      |         |  56     |  5      |  
|  DATA4            |  Double      |  8      |  System.Double    |  8      |  6      |         |  72     |  6      |  
|  DATA5            |  Integer     |  4      |  System.Int32     |  4      |  7      |         |  88     |  7      |  
|  DATA6            |  Integer     |  4      |  System.Int32     |  4      |  8      |         |  96     |  8      |  
|  DTINSERT         |  Timestamp   |  4      |  System.DateTime  |  4      |  9      |  Y      |  0      |  9      |  
|  CORRELID         |  Integer     |  4      |  System.Int32     |  4      |  10     |         |  0      |  10     |  

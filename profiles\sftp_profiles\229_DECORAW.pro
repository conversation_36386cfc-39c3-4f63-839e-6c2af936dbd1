*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  3      |         |  0      |  3      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  4      |         |  0      |  4      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  5      |         |  0      |  5      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  LEADING_MANDRIL       |  Char        |  1      |  System.String         |  1      |  7      |         |  0      |  7      |  
|  BRUSH_RPM             |  Double      |  8      |  System.Double         |  8      |  8      |         |  0      |  8      |  
|  DISKBELT1_RPM         |  Double      |  8      |  System.Double         |  8      |  9      |         |  0      |  9      |  
|  DISKBELT2_RPM         |  Double      |  8      |  System.Double         |  8      |  10     |         |  0      |  10     |  
|  SCRUB_PTIME           |  Double      |  8      |  System.Double         |  8      |  11     |         |  0      |  11     |  
|  SCRUB_LIFECOUNT       |  SmallInt    |  2      |  System.Int16          |  2      |  12     |         |  0      |  12     |  
|  TOTAL_US_TIME         |  SmallInt    |  2      |  System.Int16          |  2      |  13     |         |  0      |  13     |  
|  US_UNIT_PH            |  Double      |  8      |  System.Double         |  8      |  14     |         |  0      |  14     |  
|  US_TANK1_POWER        |  SmallInt    |  2      |  System.Int16          |  2      |  15     |         |  0      |  15     |  
|  US_TANK1_FREQ         |  Double      |  8      |  System.Double         |  8      |  16     |         |  0      |  16     |  
|  US_TANK1_TEMP         |  Double      |  8      |  System.Double         |  8      |  17     |         |  0      |  17     |  
|  US_TANK1_FLOW         |  Double      |  8      |  System.Double         |  8      |  18     |         |  0      |  18     |  
|  US_TANK1_PTIME        |  Double      |  8      |  System.Double         |  8      |  19     |         |  0      |  19     |  
|  US_TANK1_ALARM        |  Char        |  16     |  System.String         |  16     |  20     |         |  0      |  20     |  
|  US_TANK2_POWER        |  SmallInt    |  2      |  System.Int16          |  2      |  21     |         |  0      |  21     |  
|  US_TANK2_FREQ         |  Double      |  8      |  System.Double         |  8      |  22     |         |  0      |  22     |  
|  US_TANK2_TEMP         |  Double      |  8      |  System.Double         |  8      |  23     |         |  0      |  23     |  
|  US_TANK2_FLOW         |  Double      |  8      |  System.Double         |  8      |  24     |         |  0      |  24     |  
|  US_TANK2_PTIME        |  Double      |  8      |  System.Double         |  8      |  25     |         |  0      |  25     |  
|  US_TANK2_ALARM        |  Char        |  16     |  System.String         |  16     |  26     |         |  0      |  26     |  
|  US_TANK3_POWER        |  SmallInt    |  2      |  System.Int16          |  2      |  27     |         |  0      |  27     |  
|  US_TANK3_FREQ         |  Double      |  8      |  System.Double         |  8      |  28     |         |  0      |  28     |  
|  US_TANK3_TEMP         |  Double      |  8      |  System.Double         |  8      |  29     |         |  0      |  29     |  
|  US_TANK3_FLOW         |  Double      |  8      |  System.Double         |  8      |  30     |         |  0      |  30     |  
|  US_TANK3_PTIME        |  Double      |  8      |  System.Double         |  8      |  31     |         |  0      |  31     |  
|  US_TANK3_ALARM        |  Char        |  16     |  System.String         |  16     |  32     |         |  0      |  32     |  
|  QDR1_PTIME            |  Double      |  8      |  System.Double         |  8      |  33     |         |  0      |  33     |  
|  QDR1_SPARGER_FLOW     |  Double      |  8      |  System.Double         |  8      |  34     |         |  0      |  34     |  
|  QDR1_SHOWERTIME       |  Double      |  8      |  System.Double         |  8      |  35     |         |  0      |  35     |  
|  TOTAL_MS_TIME         |  SmallInt    |  2      |  System.Int16          |  2      |  36     |         |  0      |  36     |  
|  MS_UNIT_PH            |  Double      |  8      |  System.Double         |  8      |  37     |         |  0      |  37     |  
|  MS_TANK1_POWER        |  SmallInt    |  2      |  System.Int16          |  2      |  38     |         |  0      |  38     |  
|  MS_TANK1_TEMP         |  Double      |  8      |  System.Double         |  8      |  39     |         |  0      |  39     |  
|  MS_TANK1_FLOW         |  Double      |  8      |  System.Double         |  8      |  40     |         |  0      |  40     |  
|  MS_TANK1_PTIME        |  Double      |  8      |  System.Double         |  8      |  41     |         |  0      |  41     |  
|  MS_TANK1_ALARM        |  Char        |  16     |  System.String         |  16     |  42     |         |  0      |  42     |  
|  MS_TANK2_POWER        |  SmallInt    |  2      |  System.Int16          |  2      |  43     |         |  0      |  43     |  
|  MS_TANK2_TEMP         |  Double      |  8      |  System.Double         |  8      |  44     |         |  0      |  44     |  
|  MS_TANK2_FLOW         |  Double      |  8      |  System.Double         |  8      |  45     |         |  0      |  45     |  
|  MS_TANK2_PTIME        |  Double      |  8      |  System.Double         |  8      |  46     |         |  0      |  46     |  
|  MS_TANK2_ALARM        |  Char        |  16     |  System.String         |  16     |  47     |         |  0      |  47     |  
|  QDR2_PTIME            |  Double      |  8      |  System.Double         |  8      |  48     |         |  0      |  48     |  
|  QDR2_SPARGER_FLOW     |  Double      |  8      |  System.Double         |  8      |  49     |         |  0      |  49     |  
|  QDR2_SHOWERTIME       |  Double      |  8      |  System.Double         |  8      |  50     |         |  0      |  50     |  
|  DIPRINSE_PTIME        |  Double      |  8      |  System.Double         |  8      |  51     |         |  0      |  51     |  
|  DIPRINSE_FLOW         |  Double      |  8      |  System.Double         |  8      |  52     |         |  0      |  52     |  
|  QDR3_PTIME            |  Double      |  8      |  System.Double         |  8      |  53     |         |  0      |  53     |  
|  QDR3_SPARGER_FLOW     |  Double      |  8      |  System.Double         |  8      |  54     |         |  0      |  54     |  
|  QDR3_SHOWERTIME       |  Double      |  8      |  System.Double         |  8      |  55     |         |  0      |  55     |  
|  IPADRYER_PTIME        |  Double      |  8      |  System.Double         |  8      |  56     |         |  0      |  56     |  
|  IPADRYER_VAPORTEMP    |  Double      |  8      |  System.Double         |  8      |  57     |         |  0      |  57     |  
|  IPADRYER_LIFECOUNT    |  SmallInt    |  2      |  System.Int16          |  2      |  58     |         |  0      |  58     |  
|  IPADRYER_RESISTIVITY  |  SmallInt    |  2      |  System.Int16          |  2      |  59     |         |  0      |  59     |  
|  IPADRYER_CW_TEMP      |  Double      |  8      |  System.Double         |  8      |  60     |         |  0      |  60     |  
|  DETERGENT_CARBOY      |  VarChar     |  32     |  System.String         |  32     |  61     |         |  0      |  61     |  
|  DETERGENT_BATCH       |  VarChar     |  32     |  System.String         |  32     |  62     |         |  0      |  62     |  
|  TOTAL_TRANSFER_TIME   |  Double      |  8      |  System.Double         |  8      |  63     |         |  0      |  63     |  
|  QDR2_SHOWER_FLOW      |  Double      |  8      |  System.Double         |  8      |  64     |         |  0      |  64     |  

*ColumnName | DBColumnType | DBColumnLength | SystemType |BinaryLength	|ColumnNo  |NULLS |StartIndex	|ValueIndex
*    |             |              |                |           |            |         |      |            |          
*    |             |              |                |           |            |         |      |            |          
*    |             |              |                |           |            |         |      |            |          
*    |             |              |                |           |            |         |      |            |          
*    |             |              |                |           |            |         |      |            |          
*    |             |              |                |           |            |         |      |            |          
|SPINDLE      | SmallInt      |   2         |  System.Int16  | 2        |   0      |  Y	  |  0     |      0
|GLIDENUM     | Integer       |   4         |  System.Int32  | 4        |   1	   |  Y	  |  8     |      1
|BATCHID      |	Integer       |   4         |  System.Int32  | 4        |   2	   |  N	  |  16    |	  2
|PRODUCTCODE  |	SmallInt      |   2         |  System.Int16  | 2        |   3	   |  N	  |  24    |      3
|DISKCODE     |	Integer	      |   4         |  System.Int32  | 4        |   4	   |  N	  |  32    |      4


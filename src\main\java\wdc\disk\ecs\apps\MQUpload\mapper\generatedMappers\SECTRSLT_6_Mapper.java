package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.SECTRSLT_6;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for SECTRSLT_6
 */
public interface SECTRSLT_6_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.SECTRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{testnum},",
        "    #{bandnum},",
        "    #{track},",
        "    #{rev},",
        "    #{sector},",
        "    #{reqopernum},",
        "    #{side},",
        "    #{result},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    TRACK,",
        "    REV,",
        "    SECTOR,",
        "    REQOPERNUM,",
        "    SIDE,",
        "    RESULT,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.TESTNUM = SOURCE.TESTNUM AND TARGET.BANDNUM = SOURCE.BANDNUM AND TARGET.TRACK = SOURCE.TRACK AND TARGET.REV = SOURCE.REV AND TARGET.SECTOR = SOURCE.SECTOR AND TARGET.REQOPERNUM = SOURCE.REQOPERNUM AND TARGET.SIDE = SOURCE.SIDE",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.RESULT = SOURCE.RESULT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    TESTNUM,",
        "    BANDNUM,",
        "    TRACK,",
        "    REV,",
        "    SECTOR,",
        "    REQOPERNUM,",
        "    SIDE,",
        "    RESULT,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.TESTNUM,",
        "    SOURCE.BANDNUM,",
        "    SOURCE.TRACK,",
        "    SOURCE.REV,",
        "    SOURCE.SECTOR,",
        "    SOURCE.REQOPERNUM,",
        "    SOURCE.SIDE,",
        "    SOURCE.RESULT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(SECTRSLT_6 record);
}











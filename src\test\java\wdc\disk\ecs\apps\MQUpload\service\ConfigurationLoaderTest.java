package wdc.disk.ecs.apps.MQUpload.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;

public class ConfigurationLoaderTest {

    @Mock
    private LogStream mockLog;

    private String originalAppEnv;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Store original app.env property to restore it after tests
        originalAppEnv = System.getProperty("app.env");
    }

    @AfterEach
    void tearDown() {
        // Restore original app.env property
        if (originalAppEnv != null) {
            System.setProperty("app.env", originalAppEnv);
        } else {
            System.clearProperty("app.env");
        }
    }

    @Test
    void testLoadDefaultEnvironment() throws SFTPSendException {
        // Unset app.env to test default behavior (dev_windows)
        System.clearProperty("app.env");

        // The config path is now just a placeholder, as loading is from classpath
        ConfigurationLoader loader = new ConfigurationLoader(mockLog, "src/test/resources");

        RabbitMQProperties props = loader.getRabbitMQProperties();

        // Assert values from the base MQ_Upload.yml file
        assertArrayEquals(new String[]{"csf-md-rabitmq1.ad.shared"}, props.getRabbitMQHosts());
        assertEquals(5672, props.getRabbitMQPort());
        assertEquals("mqupload", props.getRabbitMQUsername());
        verify(mockLog).write(eq(LogStream.INFORMATION_MESSAGE), anyString());
    }

    @Test
    void testLoadProdWindowsEnvironment() throws SFTPSendException {
        System.setProperty("app.env", "prod_windows");

        ConfigurationLoader loader = new ConfigurationLoader(mockLog, "src/test/resources");
        RabbitMQProperties props = loader.getRabbitMQProperties();

        // Assert that the host is overridden by prod_windows config
        assertArrayEquals(new String[]{"csf-mp-talend03.ad.shared"}, props.getRabbitMQHosts());
        
        // Assert that other properties are inherited from the base config
        assertEquals(5672, props.getRabbitMQPort());
        assertEquals("mqupload", props.getRabbitMQUsername());

        // Verify logging output
        verify(mockLog).write(LogStream.INFORMATION_MESSAGE, "Loading configuration for environment: 'prod_windows'");
        verify(mockLog).write(LogStream.INFORMATION_MESSAGE, "Found and loaded environment-specific config: MQ_Upload.prod_windows.yml");
    }

    @Test
    void testLoadDevLocalhostEnvironment() throws SFTPSendException {
        System.setProperty("app.env", "dev_localhost");

        ConfigurationLoader loader = new ConfigurationLoader(mockLog, "src/test/resources");
        RabbitMQProperties props = loader.getRabbitMQProperties();

        // Assert that the host is overridden to localhost
        assertArrayEquals(new String[]{"localhost"}, props.getRabbitMQHosts());
        
        // Assert inheritance
        assertEquals(5672, props.getRabbitMQPort());
        assertEquals("mqupload", props.getRabbitMQUsername());
    }

    @Test
    void testLoadNonExistentEnvironment() throws SFTPSendException {
        System.setProperty("app.env", "non_existent_env");

        ConfigurationLoader loader = new ConfigurationLoader(mockLog, "src/test/resources");
        RabbitMQProperties props = loader.getRabbitMQProperties();

        // Assert that it falls back to the base configuration
        assertArrayEquals(new String[]{"csf-md-rabitmq1.ad.shared"}, props.getRabbitMQHosts());
        assertEquals(5672, props.getRabbitMQPort());

        // Verify warning log message
        verify(mockLog).write(LogStream.WARNING_MESSAGE, "Environment-specific config not found for 'non_existent_env'. Using base config only.");
    }
    
    @Test
    void testPathResolutionForLinuxEnv() throws SFTPSendException {
        System.setProperty("app.env", "dev_TV_linux");
        ConfigurationLoader loader = new ConfigurationLoader(mockLog, "src/test/resources");

        // In a real Linux environment, this would resolve to /u/MQUpload/profiles/pdsMsgId.pro
        // Here we just check if the properties are loaded correctly before path resolution
        String appRoot = loader.getAppRoot();
        assertEquals("/u/MQUpload", appRoot);
    }
}
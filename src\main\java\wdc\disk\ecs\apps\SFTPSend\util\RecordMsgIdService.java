package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Set;
import java.util.TreeSet;

public class RecordMsgIdService {
	private String filePath;
	private String msgIdRecFilePath;
	private Set<String> msgIdSet;
	private String machineName;
	private Calendar lastCal;
	private String msgIdRecDetailFilePath;
	
	
	//filePath is a diretory, then use filepath and machine name to combine into a fileName
	public RecordMsgIdService(String machineName, String filePath) throws IOException {
		super();
		this.msgIdRecFilePath = filePath;
//		if (!filePath.trim().endsWith(File.separator)) {
//			filePath += File.separator;
//		}
		this.filePath = filePath;
		this.msgIdRecFilePath = filePath + machineName + "_MsgId.rec";
		this.machineName = machineName;
		
		msgIdSet = new TreeSet<String>();
		loadRecord(machineName, filePath);
		System.out.println("MsgIdSet:" + msgIdSet);
	}
	
	/*
	 * Load record from file path. If file is not exist, then return.
	 */
	public void loadRecord(String machineName, String filePath) {
		if(filePath != null) {
			if (!filePath.trim().endsWith(File.separator)) {
				filePath += File.separator;
			}
		}
		
		File file = new File(filePath + machineName + "_MsgId.rec");
		if(file.exists()) {
			BufferedReader reader = null;
			try {
				reader = new BufferedReader(new FileReader(file));
			} catch (FileNotFoundException e) {
				e.printStackTrace();
			}
			String str;
			try {
				str = reader.readLine();
				while(str!=null && !"".equals(str)) {
					if(!machineName.equals(str)) {
						msgIdSet.add(str);					
					}
					str = reader.readLine();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}finally {
				if(reader != null) {
					try {
						reader.close();
					} catch (IOException e) {
						e.printStackTrace();
					}				
				}				
			}
		}else{
			return;
		}
		
	}
	
	/**
	 * If record exist in the Set, then return
	 * If not exist, then append to file
	 * @param msgId
	 */
	public void recordMsgId(String msgId)  {
		if(msgIdSet.contains(msgId)) {
			return;			
		}else {
			msgIdSet.add(msgId);	
			File file = new File(msgIdRecFilePath);
			if(!file.exists()) {
				System.out.println("not exist");
			}
			appendLineTofile(file, msgId);				
		}
	}


	private void appendLineTofile(File file,String data) {
		FileWriter fWriter = null;
		try {
			fWriter = new FileWriter(file, true);
			fWriter.append(data);
			fWriter.append("\n");
			fWriter.flush();
		} catch (IOException e) {
			e.printStackTrace();
		}finally {
			if(fWriter != null) {
				try {
					fWriter.close();
				} catch (IOException e) {
					e.printStackTrace();
				}					
			}
		}
	}
	
	/**
	 * For each line will be record in the file
	 * Information including: MsgId, file name, workstation, timestamp
	 */
	public void recordMsgDetail(String msgId, String fileName, String sendingDir) {
		//a.prepare data
		// DateFormat
		Calendar instance = Calendar.getInstance();
		Date date = instance.getTime(); 
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String timeStampString = sdf.format(date);

		//b.Assemble to one line
		String recordLine = assembleMsgRecordToStringLine(msgId, fileName, timeStampString);
		
		//c.write to file
		writeMsgDetailToFile(instance, recordLine, sendingDir);
		
		//Update last
		lastCal = instance;
	}


	private void writeMsgDetailToFile(Calendar calendar, String recordLine, String sendingDir) {
		//If the day changed, write to another file
		boolean isSameDay = isSameDayBetweenCurrentDayAndLastRecDay(calendar);
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		
		//Append to file
		String dateTime = sdf.format(calendar.getTime());
		msgIdRecDetailFilePath = this.filePath + this.machineName + "_" + dateTime + "_MsgIdDetail.rec";
		File file = new File(msgIdRecDetailFilePath);
		appendLineTofile(file, recordLine);				
		
		//Move old record file to Sending
		if(!isSameDay && lastCal != null) {
				dateTime = sdf.format(lastCal.getTime());
				msgIdRecDetailFilePath = this.filePath + this.machineName + "_" + dateTime + "_MsgIdDetail.rec";
				File renameToFile = new File(msgIdRecDetailFilePath);
				
				File dest = new File(sendingDir +  this.machineName + "_" + dateTime + "_MsgIdDetail.rec" );
				if(renameToFile.exists()) {
					renameToFile.renameTo(dest);
				}				
		}
	}


	private boolean isSameDayBetweenCurrentDayAndLastRecDay(Calendar calendar) {
		int currentDay = calendar.get(Calendar.DAY_OF_MONTH);
		int lastDay = -1;
		if(lastCal != null) {
			lastDay = lastCal.get(Calendar.DAY_OF_MONTH);
		}
		if(currentDay == lastDay) {
			return true;
		}else {
			return false;
		}
	}


	private String assembleMsgRecordToStringLine(String msgId, String fileName, String timeStampString) {
		StringBuffer sBuffer = new StringBuffer();
		sBuffer.append(msgId);
		sBuffer.append(",");
		sBuffer.append(fileName);
		sBuffer.append(",");
		sBuffer.append(machineName);
		sBuffer.append(",");
		sBuffer.append(timeStampString);
		return sBuffer.toString();
	}

	public Calendar getLastCal() {
		return lastCal;
	}

	public void setLastCal(Calendar lastCal) {
		this.lastCal = lastCal;
	}
	
	

}

package wdc.disk.ecs.apps.SFTPSend;

import java.awt.Font;
import java.awt.GridBagConstraints;

import javax.swing.SwingConstants;

/**
 *********************************************************************
 *                                                                   *
 *  SFTPSendView	                                                 *
 *                                                                   *
 *********************************************************************
 * This is an ECS Application and must be started through the ECS2000.
 * Sample profile in ecs.ini:
 * MODEL {
 *    MODELKEY=SFTPSend,
 *    CLASS=wdc.disk.ecs.apps.SFTPSend.SFTPSendEcsApp,
 *    PARAMETERS=SFTPSend DEBUG,    // block name with all the parameters to start the application
 * }
 * 
 * VIEW {
 *    MODELKEY=SFTPSend,
 *    MAIN_TABNAME=Ecs2000,
 *    TABNAME=SFTPSend,
 *    CLASS=wdc.disk.ecs.apps.SFTPSend.SFTPSendView,
 * }
 * 
 * SFTPSend {
 * 	  PDS_DIR=/opt/ecs/storage/mq/sent, // File directory where PDS files are saved
 * 	  SFTP_SERVER_DIR=/D:/ECS_FTP/InputFiles/POC/DECO/, // The file directory in SFTP server where PDS files will be sent
 * 	  PRIMARY_SFTP_SERVER=csf-mp-talend02.ad.shared:22:sftpadm:sftp@123456,
 * 	  BACKUP_SFTP_SERVER=csf-mp-talend01.ad.shared:22:sftpadm:sftp@123456,
 * 	  DB_MSG_ID_PROFILE=/opt/ecs/profiles/dbmsgid.pro,
 * 	  BINARY_FORMAT, //optional, default is N. N--PDS file is ascii-encoded data, Y--PDS file is binary data
 * 	  MAX_READ_FILES, // optional, default is 1000. Limit the number of files JVM read one time to avoid OOM
 * 	  MAX_LINE_LENGTH, // optional, default is 4096. If the length of data line is more than specified length, not send to SFTP, but write to problem file	
 * 	  IDLE_SEND_INTERVAL, // optional, default is 60, units-seconds. Interval time after last send in idle mode
 * 	  WAITING_FOR_WRITE_ENDED, // optional, default is 10, units-seconds. The time of waiting for other ECS application writes PDS file ended
 * 	  CHANGE_TO_IDLE, // optional, default is 120, units-seconds. No data to send for specified time, the app will change to idle mode
 * 	  RETENTION_HOURS=144, //optional, default is 144, units-hours. The time of retaining the sent data files, default is 7 days
 * }
 * 
 * Creation date: (10/27/2003 3:52:25 PM)
 * @author: Administrator
 */
public class SFTPSendView extends ibm.disk.ecs.ecs2000.EcsApplicationView {
	private javax.swing.DefaultListModel ivjMessageList = null;
	private javax.swing.JLabel ivjChannelJLabel = null;
	private javax.swing.JTextField ivjChannelJTextField = null;
	private java.beans.PropertyChangeListener ivjConnEtoC1SourceListener;
	private javax.swing.JPanel ivjMainJPanel = null;
	private javax.swing.JList ivjMessageJList = null;
	private javax.swing.JScrollPane ivjMessageJScrollPane = null;
	private javax.swing.JLabel ivjMQHostNameJLabel = null;
	private javax.swing.JTextField ivjMQHostNameJTextField = null;
	private javax.swing.JTextField ivjQMgrJTextField = null;
	private javax.swing.JLabel ivjQmgrNameJLabel = null;
	private javax.swing.JLabel ivjQueueNameJLabel = null;
	private javax.swing.JTextField ivjQueueNameJTextField = null;
	private SFTPSendEcsApp ivjSendSftpEcsApp = null;
	private java.beans.PropertyChangeListener ivjConnPtoP2SourceListener;
	private java.beans.PropertyChangeListener ivjConnPtoP3SourceListener;
	private java.beans.PropertyChangeListener ivjConnPtoP4SourceListener;
	private java.beans.PropertyChangeListener ivjConnPtoP5SourceListener;
	//For send app
	private java.beans.PropertyChangeListener ivjConnPtoP6SourceListener;            	
	
//	private LogStream log = new LogStream("/opt/ecs/logs/sftpView.log");
	/**
	 * SendToMQView constructor comment.
	 * @param ecs2000 ibm.disk.ecs.ecs2000.Ecs2000
	 * @param model ibm.disk.ecs.ecs2000.EcsApplication
	 * @param name java.lang.String
	 * @param ico javax.swing.Icon
	 * @param tip java.lang.String
	 */
	public SFTPSendView(ibm.disk.ecs.ecs2000.Ecs2000 ecs2000, ibm.disk.ecs.ecs2000.EcsApplication model, java.lang.String name, javax.swing.Icon ico, java.lang.String tip) {
		super(ecs2000, model, name, ico, tip);
		initialize();
	}
	/**
	 * SendToMQView constructor comment.
	 * @param ecs2000 ibm.disk.ecs.ecs2000.Ecs2000
	 * @param model ibm.disk.ecs.ecs2000.EcsApplication
	 * @param name java.lang.String
	 * @param ico javax.swing.Icon
	 * @param tip java.lang.String
	 * @param parameters java.lang.String
	 */
	public SFTPSendView(ibm.disk.ecs.ecs2000.Ecs2000 ecs2000, ibm.disk.ecs.ecs2000.EcsApplication model, String name, javax.swing.Icon ico, String tip, String parameters) {
		super(ecs2000, model, name, ico, tip, parameters);
		initialize();
	}
	/**
	 * connEtoC1:  (sendToMQ.message --> SendToMQView.sendToMQ_Message(Libm.disk.extensions.ColoredString;)V)
	 * @param arg1 java.beans.PropertyChangeEvent
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void connEtoC1(java.beans.PropertyChangeEvent arg1) {
		try {
			// user code begin {1}
			// user code end
			this.sendToMQ_Message(getSendToSftp().getSendApp().getMessage());
			// user code begin {2}
			// user code end
		} catch (java.lang.Throwable ivjExc) {
			// user code begin {3}
			// user code end
			handleException(ivjExc);
		}
	}
	/**
	 * connPtoP1SetTarget:  (MessageList.this <--> JList1.model)
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void connPtoP1SetTarget() {
		/* Set the target from the source */
		try {
			getMessageJList().setModel(getMessageList());
			// user code begin {1}
			// user code end
		} catch (java.lang.Throwable ivjExc) {
			// user code begin {3}
			// user code end
			handleException(ivjExc);
		}
	}
	/**
	 * connPtoP2SetTarget:  (sendToMQ.channelname <--> ChannelJTextField.text)
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void connPtoP2SetTarget() {
		/* Set the target from the source */
		try {
			if ((getSendToSftp() != null)) {
				getChannelJTextField().setText(getSendToSftp().getSendApp().getSftpConnected());
			}
			// user code begin {1}
			// user code end
		} catch (java.lang.Throwable ivjExc) {
			// user code begin {3}
			// user code end
			handleException(ivjExc);
		}
	}
	/**
	 * connPtoP3SetTarget:  (sendToMQ.mqhostname <--> MQHostNameJTextField.text)
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void connPtoP3SetTarget() {
		/* Set the target from the source */
		try {
			if ((getSendToSftp() != null)) {
				getMQHostNameJTextField().setText(getSendToSftp().getSendApp().getCurrentSFTPServer().toString());
			}
			// user code begin {1}
			// user code end
		} catch (java.lang.Throwable ivjExc) {
			// user code begin {3}
			// user code end
			handleException(ivjExc);
		}
	}
	/**
	 * connPtoP4SetTarget:  (sendToMQ.qmgrname <--> QMgrJTextField.text)
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void connPtoP4SetTarget() {
		/* Set the target from the source */
		try {
			if ((getSendToSftp() != null)) {
				getQMgrJTextField().setText(getSendToSftp().getSendApp().getPdsDirName());
			}
			// user code begin {1}
			// user code end
		} catch (java.lang.Throwable ivjExc) {
			// user code begin {3}
			// user code end
			handleException(ivjExc);
		}
	}
	/**
	 * connPtoP5SetTarget:  (sendToMQ.queuename <--> QueueNameJTextField.text)
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void connPtoP5SetTarget() {
		/* Set the target from the source */
		try {
			if ((getSendToSftp() != null)) {
				getQueueNameJTextField().setText(getSendToSftp().getSendApp().getSftpServerDirName());
			}
			// user code begin {1}
			// user code end
		} catch (java.lang.Throwable ivjExc) {
			// user code begin {3}
			// user code end
			handleException(ivjExc);
		}
	}
	/**
	 * 
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private static void getBuilderData() {
	/*V1.1
	**start of data**
		D0CB838494G88G88GCFEBDCAFGGGGGGGGGGGG8CGGGE2F5E9ECE4E5F2A0E4E1F4E165FC8DD8145719A8FD56A46E0D5D582D6912443DB59B1235DD3731DDEF2F4F46366C565CB529595A3B366576FADB3635A96DA5A95D5ADB5A1A754CB04020A0A0430F00F178D3C8F0989493A4C4D190C1C19109221248B07381930719E166C386EB743E6F7979661B19EFC05836F77739737072FD67BD677DB9673C671C773C674C90537E9569BA3D0990DDBA611F37F504384688C93E1717A4F0DD2454C2
		D41F8A00FC12D6E00067A064ED99AD35641167270C10F6C1FA550DD2CBB13C971097271AEE40CB94B9591017FF6468CB334B7940A364E452F4D2C5B6BCF783F881378CA01120FCD2D94271EBA13D1B3FA36874044468304CE6E3098ADF2DE3658660B98ED02D47B2E51565BCFF8362G928136713259D0A60BEAD7DA5E4C4B15FC69D1327EAB9774B9A8CB45DF00CB4FE8177227193CC49BA20DB46C2F04E7EB5F4D31634E11890B43693FE9116CFE4B44E4CB4BA5496D987074DE3C6C142631B06A76EB23115201
		EE40EB8945793230BDBA60FD88C0CE937A2DAAED65724D3FF8062C72FEE408265F26B42657363420FE65C41796E7C07E9D15591C2E8952AEG264B12CA21CB12C16872E39D16B52717DA1A817A52857EF29A6247A19D83901BAEF4FC39D768E8784653E4708BA6FDEE949DD727B35D925203BA16DCA0E9FB29CD7C440B7DEA4AG39GBDGDE0025G2B814A28ADBE6DF9831EA7C764F64F440447FD526BBD65F278FABD8E49856FADAD1043F17D125D63F390426CED0731DEC39F819BB2C2ED8D01556D8A917661
		7D4E93E44D39EEFD3AC65DD62CBA205F42F924E4846B5616096D36FBA736DB9E3C6BE89D88E1F8B345AFD261D9DF8528ADE0FA8D647599C5BB3FF3DC689233EEA999DFD12C4F5450256401B77449C62603A1AB288BEB6375BEECE3AB60FBG9CGF7G16832C4B92ED7CD34F11395A385BADCBBE374DF5E9EA44EB734927261CAE076463EDFD36F236365682C6B7B8EFBA56AEAB0F287BE878F415E63BA4961C55A77076C8D1358B137D09283273F6C65A46EE258F5F57E3DFD56FE4FD1829601D945F2A42333E7D
		A99DDB189E003C35G2C7CBF5231D53E0B154FD1787CAF0AEFD361991FA10A47348372B681307E9D11F15EEB4DAE358C82F88196832C84D88F1010AD7A38455BF01F6328CF553F0C76675EA2F8CA8159E76BF67BE51B5BAEDD36791C36911764A73C4CF6AF56F1A404CEB70936E78799E7A437643349CE0F9B385BA6A4B0A6BFAB33F3B29F4B78A41ABB601C10C0400417883EFBA6F1DC38ECFE79BD2F43A6CB4192AC3FF492ED62166DB2F0848670DE1B036DD5140573DCAA545F14A35A6A913656C1FAD10EE817
		C7A77740739C983764EAE969F05AD18D1BEFC65828E9E7B5BCE3217CEA002DG29G433BCBADABG368218F3A11DAB466C2B0342B6B75EF81CAC3BE955344DF269A3C3ECAE333355396136F9004ACCGFCAAC0C99E4C0FG03G8100D80035G89F9E2ED3D1B35B40CBF6AB957D8CA8DAB037540BA8E674ECD0B75D666C255BB36CBAC674BF4FBBAC2374C7B9C676AB45AC8BAE1F364DC34392A779C557BAE4256BDFCE776705AAD9CD329500E267CD2CBD33E3009CD13E6B6D6CE597C12989D97FCE05812A3E8A1
		2C6CCBBE14A75D125CF2076405459FDE7C5CB6B77B320263E654235874F7A89E6751F6B0EC3F02CF7465B3BC70D00FC2111FC473ED326CF30ECC4952A5C9161D6E31A0FD8A55459FB2C57BF9DDF7CCE3DDFB200E8EGD9296B0C3FC0E57FBD9E4F4DA96FG0EECBE0ED6D2FFE1B114DF11AF6C4DCE673A75105E8230CD41FBA89E4B997714DAEA77887CAFF4033C7CFC66FAB5EC53205D122F36335D7AC1D57FAE84346CB2DD1517CFB62761FC2663F252B9BE0B5B96AB7383DA268B6A6E8508850887C88248AB90
		6DB112B6BC8B1F056A2FD51FFC72911525A5A767F7A80B5912CB0EBE967C3276D81811354742BDF9F6DBFFC85632F51677A7B25655B36DD60D1DCF4AB84F5BBDEE37C48730C2C218CF7F6AG564369F64AD71C0EB1C93E643CAD41FCD600E36090DDCB1A613D27C04C7353D54ABC7F6DCF112EF1EDBF31E36A32BE2E0059695A0260BC4FF4DAB60563C5F2C99350D06D1EA9374C700F53F50A637DE99457F9977B2CD7727BEDE3D20F53AF9306FF2F7CG722CD565544B63CE3F3975502163663318EE2C1F40E53E
		4ECAB80D3591797B79CE0561F22F8ED1BE82DBBF41981A110DFAD3D3AFCA5C60A2EBFB021B2000AC39F19976DB5ABDAE8FCC1717207F5CE36F54DD2EEBE4F2CEF6B6054A310F5B20CFDCEE708A184E6D8CF1F6G38F5BAA51723DE06373A430C7E056BEF0569B9B96E714BC1725E0BDD10BE8F69F08EC7C47DEF17D9C2F9CCCE0C79029CAE760E7942E91B84EDEBD7187E13D3521424A246241674230CFE43C87DAC7D4531345BC42F31797B708C5ACA8258D0B20041A4666F22DB18AF66EF3EC8C1633B1819C35E
		3089ED7846CE134AAE4BCDEC8F5E83CF165FDA204EDF4973F7AB746385DA74DB957A023D6A7C54887A93FBE767FFF30E7CBF1BA37F2BF3647FB4EABEEB473469BCBAA620BB413D4585701CB4A3FC5469BDE1F917ED2E29206F3BE79A4726CB35F6B2FCFAC0FBFC6F8AE0EC642642BFB710C15007E5BC59FC345A06BC3DBE6784389A688C70D848D7AA5531100EC27085219EB170DC8640E81B8E21DC75201DFD8E891D8FD8FB2D512B5BEB9517C37B3E887393157CAD1037CD159FCF73779AC4BC46D1447C01BB70
		DC82308A60F5405FA946F2C9CA396CE266B3D497B3DE4D45EAFECF26AB719DDEAE911E29GA5A50C9FABB719A14A551630F2ED709CAC89AF77EE26A89760651642B3AE221C51A84A2D67653640F31B2A1CE9AF0D37E40938CA03023BCEF1D330683EE790FB575EBCF57C29F5E907663A01B03817555F35B7FCCFBFF69B79AE817C723DE29E7863005A0FBB6977FB6CCE3A25623658BC23DED3986E5D191D709CF57A7C303FF7C881E14F6DB328AB6E39B81A5B640F0BB06E953E07BC6DF04A9E1F92A83053C90746
		0FBE3593E5224F31FA2FD8B71FBB210EEFA57E62BD7D2EA875CF6275B616866B3F61BCC9E33A5C8BD8665425FD3ADDD96F557138CF9720DE28CFD72998B3FF75663B77416FB47AB6831EF618193BE038C730E73C9E729EA2DA71C10043D62B352565345D8FBCDDCE3BCDC54BF42B2BB2F2BEB125A28EB7FCG4773DAC8EF82C8D2F4FED8D262109F5AEE5982967FB4AC36300B15C6EDD3AE99C7B85D600B98C2E61A088912B298DFE5024F978BCBA374D547B2056DE6CA226F367E6CA9327A661BD17B4EDB46E327
		E5413EB345D69B189E278C59FC2CEC00FCD2DEEAE17350E78CB8AF62EA48662001AA759C548C6506GE214725FB0207E932C0ADC472A02B6DEDE7E7827C876DF27EB46A6B7FE3FD83F2C1C69362EBC28E37232878C645187381E7969593CAF327715DABA76893901F434E96EA071BECFCDC73FE4E13E11503D25DC2D7B5A2496CD5D933F512A3F330F699C37AF287B16A7C9DA8AG537D45F45146A9D02676F521FBE38676194A69626B63DE033A2D07217CE200D58A5D758C5AB7D08B467F718C41BF9E4A24AA65
		F419B827841DB2317EA63B3578DB77431CCB790DE68AFE9D003B33DF70FBA9934703F05098AFF71A962FB58A2FC623E2DF00A3E641AB56086D2CB8ECC46876F53B963FE63318332E9CD64F59094F75C61D33FB4C5C67B0074F597FF2876BF18770CB4DE24E7E728C6EF1441C5D8FB3094DBD66C23D78FE6AB66A8DFE82CFFBEF23DC6AA790313FFBE2E7F89CB34E9C5CAB2FD53DEF646FCC1F063BD867C440ED87B0D514DA6A81FAAA04EE3F3CBDE7CC78F4E01618F0DCC138DE2E0A20AEB12A77E5703E32C26C0D
		ADD561F46BD4E5B7D5C44681D85CF4B31DA337D63038E91A027FAE45EFD361393D9D90B6E228AC350CD78A79B9E98B0BE5174D1B0E754312FB588F9BC1FE92G29823F822039CA7443495F564F558FA2964BBB006953E518CB1F412A5036E4F41767243B93C647663F2F556159E164DAC437B25CF70D372AFD57E5D5EC1C4543F3F315B81FFAD8D97F122BC45C767B84FD306AEA7065G8681E400D800B5G8955222D9E796D29395A6A2488A88CF8145776702000E80B62747036C8298E2D3BF69B56C4503D9E16
		4E2E8925E3753C222775043CB600C1G99A096E08DC0C20D2867AB3F5572D5C247E6B4C7A598D3E43AEE0A50757E6C643BF3522556E859C9CDED39361DA85BE5E6AB0587553612F700459F1B6059F5C0584AF50330156183E27C2E48D8D8FDBEB7A7DD6C01D0BA26C315C76830DA51411539B09DEE4FC91724294353E9C287F2D06870762C671F51F5B8BDA7DD73C1AD9DB29CC2073E03E25C7E9955EBB124E38F8A5BBDFB87630EE24D6B73B85DB2916B5DAF6F60BE5863F30EB95D4A39924559BDBE9A4CE3B169
		AF50FD45CA6039810061DE4CC6DCB2248D358277D08E62EAA15DDAAB643FFDB7FA0C7278DD6D98E547DD6D9865593B33476B7B6FC60BE1B27D1EAB907598D0F4FE39D054A320601EAA9275D82E603EC2F12BA11D2060CE5339F3BB24736A842E32C670EB2A9336711D8374EE06125ECA53B897F37BBDB0B8078DA8B6D4B77F18BE137140FD4BD8C8CC1F47D76FC56F6BA76EE977751377347B7AAF6F4D5E574F5E0B56577BE89DFEA497920D7873F96607B27F741EA10B176913BDFD0DC44C19E3271107461CF949
		6E7338DCFDB637240C43D42B08C3F86B447E715374AE48CAC84783A4A87D3EB44257C2C861FDEAF8A332EFD9DD13C974F67DB951EE57DF5173863FE74AE717425B0E68CA143C60CA55ADCB9358BC2CCC168E69E51BEFCC0AE87BBCCA4F7222516F55613EE74469F07AF8E414754B8BC53934DF7DFE49CF974D8E1BEC637DF1BEDD7447E959535E284833692357FFDC2FDDFF37BEFA7D2774F3577F2EBEFA7D9F504FDD7FBF51C733CFD6574C8C356D9DD6EC6F67F5A55A3607D667E2B657FBD04C7DA5EF881B7B27
		863439BEC80F83443CA1ECEEB82AFF998BE5D6E9589C536F5BA17A9DD16611DACFB4FE7140EBF3D4FECFE52A79B5A97CBE9B550FCF82DE5B2372F39B557C0EAA7CF6B9225EB3F93354D27DE634B136BC8D77AB81A53D0226E714FE718CE3CCFA4CE7730EBB6D7E8E49EF77B93D32870CA27ECDEC7F97F6E23FB5433B83E099409A15AC466373B4EEBD6A33CDC017DE72CAF6B2D40F347F1CADFCAFE3BD0F7972E7EB3D0899BDFB841FAE5B880D3D30B6784FB96171E5264BFFAFC0FEF1C03B81A035BED497A67335
		C2A13364904B2B674F1EC3E27F7E37C56AB8B553637CA87A13A3B67B4DB11FE74A6DB89351A2CC07EF95A1FF9770DA82306ED0288E0C7FD0D5862D530CE78A1C12AE8B52792B04DEDB00E69BC0B9602D96660FB25D0F5508B2CB819F87309EE0AB0021C1687EB5BB46C5FAEDCEB79FB95CBEDE334E66EFD5B7C45B6F96D46178EAEE885F6F6E23782E06707D6E5315E23FBB88F96B9A04FF701754E7B0DBE14FE99538C245E798320AF15BFFB0DC4F982BEA8FEF0D262747CA579EEB78DD380FD1FC02B57CAE5C7F
		E890F7610C0D50160DC227E96A6B379D06FD51E1015BD0A574F4A938939ED1EE1D024BBDAF4AEDD2F0FF17A64A190F885C772B95DFC941D5BAC439C58AAE7304A897FBC4746F561D3847D3CD111670A8EC63DB042CEFC2FA5DAE3C5FD341A10B834E4FC940CF06B2783E2CC9707E6BEC0C915C320D0CB9C77DC38EE954BBF53B71AD8B24D87EBFE013719862338DA2C69B786493A46ECCFE547B2D4623FC1C9D8D462A126283E9ECCE4F243EB352B9A0FF79D1449D4DD14749D04787C0818BBCD97ED8C8BE0ECF1B
		0E65B35D06AB05EE59DD0F12124111283AED65BA995FE24F642B122E7CDB3FC8E375FD25G6F2329C2559691298E363151942D0D5B00674637D89BA73FA55AF8E3E134B6667B3CA2519E65EF0B7638D8945E9E0F68546D71C7C5616D71F4BA4BE7B2FFC67315700DB2079DEEE2F558D50CF5E864A06A00FB2B8150A12619693E22D95461C7A5792ABA5C32798EB7518A083175C415087FEF829A63B1B117FF7D8672D456F80BEDDAEEE9B963F3BAFA78D448FD4B9FD409B6E8BBA656797CEA64990074D2001547C4
		4C74F9650EF06C3A9524E9C67B9E73362ABD7A78E32C0F379E8B5A21618261F6F823C648B43708F60F2DD137AB567D2BFAF53B3FD8935EAFF5C41D3FBDA49F757D06C11DEFB9905EEF021E0DFFD323987F03ADE2FE5CDB05FB2815CA3A0A26713C13257F2AD23D5F41F251667C05G1331BEC4463F65AE1496B09B7BB235A13B64F27523636B13FCEC1D7C1632CE46C35D92GCA5EA935543F436A495649FAE51D6CG7CA0C0GA096E06DBBE2EEF5D2DFB28152498A6E4986515EF92D22BD272CB8B61AA0BDG60
		85D8823092E0BD40D6G433BB0373FAB4ABFC44B6379D19B600681E2GD63C4B4E06377133DFBCCFE6653F57A06EADA2FF518648D35DA66630747A5648FEDD42F1CD9AF9546EB9FEE514FC84EF2B7A2EDB06D568162EAA139FC693FED7D26B3E1738E7994DD644DD4A596E25E52AF894104B9E74F3027E98A1296FE29F2E206BF7BC3CEF84D8F49CF15F2B6A60342D47A36B1E49F98A6803B28E00C2DE6F40F166BBA0B099CDBE143191708947050C8367058C4389ED5FDD2712D1F2A2D4C67D09F099BF21FEC3B3
		60BBCE88993F2A96B23CD1E428E1D1180C58889967284F918778550A0C57CF8899DBB4E4E40655A3A5CC0621ADA803FD47E77F2E98F8BEB8BA8AFF630B6D0FAD0DF96C312245A3270C0B535AB3965B5B0D0B7527B2960FF418C9D3BBB15ABBCE9D6EB815E59EB9BDDDBC5951E1351F1971CF1B1A4CA3E7262727770CD86D1D45531317C84648290B790527B2466D9DB56FD5831F11437849D8ECA378A9DA8C42C6D11E782CE923673C3B44FC1B87E9DC2F173731BA32F5701590FFA90D845742D4A857F1B2589EEC
		6D591F23BE2FDB7ECA1B3E00E87B998ECE3B0CBF17FCA071733AFFCA4739EA836093G12818C27E0CE83E8BAA5E209D586DC97CEFAC77BA536F96F3539E16703B7ACDADAG5DBD81A975560C0F5F6C1C05FD6FE17720D8D363BB9FA35B2D7B231E3F771DE275717227631A2443A72B1BAD936B3682725681A4822482E43743FE34DD546DC383BFF742AB90A7E559E69F27B7C714C3A727B49DC4636611595581533F9F3B3AEE92F6555C4E6CEAFD3B5A2E328B23595596A8D75B9134AB9EEBA81AEF3F31B629ABB6
		53CB973AFC7A498BFB4FD7BE7FFF3CC7E98756E78F960BBE6B6AE0FD3639C35DE77BCB54BE313A4F12215C606970BEFBD2B346B99BB03AE797C817B4EFBA369F7E817323CF8767E906CF2678C5AABC73650B2B05AF3F9472361D96E7AEFFCB3FABB958897BC800A5GF1G6B8192BB45587E9B03154CF1FEB8607176C837A417AA544D6A672819EF7D587A75E623D83F36F5C62EDFD80FF45E0F582746B3251672B3EC7D423475CC307E4C4F78FA9156DFFDDF16354B3FD609B8C7AB5048E7449E67FB7D98B38C3B
		1325DC49E9E9893913532EF2EF795E2733DA6CC3D61D91FB9F197641D6C8EF87B0F509F349FEE54FD25FA576ACB191FB161F91751E638B91FB96DDC87E590361FB1613A1FB4DBB874377AC021E35D74C41485856630704BF6EGBD6FG44812C6DE267721B3A304C5A8675B92C16EFD3C032EF6CD175057AD0026FF30B316C4509B1FA5CF13607858DD95E2FBE40E9C0F5E91B66323CE3E368FF86EFD4B3C216774DEA74E745DD711E10B8645018A91427F2364CED077A469AB735B9FD52E52AEF48B5F0415BFBA5
		DFA5D7154D6F92FDAE249FCA3AD9DB36F60BFB526312D6FE1F127F32C7AB5F21641F1D544A8F40F3C9B7FE8F7B9F692C4AF47DD255FEF83A25E9335856332BE735071B2C759C6A38EFF123C06DB339FEF3BC6A9D27CD5DFC3F4E1F5983120E59FDDE35B887A8BFDBEA59CFF89C0F08CF4A09C89C78DCD46FE6B4A12E4B47F3051F33685A23247925AAFDD594DD864E72FBF0E7033103B57F69C1836319EE8A3605617C7D34852B471573229EDB225403712FAD8C6A9FD79414756D6A7B1775C60D10E5BAB71B2CFF
		A88E4ABA34B7A8EB7B717B17F561041055B32B2CCADCA6386F62267B8475A786907174C348B34F3C78E2CA8AA1BBF67C66B778DC811F38B8AC039FD6FE470EE0799D74C3667DE1F37B6ADDE24DDB847A2FBBA7E2CD529DE13B4BEFFD1C2C7B7B3A28FE7136F34CCECCBDC1FF1F55F3D7F69BBCBFCAA564356D302F603063DF197E9100786DC138174A74E4BE0CB5DB0C51369E367E8C75286F5E6DAEC79FC6865CE20065G2B81B6GA48224G98FACBAD66DE31DE279AEA614996EBF13E81AB56D807F44BE91770
		0EB6BF04249B8EC633A59D63CFEE657CAFB1DDFDF71061FE180EFBF59F5F3CF4BB847EFF537B2D8E170B1ECE736F962663FC6BF47BF145141D37E8F1F1EFA3BDAFB40FFEBD02557572BEEC0BD6284BA0409D0025GF1CA5D9E4A60F74AB57698279DE3F89E0A5FB816052C8FB3231F3DCEE64E7DBD655B74FE6A08C716BD9348DF60E3B2314E3247CB117CDC151E917A1CE363B2AB4A718F53BB29AEE91422197DCC19E242EC767F5DB8792FA665EEAE34F3EA2F78DD0FB78AFFF7B6FE21D0583875BC33711E734C
		46192C9545B1A4D8BFD12FBF74073547770B453C61G9D171F976D71EE49B0BCD349FE8A7150E23FCB90FCD9FBFDD8A25AEBB3EF2F14736A185E0FF1CA606F7F22FA5FD7FD3F77E49682CC46DB3FD799EC4CFC5598FD5C3EE8543EB3314928FD9767DB46593F9B7ADDE3746F067E50287EEEA8DB97079A453AE83CF03FFE0BD8ABFF7CFA302F8C55F36D0D5AAEE85D395BB3A7DD787DD1C6F7EECE3A8879F4CF19C477ADC39742771ABF2378718B61FB4D5B4ADED3063C048BA2C6FEE55EFBC17EFBA796412F358F
		76E8FDE20F56B89CF2B7B4F88EF54AB6460F22E8FF45DB143DD81F580B3DCAF9EE0574F6G53C5619B7C31DDFD6658F45124F96638EC20CCDFFF1175CD5FC555770D40176477E0C6235C03611B925461078745771D96DD94FB41CFCB8237CE416D738A5CB6857731C9012B6F973A67B629FD723C7EABFA1D066E880EFE9E6F6B8F7F5E0159CCE349000F6F97EBEEC17E1B844F9DBF0C7C1693A07BFC9E2F6413E79816AE655C5E8AC36CAD9178ED6B1F7D9C0B69B0D401FBFF6325D2CBB5C09740B0008C3084E085
		409A008DG09G29G5983607FGB481F4G8C823886047E0B77544D22BFB7986AB878EC6CFEA953FF683CDA7F986037ECE0F67D99DDC3081D2F841AF577C5B7F0C2CD3789E8365E975D97F7ADAC7634FE5EF42CFFD2AAF1EDCA855D325FAB35548334828C3CA75AFB244CCC347CCF7A6DC04951E7F3418C4C7D203359F4834ABF420FE1635E1BAD463D8BF847A97CCB4BF10CCDBB9D72B891F3787EF20CD70CCB68FC89DCB5AD875E2E2ADCDDB97A7AFE3B4D6DF23A25BE1B03639B4AD156480CAC797BA4DF8F64
		B17C51F2740B9DB6594652474A316D3C2C8A629EF0B956810C476D6797BE1D81494572CE076559E7FAED7E1BAC6F03FD5157B8E95F5C3E29FB9F7D0EA6781483B3DE2E2FFF9F3D93BDBA6A1714F6886C4BD3F05D7EBE8FBA5D9EB73301AF1942EDE04BFB77E3F3DF0B201B6B77A1D8FFBB8B237777A305223F3721DDDDD6FE8B2EF8E1767DFF66CD47F4DCD492DD474F16A83EA16837DC5151562816359970C917E7EFC7F61FB43E006D13509A5B59AF9FA97A3F2A726BFE29FA0F798378F80F7D81E45C35AC2CDF
		17AC106E79F953B1BFE7045EB15BFEB95C7F39C97146AB617E4FB645DFB1C1DE600A0835FF4204BE4196C82782148F42FA855085B0BCA86636C37BF16F9EED2DE2DECDF029E2757AC948DDBBF910595DE27EDCBE28FD7E1BCF62A4B55DEADEEEBDFFA6C4252B742A6912F9B9F21547CA2EC623FBF0D2CDD74B4B3572E71F869DEB37B5B4EE465A47373FA6DA7B1C9C114F022BF5E164C349AE8F5846941A9AB372D27CFF6622703CFAB32EE578BBAECE1B4BF91BFE411A857559DD12FA9A23F4DCE5FD4CF00D94F7
		076378F7ADAB224F655395F34F65B1955AF10687ABA2638C8FD7E84799BED1210EB3B05C27AAF08D9C839FE7D0A97704023B2A60D6D01E083B92322EBEC375C2FCD7485A3A0A6AB5CDF1039C77F9956EAA47FD318257A32872B89EE8B35CF39538260EBADDFC0D7C8AAD0371A0D9E90F75D4C7275726527B8585F7D5698B7F79487E891CD7778F43F556C416BBD793D96E660948F25E93117D1DDAC97D8D1B5B61D27A6A35CA8CCA238F51AFF9FD129FCC007D240278FD8FFA474B6F3CAD3E23D0257DBBA03974CC
		4A636FBFF36A349BECD1723371F3ABFBE1735E22F95345DE2573980D8B69F4BA85983FC78B972647D38B24DBDD32B03AAF4F1B0E5BCFE3248DFC5428368146FF9CFF3A033FCF2A5EFFED797D2F1D858D3F7F984C632668F35A13263967344F1A346734552648B96D3F1834673467CC6AB90D75052E96F1DB20AF122F0ABBFFC7FAE92C659A3CDF933858DEF18F30DC41BDA789DA17027B66D54445C0FA2902BBFDCD50AED7F0473C0A5CE0B90F20A557856E990F204DD6F0FFBEA9E80794DC5CCDC16BD5F069AEC1
		FB673A08F35868F74F799AC527E706AF8F648471E2FA66E7E231FB54310061DFDE577CDDC9040D5779799CFF16BBA59DBBABAA2925733447AD33FBE6965A6E59C3251612A1F10778EC2F781D87965F9A2267CE6C8C7F59DEF1065FB524FD062F3E67B684E5908AF91EF7A87C1E5BD65AF7F7G3FD8111FFCB5D47ED7CB0272072E8979EB2348D76B60D05D97C47C468879A3347F93G3FF5C81C1F3E3CB7A86FCB9EA14F74FE243C74B964553E9FAE6F3FD11BE982FC5B7BC2DED6D9D0DE39CB39C798C5DE5AAC72
		962964B1BF6B0BE5E8E7CB813F82E04D7B42A73596616F3DC46E67E1FD22E707621C510C57EF78B96341DECC041CB3F602A155D61E04C942D5D9EFEE043F26BACA00F5C96C95B155756FEB45D4050C764A607B1FD687653DF0F5FE72BEFECD485B2E34EDFCCD1077125AA06FE31E7971FED5790EF7538702F7D2FD1077679B023CE3EF4E0FF72FCB70F6A93C3F0757F378FC392E8C797AA4915708AFE33E1EC477158C77FC990E1991700259DA7243DE75BA9987FC57FEF0BFB100BF3D26265B88B40977C5777310
		6F60ED839A630D7B216BF729694A0146BA879D1BD33777C62E67BA8D9F71B357A24BFD6EDAE4391D5E48F2395E48F2CED7E4B937CBDD0E459BFE15DB8595DC758AC9777B486E50F3F95D5345FF14774811A9BDB1F6120CD11B4BBBEE6BA66974E036076C9A21AB26640332B2496BC15A8620ED20346F74903D5DAF1332A05E0EF816FB49EB33CBD0A44BE1735D249CA649DD223BCE32C6FDE3A3D400CC7457BB2F03D81A426C349E32D39E47D3D61CF2E5D29CE45D079BD21E71CEGFF11196CC8C9C971528FD4AC
		D729D8B7757493EF9DE9C97B41D3E9FAE268A6994CCB68A686DC6A210234DE5DA48D3C844A4B2086BF2AC242A4A0AE638FD6B282AB135D74061019DD6A753548A4277722996F941971DC9C523BD8811E4A611702D89A9AA8A7E486F87E4923AFEF7C6EE5FF9A4968A6FBF1913EA479689ED5F260F70E798FD8F712ACAF7EA8827E7EA8341E1FBE527D720C8B4C43B45A0FBFC26817A507726BA5E08F052C5326E4272B214B669F1751EB83CBB060713FCC7630EC174DBD5640FFF30AF6016E64B0ED9F3AF8023E85
		D4DF64E2D13894BC17DBD3F1F66B5AB4226FA4BBDD9E1B2353E6078DFAB7491247E1CBB46EF1B9825E93ED1411F19A4742AE5A26BED226D49FABEB412BD20A52B783FF3EA4A7A927D3CFC840C79AE8C934B30BD242812D6983D1122FDB461F8E77782A153C0F3DAE9FF869CC71101E68F8A531878B83DE99C7540453B583AF9D50361EB15A37FBA149F6310A59E2E3D8BA21BE72F7DEF961AF7EE7EBCAB2B06B646390D44A8B16BABFB5B1A2799EDC75683EDD4B929FC60D3DD8DD41B90B4602C7BD3E89E0EE8E2B
		3348D2D437FC72856B231F7975B52AFA10D4A67B0383EFCC72CCB41C9C9DF53AA505817498305F273047B21630B2FB821FA8BC7B4D9DA3E0E19DA4ED42EA257F9B697F43783F11E29AA92671B0F4DB8A1975931E9F90F526E6C993E63421G3E6D8B4CB23A8AA24C8D314511F60368B22577B075BA7E2B44FFD51428067E2F1D376EC875C6184E567F00BA3F7D9FD06763D1F466FB1B9FB52B77B6A989C7225EBDB40F716FD20D051FF37E1A7E160A9770CB46145F1AA9405FE10C96DB3CB4E1F339E41B6B66C937
		83630C27702C08881FEAC78172770BB2BC1365E5503D6D07AA1A7F8BD0CB8788FB82F0D0D2A2GG14E7GGD0CB818294G94G88G88GCFEBDCAFFB82F0D0D2A2GG14E7GG8CGGGGGGGGGGGGGGGGGE2F5E9ECE4E5F2A0E4E1F4E1D0CB8586GGGG81G81GBAGGG0CA2GGGG
	**end of data**/
	}
	/**
	 * Return the ChannelJLabel property value.
	 * @return javax.swing.JLabel
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JLabel getChannelJLabel() {
		if (ivjChannelJLabel == null) {
			try {
				ivjChannelJLabel = new javax.swing.JLabel();
				ivjChannelJLabel.setName("ChannelJLabel");
				ivjChannelJLabel.setFont(new Font("Monospaced", Font.BOLD, 18));
				ivjChannelJLabel.setHorizontalAlignment(SwingConstants.LEFT);
				ivjChannelJLabel.setText("Connected(Y/N):");
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjChannelJLabel;
	}
	/**
	 * Return the ChannelJTextField property value.
	 * @return javax.swing.JTextField
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JTextField getChannelJTextField() {
		if (ivjChannelJTextField == null) {
			try {
				ivjChannelJTextField = new javax.swing.JTextField();
				ivjChannelJTextField.setName("ChannelJTextField");
				ivjChannelJTextField.setFont(new Font("Monospaced", Font.PLAIN, 18));
				ivjChannelJTextField.setEditable(false);
				ivjChannelJTextField.setBorder(null);
				ivjChannelJTextField.setBackground(new java.awt.Color(240,240,240));
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjChannelJTextField;
	}
	/**
	 * Return the JPanel1 property value.
	 * @return javax.swing.JPanel
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JPanel getMainJPanel() {
		if (ivjMainJPanel == null) {
			try {
				ivjMainJPanel = new javax.swing.JPanel();
				ivjMainJPanel.setName("MainJPanel");
				ivjMainJPanel.setLayout(new java.awt.GridBagLayout());
	
				java.awt.GridBagConstraints constraintsMessageJScrollPane = new java.awt.GridBagConstraints();
				constraintsMessageJScrollPane.gridx = 0; constraintsMessageJScrollPane.gridy = 2;
				constraintsMessageJScrollPane.gridwidth = 4;
				constraintsMessageJScrollPane.fill = java.awt.GridBagConstraints.BOTH;
				constraintsMessageJScrollPane.weightx = 1.0;
				constraintsMessageJScrollPane.weighty = 1.0;
				constraintsMessageJScrollPane.insets = new java.awt.Insets(4, 4, 4, 4);
				getMainJPanel().add(getMessageJScrollPane(), constraintsMessageJScrollPane);
	
				java.awt.GridBagConstraints constraintsQmgrNameJLabel = new java.awt.GridBagConstraints();
				constraintsQmgrNameJLabel.gridx = 0; constraintsQmgrNameJLabel.gridy = 1;
				constraintsQmgrNameJLabel.insets = new java.awt.Insets(4, 4, 4, 4);
				constraintsQmgrNameJLabel.anchor = GridBagConstraints.WEST;
				getMainJPanel().add(getQmgrNameJLabel(), constraintsQmgrNameJLabel);
	
				java.awt.GridBagConstraints constraintsMQHostNameJLabel = new java.awt.GridBagConstraints();
				constraintsMQHostNameJLabel.gridx = 0; constraintsMQHostNameJLabel.gridy = 0;
				constraintsMQHostNameJLabel.insets = new java.awt.Insets(4, 4, 4, 4);
				constraintsMQHostNameJLabel.anchor = GridBagConstraints.WEST;
				getMainJPanel().add(getMQHostNameJLabel(), constraintsMQHostNameJLabel);
	
				java.awt.GridBagConstraints constraintsQueueNameJLabel = new java.awt.GridBagConstraints();
				constraintsQueueNameJLabel.gridx = 2; constraintsQueueNameJLabel.gridy = 0;
				constraintsQueueNameJLabel.insets = new java.awt.Insets(4, 4, 4, 4);
				constraintsQueueNameJLabel.anchor = GridBagConstraints.WEST;
				getMainJPanel().add(getQueueNameJLabel(), constraintsQueueNameJLabel);
	
				java.awt.GridBagConstraints constraintsChannelJLabel = new java.awt.GridBagConstraints();
				constraintsChannelJLabel.gridx = 2; constraintsChannelJLabel.gridy = 1;
				constraintsChannelJLabel.insets = new java.awt.Insets(4, 4, 4, 4);
				constraintsChannelJLabel.anchor = GridBagConstraints.WEST;
				getMainJPanel().add(getChannelJLabel(), constraintsChannelJLabel);
	
				java.awt.GridBagConstraints constraintsMQHostNameJTextField = new java.awt.GridBagConstraints();
				constraintsMQHostNameJTextField.gridx = 1; constraintsMQHostNameJTextField.gridy = 0;
				constraintsMQHostNameJTextField.fill = java.awt.GridBagConstraints.HORIZONTAL;
				constraintsMQHostNameJTextField.weightx = 1.0;
				constraintsMQHostNameJTextField.insets = new java.awt.Insets(4, 4, 4, 4);
				getMainJPanel().add(getMQHostNameJTextField(), constraintsMQHostNameJTextField);
	
				java.awt.GridBagConstraints constraintsQueueNameJTextField = new java.awt.GridBagConstraints();
				constraintsQueueNameJTextField.gridx = 3; constraintsQueueNameJTextField.gridy = 0;
				constraintsQueueNameJTextField.fill = java.awt.GridBagConstraints.HORIZONTAL;
				constraintsQueueNameJTextField.weightx = 1.0;
				constraintsQueueNameJTextField.insets = new java.awt.Insets(4, 4, 4, 4);
				getMainJPanel().add(getQueueNameJTextField(), constraintsQueueNameJTextField);
	
				java.awt.GridBagConstraints constraintsQMgrJTextField = new java.awt.GridBagConstraints();
				constraintsQMgrJTextField.gridx = 1; constraintsQMgrJTextField.gridy = 1;
				constraintsQMgrJTextField.fill = java.awt.GridBagConstraints.HORIZONTAL;
				constraintsQMgrJTextField.weightx = 1.0;
				constraintsQMgrJTextField.insets = new java.awt.Insets(4, 4, 4, 4);
				getMainJPanel().add(getQMgrJTextField(), constraintsQMgrJTextField);
	
				java.awt.GridBagConstraints constraintsChannelJTextField = new java.awt.GridBagConstraints();
				constraintsChannelJTextField.gridx = 3; constraintsChannelJTextField.gridy = 1;
				constraintsChannelJTextField.fill = java.awt.GridBagConstraints.HORIZONTAL;
				constraintsChannelJTextField.weightx = 1.0;
				constraintsChannelJTextField.insets = new java.awt.Insets(4, 4, 4, 4);
				getMainJPanel().add(getChannelJTextField(), constraintsChannelJTextField);
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjMainJPanel;
	}
	/**
	 * Return the JList1 property value.
	 * @return javax.swing.JList
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JList getMessageJList() {
		if (ivjMessageJList == null) {
			try {
				ivjMessageJList = new javax.swing.JList();
				ivjMessageJList.setName("MessageJList");
				ivjMessageJList.setCellRenderer(new ibm.disk.extensions.ColoredStringCellRenderer());
				ivjMessageJList.setBounds(0, 0, 160, 120);
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjMessageJList;
	}
	/**
	 * Return the JScrollPane1 property value.
	 * @return javax.swing.JScrollPane
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JScrollPane getMessageJScrollPane() {
		if (ivjMessageJScrollPane == null) {
			try {
				ivjMessageJScrollPane = new javax.swing.JScrollPane();
				ivjMessageJScrollPane.setName("MessageJScrollPane");
				getMessageJScrollPane().setViewportView(getMessageJList());
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjMessageJScrollPane;
	}
	/**
	 * Return the MessageList property value.
	 * @return javax.swing.DefaultListModel
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.DefaultListModel getMessageList() {
		if (ivjMessageList == null) {
			try {
				ivjMessageList = new javax.swing.DefaultListModel();
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjMessageList;
	}
	/**
	 * Return the MQHostNameJLabel property value.
	 * @return javax.swing.JLabel
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JLabel getMQHostNameJLabel() {
		if (ivjMQHostNameJLabel == null) {
			try {
				ivjMQHostNameJLabel = new javax.swing.JLabel();
				ivjMQHostNameJLabel.setName("MQHostNameJLabel");
				ivjMQHostNameJLabel.setFont(new Font("Monospaced", Font.BOLD, 18));
				ivjMQHostNameJLabel.setHorizontalAlignment(SwingConstants.LEFT);
				ivjMQHostNameJLabel.setText("SFTP Server:");
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjMQHostNameJLabel;
	}
	/**
	 * Return the MQHostNameJTextField property value.
	 * @return javax.swing.JTextField
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JTextField getMQHostNameJTextField() {
		if (ivjMQHostNameJTextField == null) {
			try {
				ivjMQHostNameJTextField = new javax.swing.JTextField();
				ivjMQHostNameJTextField.setName("MQHostNameJTextField");
				ivjMQHostNameJTextField.setFont(new Font("Monospaced", Font.PLAIN, 18));
				ivjMQHostNameJTextField.setEditable(false);
				ivjMQHostNameJTextField.setBorder(null);
				ivjMQHostNameJTextField.setBackground(new java.awt.Color(240,240,240));
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjMQHostNameJTextField;
	}
	/**
	 * Return the QMgrJTextField property value.
	 * @return javax.swing.JTextField
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JTextField getQMgrJTextField() {
		if (ivjQMgrJTextField == null) {
			try {
				ivjQMgrJTextField = new javax.swing.JTextField();
				ivjQMgrJTextField.setName("QMgrJTextField");
				ivjQMgrJTextField.setFont(new Font("Monospaced", Font.PLAIN, 18));
				ivjQMgrJTextField.setEditable(false);
				ivjQMgrJTextField.setBorder(null);
				ivjQMgrJTextField.setBackground(new java.awt.Color(240,240,240));
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjQMgrJTextField;
	}
	/**
	 * Return the JLabel1 property value.
	 * @return javax.swing.JLabel
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JLabel getQmgrNameJLabel() {
		if (ivjQmgrNameJLabel == null) {
			try {
				ivjQmgrNameJLabel = new javax.swing.JLabel();
				ivjQmgrNameJLabel.setName("QmgrNameJLabel");
				ivjQmgrNameJLabel.setFont(new Font("Monospaced", Font.BOLD, 18));
				ivjQmgrNameJLabel.setHorizontalAlignment(SwingConstants.LEFT);
				ivjQmgrNameJLabel.setText("PDS Dir:");
				
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjQmgrNameJLabel;
	}
	/**
	 * Return the QueueNameJLabel property value.
	 * @return javax.swing.JLabel
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JLabel getQueueNameJLabel() {
		if (ivjQueueNameJLabel == null) {
			try {
				ivjQueueNameJLabel = new javax.swing.JLabel();
				ivjQueueNameJLabel.setName("QueueNameJLabel");
				ivjQueueNameJLabel.setFont(new Font("Monospaced", Font.BOLD, 18));
				ivjQueueNameJLabel.setHorizontalAlignment(SwingConstants.LEFT);
				ivjQueueNameJLabel.setText("SFTP Dir:");
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjQueueNameJLabel;
	}
	/**
	 * Return the QueueNameJTextField property value.
	 * @return javax.swing.JTextField
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private javax.swing.JTextField getQueueNameJTextField() {
		if (ivjQueueNameJTextField == null) {
			try {
				ivjQueueNameJTextField = new javax.swing.JTextField();
				ivjQueueNameJTextField.setName("QueueNameJTextField");
				ivjQueueNameJTextField.setFont(new Font("Monospaced", Font.PLAIN, 18));
				ivjQueueNameJTextField.setEditable(false);
				ivjQueueNameJTextField.setBorder(null);
				ivjQueueNameJTextField.setBackground(new java.awt.Color(240,240,240));
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		}
		return ivjQueueNameJTextField;
	}
	/**
	 * Return the sendToMQ property value.
	 * @return ibm.disk.mq.SendToMQ
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private SFTPSendEcsApp getSendToSftp() {
		// user code begin {1}
		// user code end
		return ivjSendSftpEcsApp;
	}
	/**
	 * Called whenever the part throws an exception.
	 * @param exception java.lang.Throwable
	 */
	private void handleException(java.lang.Throwable exception) {
	
		/* Uncomment the following lines to print uncaught exceptions to stdout */
		// System.out.println("--------- UNCAUGHT EXCEPTION ---------");
		// exception.printStackTrace(System.out);
	}
	/**
	 * Initializes connections
	 * @exception java.lang.Exception The exception description.
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void initConnections() throws java.lang.Exception {
		// user code begin {1}
		// user code end
		class ConnEtoC1SourceListener implements java.beans.PropertyChangeListener {
			public void propertyChange(java.beans.PropertyChangeEvent evt) {
				if ((evt.getPropertyName().equals("message"))) 
					connEtoC1(evt);
			};
		};
		ivjConnEtoC1SourceListener = new ConnEtoC1SourceListener();
		class ConnPtoP2SourceListener implements java.beans.PropertyChangeListener {
			public void propertyChange(java.beans.PropertyChangeEvent evt) {
				if ((evt.getPropertyName().equals("sftpConnected"))) 
					connPtoP2SetTarget();
			};
		};
		ivjConnPtoP2SourceListener = new ConnPtoP2SourceListener();
		class ConnPtoP3SourceListener implements java.beans.PropertyChangeListener {
			public void propertyChange(java.beans.PropertyChangeEvent evt) {
				if ((evt.getPropertyName().equals("sftpServer"))) 
					connPtoP3SetTarget();
			};
		};
		ivjConnPtoP3SourceListener = new ConnPtoP3SourceListener();
		class ConnPtoP4SourceListener implements java.beans.PropertyChangeListener {
			public void propertyChange(java.beans.PropertyChangeEvent evt) {
				if ((evt.getPropertyName().equals("pdsDir"))) 
					connPtoP4SetTarget();
			};
		};
		ivjConnPtoP4SourceListener = new ConnPtoP4SourceListener();
		class ConnPtoP5SourceListener implements java.beans.PropertyChangeListener {
			public void propertyChange(java.beans.PropertyChangeEvent evt) {
				if ((evt.getPropertyName().equals("sftpDir"))) 
					connPtoP5SetTarget();
			};
		};
		ivjConnPtoP5SourceListener = new ConnPtoP5SourceListener();
		class ConnPtoP6SourceListener implements java.beans.PropertyChangeListener {
			public void propertyChange(java.beans.PropertyChangeEvent evt) {
//				log.write("ConnPtoP6SourceListener property change." + evt.getPropertyName());
				if ((evt.getPropertyName().equals("sendApp"))) {
					initSendAppListener();					
				} 
			};
		};
		ivjConnPtoP6SourceListener = new ConnPtoP6SourceListener();
		
		connPtoP1SetTarget();
		connPtoP2SetTarget();
		connPtoP3SetTarget();
		connPtoP4SetTarget();
		connPtoP5SetTarget();
//		log.write("Init connections completed.");
	}
	/**
	 * Initialize the class.
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void initialize() {
		try {
			// user code begin {1}
			// user code end
			setName("SendToMQView");
			setLayout(new java.awt.GridLayout());
			setSize(616, 353);
			add(getMainJPanel(), getMainJPanel().getName());
			initConnections();
		} catch (java.lang.Throwable ivjExc) {
			handleException(ivjExc);
		}
		// user code begin {2}
		// user code end
	}
	/**
	 * Insert the method's description here.
	 * Creation date: (10/27/2003 3:52:25 PM)
	 */
	public void initView() {
		if (getEcsApplicationModel() == null) {
			getMessageList().addElement("No Ecs Application model found, make sure there is a MODEL block in the ecs.ini");
		} else {	
			setsendSftpEcsApp((SFTPSendEcsApp)getEcsApplicationModel());
			if (getSendToSftp() == null) {
				getMessageList().addElement("SFTPSendEcsApp is not running");
			}
		}
	}
	/**
	 * main entrypoint - starts the part when it is run as an application
	 * @param args java.lang.String[]
	 */
	public static void main(java.lang.String[] args) {
		try {
			javax.swing.JFrame frame = new javax.swing.JFrame();
			SFTPSendView aSendToMQView;
			aSendToMQView = new SFTPSendView(new ibm.disk.ecs.ecs2000.Ecs2000(), null, new java.lang.String(), null, new java.lang.String());
			frame.setContentPane(aSendToMQView);
			frame.setSize(aSendToMQView.getSize());
			frame.addWindowListener(new java.awt.event.WindowAdapter() {
				public void windowClosing(java.awt.event.WindowEvent e) {
					System.exit(0);
				};
			});
			frame.show();
			java.awt.Insets insets = frame.getInsets();
			frame.setSize(frame.getWidth() + insets.left + insets.right, frame.getHeight() + insets.top + insets.bottom);
			frame.setVisible(true);
		} catch (Throwable exception) {
			System.err.println("Exception occurred in main() of ibm.disk.ecs.ecs2000.EcsApplicationView");
			exception.printStackTrace(System.out);
		}
	}
	/**
	 * Comment
	 */
	public void sendToMQ_Message(ibm.disk.extensions.ColoredString msg) {
		if (getMessageList().getSize() >= 500) {
			getMessageList().remove(0);
		}
		getMessageList().addElement(msg);
		
		// don't know why but if there are 2 ensureIndexIsVisible calls it scrolls to the
		// bottom, with just one it doesn't work sometimes.
		if (getMessageList().size() > 30) {
			getMessageJList().ensureIndexIsVisible(getMessageList().size()-2);
			getMessageJList().ensureIndexIsVisible(getMessageList().size()-1);			
		}
		return;
	}
	/**
	 * Set the sendToMQ to a new value.
	 * @param newValue ibm.disk.mq.SendToMQ
	 */
	/* WARNING: THIS METHOD WILL BE REGENERATED. */
	private void setsendSftpEcsApp(SFTPSendEcsApp newValue) {
//		log.write("starting setsendSftpEcsApp");
		if (ivjSendSftpEcsApp != newValue) {
			try {
				/* Stop listening for events from the current object */
				if (ivjSendSftpEcsApp != null) {
					ivjSendSftpEcsApp.getSendApp().removePropertyChangeListener(ivjConnEtoC1SourceListener);
					ivjSendSftpEcsApp.getSendApp().removePropertyChangeListener(ivjConnPtoP2SourceListener);
					ivjSendSftpEcsApp.getSendApp().removePropertyChangeListener(ivjConnPtoP3SourceListener);
					ivjSendSftpEcsApp.getSendApp().removePropertyChangeListener(ivjConnPtoP4SourceListener);
					ivjSendSftpEcsApp.getSendApp().removePropertyChangeListener(ivjConnPtoP5SourceListener);
					ivjSendSftpEcsApp.removePropertyChangeListener(ivjConnPtoP6SourceListener);
				}
				ivjSendSftpEcsApp = newValue;
	
				/* Listen for events from the new object */
				if (ivjSendSftpEcsApp != null) {
					ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnEtoC1SourceListener);
					ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP2SourceListener);
					ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP3SourceListener);
					ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP4SourceListener);
					ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP5SourceListener);
					ivjSendSftpEcsApp.addPropertyChangeListener(ivjConnPtoP6SourceListener);
				}
				connPtoP2SetTarget();
				connPtoP3SetTarget();
				connPtoP4SetTarget();
				connPtoP5SetTarget();
				// user code begin {1}
				// user code end
			} catch (java.lang.Throwable ivjExc) {
				// user code begin {2}
				// user code end
				handleException(ivjExc);
			}
		};
//		log.write("End setsendSftpEcsApp");
		// user code begin {3}
		// user code end
	}

	public void initSendAppListener() {
//		log.write("initSendListener");
		if (ivjSendSftpEcsApp != null) {
			ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnEtoC1SourceListener);
			ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP2SourceListener);
			ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP3SourceListener);
			ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP4SourceListener);
			ivjSendSftpEcsApp.getSendApp().addPropertyChangeListener(ivjConnPtoP5SourceListener);
			ivjSendSftpEcsApp.addPropertyChangeListener(ivjConnPtoP6SourceListener);
//			log.write("ivjSendSftpEcsApp Add listener completed.");
		}else {
//			log.write("ivjSendSftpEcsApp is null.");			
		}
		connPtoP1SetTarget();
		connPtoP2SetTarget();
		connPtoP3SetTarget();
		connPtoP4SetTarget();
		connPtoP5SetTarget();
	}
}

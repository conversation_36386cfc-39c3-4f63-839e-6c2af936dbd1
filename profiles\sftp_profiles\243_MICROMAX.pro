*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  3      |         |  0      |  3      |  
|  SIDE                  |  Char        |  1      |  System.String         |  1      |  4      |         |  0      |  4      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  5      |         |  0      |  5      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  6      |         |  0      |  6      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  8      |         |  0      |  8      |  
|  MEASURE_RESOURCE      |  Char        |  10     |  System.String         |  10     |  9      |         |  0      |  9      |  
|  TEXTURE_RESOURCE      |  Char        |  10     |  System.String         |  10     |  10     |         |  0      |  10     |  

*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  CASSETTE              |  Char        |  11     |  System.String         |  11     |  2      |         |  0      |  2      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  3      |         |  0      |  3      |  
|  EMPLOYEE              |  Char        |  7      |  System.String         |  7      |  4      |         |  0      |  4      |  
|  DT_2000               |  Timestamp   |  4      |  System.DateTime       |  4      |  5      |         |  0      |  5      |  
|  LOT_2000              |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  RESOURCE_2000         |  Char        |  10     |  System.String         |  10     |  7      |         |  0      |  7      |  
|  DT_2400               |  Timestamp   |  4      |  System.DateTime       |  4      |  8      |         |  0      |  8      |  
|  LOT_2400              |  Char        |  10     |  System.String         |  10     |  9      |         |  0      |  9      |  
|  RESOURCE_2400         |  Char        |  10     |  System.String         |  10     |  10     |         |  0      |  10     |  
|  DT_2500               |  Timestamp   |  4      |  System.DateTime       |  4      |  11     |         |  0      |  11     |  
|  LOT_2500              |  Char        |  10     |  System.String         |  10     |  12     |         |  0      |  12     |  

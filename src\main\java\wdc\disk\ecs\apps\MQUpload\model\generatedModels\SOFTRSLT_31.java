package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for SOFTRSLT_31
 */
public class SOFTRSLT_31  {
    private Short spindle;
    private Integer testnum;
    private Short bandnum;
    private Short reqopernum;
    private Short votes;
    private Integer topresult;
    private Integer bottomresult;
    private java.time.LocalDateTime dtinsert;
    private Integer correlid;

    // Default constructor
    public SOFTRSLT_31() {
    }

    public Short getSpindle() {
        return spindle;
    }

    public void setSpindle(Short spindle) {
        this.spindle = spindle;
    }

    public Integer getTestnum() {
        return testnum;
    }

    public void setTestnum(Integer testnum) {
        this.testnum = testnum;
    }

    public Short getBandnum() {
        return bandnum;
    }

    public void setBandnum(Short bandnum) {
        this.bandnum = bandnum;
    }

    public Short getReqopernum() {
        return reqopernum;
    }

    public void setReqopernum(Short reqopernum) {
        this.reqopernum = reqopernum;
    }

    public Short getVotes() {
        return votes;
    }

    public void setVotes(Short votes) {
        this.votes = votes;
    }

    public Integer getTopresult() {
        return topresult;
    }

    public void setTopresult(Integer topresult) {
        this.topresult = topresult;
    }

    public Integer getBottomresult() {
        return bottomresult;
    }

    public void setBottomresult(Integer bottomresult) {
        this.bottomresult = bottomresult;
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public Integer getCorrelid() {
        return correlid;
    }

    public void setCorrelid(Integer correlid) {
        this.correlid = correlid;
    }

}



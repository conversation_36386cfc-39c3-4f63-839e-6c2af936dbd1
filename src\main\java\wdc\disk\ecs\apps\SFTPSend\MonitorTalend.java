package wdc.disk.ecs.apps.SFTPSend;

import wdc.disk.ecs.apps.SFTPSend.thread.MonitorTalendStatusRunnable;

public class MonitorTalend {
	public static void main(String[] args) {
		System.out.println("args size:" + args.length + " arg0:" + args[0] );
		if (args.length < 1) {
			System.out.println("Invalid start parameters: ");
			System.out.println("java wdc.disk.ecs.apps.SFTPSend.MonitorTalend <profile path>");
			System.exit(0);
		} else {
			try {
				MonitorTalendStatusRunnable runnable = new MonitorTalendStatusRunnable(args[0]);
				runnable.run();
			} catch (Throwable e) {
				System.out.println("Unable to initialize MonitorTalendAPP, exception=" + e.toString());
				e.printStackTrace();
				System.exit(0);
			}
		}
	}
}

*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  1      |         |  0      |  1      |  
|  FACTORY_LOOK_LOT      |  VarChar     |  128    |  System.String         |  128    |  2      |  Y      |  0      |  2      |  
|  STATION_NO            |  Integer     |  4      |  System.Int32          |  4      |  3      |  Y      |  0      |  3      |  
|  STATION_TYPE          |  VarChar     |  128    |  System.String         |  128    |  4      |         |  0      |  4      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  5      |         |  0      |  5      |  
|  PPH                   |  Integer     |  4      |  System.Int32          |  4      |  6      |         |  0      |  6      |  
|  MEAS_START            |  Timestamp   |  4      |  System.DateTime       |  4      |  7      |  Y      |  0      |  7      |  
|  MEAS_END              |  Timestamp   |  4      |  System.DateTime       |  4      |  8      |         |  0      |  8      |  
|  CASSETTE_SEQ          |  Integer     |  4      |  System.Int32          |  4      |  9      |         |  0      |  9      |  
|  SAMPLE_START          |  SmallInt    |  2      |  System.Int16          |  2      |  10     |         |  0      |  10     |  
|  SAMPLE_END            |  SmallInt    |  2      |  System.Int16          |  2      |  11     |         |  0      |  11     |  
|  STEP                  |  SmallInt    |  2      |  System.Int16          |  2      |  12     |  Y      |  0      |  12     |  
|  DURATION              |  Real        |  4      |  System.Double         |  4      |  13     |         |  0      |  13     |  
|  DISKSEQ               |  SmallInt    |  2      |  System.Int16          |  2      |  14     |  Y      |  0      |  14     |  
|  EMI_PWR_A             |  Real        |  4      |  System.Double         |  4      |  15     |         |  0      |  15     |  
|  EMI_PWR_B             |  Real        |  4      |  System.Double         |  4      |  16     |         |  0      |  16     |  
|  VOLT_A_ANODE_V        |  Real        |  4      |  System.Double         |  4      |  17     |         |  0      |  17     |  
|  VOLT_B_ANODE_IA       |  Real        |  4      |  System.Double         |  4      |  18     |         |  0      |  18     |  
|  TV                    |  Real        |  4      |  System.Double         |  4      |  19     |         |  0      |  19     |  
|  Z_POS_FIL_A           |  Real        |  4      |  System.Double         |  4      |  20     |         |  0      |  20     |  
|  Z_POS_FIL_B           |  Real        |  4      |  System.Double         |  4      |  21     |         |  0      |  21     |  
|  BIAS_V                |  Real        |  4      |  System.Double         |  4      |  22     |         |  0      |  22     |  
|  BIAS_I                |  Real        |  4      |  System.Double         |  4      |  23     |         |  0      |  23     |  
|  FIL_BIAS_V            |  Real        |  4      |  System.Double         |  4      |  24     |         |  0      |  24     |  
|  MFC_MFC1              |  Real        |  4      |  System.Double         |  4      |  25     |         |  0      |  25     |  
|  MFC_MFC2              |  Real        |  4      |  System.Double         |  4      |  26     |         |  0      |  26     |  
|  MFC_MFC3              |  Real        |  4      |  System.Double         |  4      |  27     |         |  0      |  27     |  
|  MFC_MFC4              |  Real        |  4      |  System.Double         |  4      |  28     |         |  0      |  28     |  
|  PRESSURE              |  Real        |  4      |  System.Double         |  4      |  29     |         |  0      |  29     |  
|  TEMP                  |  Real        |  4      |  System.Double         |  4      |  30     |         |  0      |  30     |  
|  TARGET_NAME           |  VarChar     |  128    |  System.String         |  128    |  31     |         |  0      |  31     |  
|  TARGET_ID_A           |  VarChar     |  128    |  System.String         |  128    |  32     |         |  0      |  32     |  
|  TARGET_ID_B           |  VarChar     |  128    |  System.String         |  128    |  33     |         |  0      |  33     |  
|  KWH_REMAIN_A          |  Real        |  4      |  System.Double         |  4      |  34     |         |  0      |  34     |  
|  KWH_REMAIN_B          |  Real        |  4      |  System.Double         |  4      |  35     |         |  0      |  35     |  
|  MAG_ARRAY             |  VarChar     |  128    |  System.String         |  128    |  36     |         |  0      |  36     |  
|  TSD                   |  VarChar     |  128    |  System.String         |  128    |  37     |         |  0      |  37     |  
|  GAS_PLATE             |  VarChar     |  128    |  System.String         |  128    |  38     |         |  0      |  38     |  
|  SHIELD                |  VarChar     |  128    |  System.String         |  128    |  39     |         |  0      |  39     |  
|  PHASE_ANGLE           |  VarChar     |  128    |  System.String         |  128    |  40     |         |  0      |  40     |  
|  TGT_PREC_NON          |  VarChar     |  128    |  System.String         |  128    |  41     |         |  0      |  41     |  
|  TGT_TYPE              |  VarChar     |  128    |  System.String         |  128    |  42     |         |  0      |  42     |  
|  TGT_PURP              |  VarChar     |  128    |  System.String         |  128    |  43     |         |  0      |  43     |  

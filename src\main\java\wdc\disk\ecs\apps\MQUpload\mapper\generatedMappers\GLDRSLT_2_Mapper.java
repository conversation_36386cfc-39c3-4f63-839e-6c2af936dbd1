package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.GLDRSLT_2;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for GLDRSLT_2
 */
public interface GLDRSLT_2_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.GLDRSLT AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{glidenum},",
        "    #{detectortype},",
        "    #{radius},",
        "    #{angle},",
        "    #{hits},",
        "    #{pospeak},",
        "    #{negpeak},",
        "    #{band},",
        "    #{rejcode},",
        "    #{dtinsert},",
        "    #{correlid}",
        ")) AS SOURCE (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    DETECTORTYPE,",
        "    RADIUS,",
        "    ANGLE,",
        "    HITS,",
        "    POSPEA<PERSON>,",
        "    NEGPEAK,",
        "    BAND,",
        "    REJCO<PERSON>,",
        "    DTINSERT,",
        "    CORRELID",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.GLIDENUM = SOURCE.GLIDENUM AND TARGET.DETECTORTYPE = SOURCE.DETECTORTYPE AND TARGET.RADIUS = SOURCE.RADIUS AND TARGET.ANGLE = SOURCE.ANGLE",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.HITS = SOURCE.HITS,",
        "    TARGET.POSPEAK = SOURCE.POSPEAK,",
        "    TARGET.NEGPEAK = SOURCE.NEGPEAK,",
        "    TARGET.BAND = SOURCE.BAND,",
        "    TARGET.REJCODE = SOURCE.REJCODE,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    GLIDENUM,",
        "    DETECTORTYPE,",
        "    RADIUS,",
        "    ANGLE,",
        "    HITS,",
        "    POSPEAK,",
        "    NEGPEAK,",
        "    BAND,",
        "    REJCODE,",
        "    DTINSERT,",
        "    CORRELID",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.GLIDENUM,",
        "    SOURCE.DETECTORTYPE,",
        "    SOURCE.RADIUS,",
        "    SOURCE.ANGLE,",
        "    SOURCE.HITS,",
        "    SOURCE.POSPEAK,",
        "    SOURCE.NEGPEAK,",
        "    SOURCE.BAND,",
        "    SOURCE.REJCODE,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID",
        "  )"
    })
    void insertOrUpdate(GLDRSLT_2 record);
}











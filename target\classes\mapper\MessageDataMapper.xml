<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="wdc.disk.ecs.apps.MQUpload.mapper.MessageDataMapper">
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO message_data (id, content, queue_name, process_time, status)
        VALUES 
        <foreach collection="messages" item="msg" separator=",">
            (#{msg.id}, #{msg.content}, #{msg.queueName}, #{msg.processTime}, #{msg.status})
        </foreach>
    </insert>
    
    <update id="updateStatus">
        UPDATE message_data 
        SET status = #{status}
        WHERE id = #{id}
    </update>
</mapper>
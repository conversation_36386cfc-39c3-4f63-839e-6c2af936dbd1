*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |  Y      |  0      |  1      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  2      |  Y      |  0      |  2      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  3      |  Y      |  0      |  3      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  4      |         |  0      |  4      |  
|  VARIETY               |  Char        |  14     |  System.String         |  14     |  5      |         |  0      |  5      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  7      |  Y      |  0      |  7      |  
|  LEADING_MANDRIL       |  Char        |  1      |  System.String         |  1      |  8      |         |  0      |  8      |  
|  RECIPE                |  Integer     |  4      |  System.Int32          |  4      |  9      |         |  0      |  9      |  
|  HOLD                  |  Char        |  4      |  System.String         |  4      |  10     |         |  0      |  10     |  
|  HOLD_COMMENT          |  Char        |  40     |  System.String         |  40     |  11     |         |  0      |  11     |  

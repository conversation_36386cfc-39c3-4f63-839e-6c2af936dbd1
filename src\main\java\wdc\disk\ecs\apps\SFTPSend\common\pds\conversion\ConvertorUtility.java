package wdc.disk.ecs.apps.SFTPSend.common.pds.conversion;

import java.math.BigInteger;
import java.sql.Date;

public class ConvertorUtility {
	public static String TimeStringFormat = "yyyy-MM-dd-HH.mm.ss.ffffff";
    public static double ConvertHexBytesToDouble(String bytes, Boolean reverse)
     {
	   	 if(reverse)
	   	 {
	   		 bytes = ReverseHexBytes(bytes, reverse);
	   	 }
		BigInteger bigInteger = new BigInteger(bytes, 16);
		Long lValue = bigInteger.longValue();
	  //Long lValue = Long.valueOf(bytes, 16).longValue();
		// valid (-1.79769E+308,  -2.225E-307) or (2.225E-307, 1.79769E+308)
		Double dValue = Double.longBitsToDouble(lValue);

		if(dValue > 0 && dValue <= Double.MIN_NORMAL)
			dValue = 0.0;
		else if( dValue < 0 && dValue >= -Double.MIN_NORMAL)
			dValue = 0.0;
		
		return dValue;
     }

     public static String ConvertHexBytesToString(String bytes)
     {
    	 byte[] baKeyword = new byte[bytes.length() / 2];
    	 for (int i = 0; i < baKeyword.length; i++) {
    		 baKeyword[i] = (byte) (0xff & Integer.parseInt(bytes.substring(i * 2, i * 2 + 2), 16));
    	 }
    	 String strTmp = new String(baKeyword).trim();
//    	 strTmp = strTmp.replaceAll("\n", "");
//    	 strTmp = strTmp.replaceAll("\t", "");
//    	 strTmp = strTmp.replaceAll("\r", "");
    	 return strTmp;
     }

     public static long ConvertHexBytesToLong(String bytes, Boolean reverse)
     {
    	 if(reverse)
    	 {
    		 bytes = ReverseHexBytes(bytes, reverse);
    	 }
    	 BigInteger bi = new BigInteger(bytes, 16);
    	 //int iValue = BigInteger .parseInt(bytes, 16);
    	 //return iValue;
    	 return bi.longValue();
    	
    	 //Long lValue = Long.parseLong(bytes, 16);
    	 //return lValue;
     }
     
     public static String ReverseHexBytes(String bytes, Boolean reverse)
     {
    	 if(reverse)
    	 {
	         StringBuilder strBuiler = new StringBuilder();
	         for (int i = bytes.length() - 1; i >= 1; i = i - 2)
	         {
	             String str = bytes.substring(i-1, i+1);
	             strBuiler.append(str);
	         }
	         bytes = strBuiler.toString();
    	 }
    	 return bytes;
     }

     public static int ConvertHexBytesToInteger(String bytes, Boolean reverse)
     {
    	 if(reverse)
    	 {
    		 bytes = ReverseHexBytes(bytes, reverse);
    	 }
    	 BigInteger bi = new BigInteger(bytes, 16);
    	 //int iValue = BigInteger .parseInt(bytes, 16);
    	 //return iValue;

    	 return bi.intValue();
     }
     

     public static Boolean ConvertHexBytesToBoolean(String bytes, Boolean reverse)
     {
	   	 if(reverse)
	   	 {
	   		 bytes = ReverseHexBytes(bytes, reverse);
	   	 }
       Boolean bValue = Boolean.parseBoolean(bytes);
       return bValue;
     }

     public static short ConvertHexBytesToShort(String bytes, Boolean reverse)
     {
	   	 if(reverse)
	   	 {
	   		 bytes = ReverseHexBytes(bytes, reverse);
	   	 }
   		short iValue = Short.parseShort(bytes, 16);
   		return iValue;
     }
     public static Date ConvertHexBytesToDateTime(String bytes, Boolean reverse)
     {
    	 long lValue = ConvertorUtility.ConvertHexBytesToLong(bytes, reverse); //milliseconds since January 1, 1970, 00:00:00 GMT 
  	   	return new Date(lValue*1000);
     }
     
     public static void main(java.lang.String[] args) {
     
    	 String bytes = "8152";
    	 Boolean reverse = true;
    	 //short
    	 short siValue = ConvertorUtility.ConvertHexBytesToShort(bytes, reverse); //"8152": 21121
    	 //int
    	 bytes = "f2ed2900";
    	 int iValue = ConvertorUtility.ConvertHexBytesToInteger(bytes, reverse);//"f2ed2900": 2747890
    	 //long
    	 bytes = "4591b319";
    	 long lValue = ConvertorUtility.ConvertHexBytesToLong(bytes, reverse);//"4591b319": 431198533
    	 //double
    	 bytes = "0000000000807c40";
    	 double dValue = ConvertorUtility.ConvertHexBytesToDouble(bytes, reverse);//"0000000000807c40": 456 / 6666666666660240 : 2.3
    	 bytes = "FFFFFFFFFFFFFFFF";
    	 double dValue1 = ConvertorUtility.ConvertHexBytesToDouble(bytes, reverse);
    	 if(Double.isNaN(dValue1)){
    		 dValue1 = Double.NaN;
    		 dValue1 = Double.MAX_VALUE;
    		 dValue1 = Double.MIN_VALUE;
    		 dValue1 = Double.POSITIVE_INFINITY;
    		 dValue1 = Double.NEGATIVE_INFINITY;
    		 dValue1 = Double.NaN;
    		 dValue1 = Double.MIN_NORMAL;
    		 
    	 }
    	 // valid (-1.79769E+308,  -2.225E-307) or (2.225E-307, 1.79769E+308)
    	 dValue1 = 4.9E-324;
    	 dValue1 = Double.MAX_VALUE;
    	 double dTmp = Double.MIN_VALUE;
    	 
 		if(dValue1 >= 1.79769E+308){
 			dTmp= 1.79769E+308;
		}else if(dValue1 <=  2.225E-307){
			dTmp = 0;
		}
    	 int iCompare = Double.compare(Double.MIN_VALUE, dValue1);
    	 if( iCompare < 0){
    		 
    	 }
    	 //string
    	 bytes = "4d534d44202020202020";
    	 String  strValue = ConvertorUtility.ConvertHexBytesToString(bytes);//"4d534d44202020202020": MSMD   504a3032373534393631 :PJ02754961  
       	 bytes = "504a3032373534393631";
    	 String strValue1 = ConvertorUtility.ConvertHexBytesToString(bytes);//"4d534d44202020202020": MSMD   504a3032373534393631 :PJ02754961  
    	 bytes = "20";
    	 String strValueChar = ConvertorUtility.ConvertHexBytesToString(bytes);//"20": "" 
    	 //date
     	 bytes = "9ece1061";
    	 Date dtValue = ConvertorUtility.ConvertHexBytesToDateTime(bytes, reverse);//9ece1061 : 2021-08-09-14.43.42.000000

    	 
    	 
     }

}

package wdc.disk.ecs.apps.SFTPSend;

import java.io.File;
import java.io.FilenameFilter;
import java.io.IOException;
import java.net.URL;
import java.net.URLConnection;
import java.util.Enumeration;
import java.util.StringTokenizer;
import java.util.Vector;

import javax.swing.JOptionPane;
import javax.swing.JPanel;

import ibm.disk.ecs.ecs2000.EcsApplication;
import ibm.disk.extensions.StringMethods;
import ibm.disk.utility.LogStream;
import ibm.disk.utility.UniquelyNamedFile;
import wdc.disk.ecs.apps.SFTPSend.exception.SFTPSendException;
import wdc.disk.ecs.apps.SFTPSend.thread.MonitorSftpSendRunnable;
import wdc.disk.ecs.apps.SFTPSend.util.SendMailForFailedToWritePds;

/**
 *********************************************************************
 *                                                                   *
 *  SFTPSendEcsApp	                                                 *
 *                                                                   *
 *********************************************************************
 * Sample Profile in ecs.ini:
 * MODEL {
 *    MODELKEY=SFTPSend,
 *    CLASS=wdc.disk.ecs.apps.SFTPSend.SFTPSendEcsApp,
 *    PARAMETERS=SFTPSend DEBUG,    // block name with all the parameters to start the application
 * }
 * 
 * SFTPSend {
 *	   PDS_DIR=/opt/ecs/storage/mq/sent, // File directory where PDS files are saved
 *	   SFTP_SERVER_DIR=/D:/ECS_FTP/InputFiles/POC/DECO/, // The file directory in SFTP server where PDS files will be sent
 *	   PRIMARY_SFTP_SERVER=csf-mp-talend02.ad.shared:22:sftpadm:sftp@123456,
 *	   BACKUP_SFTP_SERVER=csf-mp-talend01.ad.shared:22:sftpadm:sftp@123456,
 *	   DB_MSG_ID_PROFILE=/opt/ecs/profiles/dbmsgid.pro,
 *	   BINARY_FORMAT, //optional, default is N. N--PDS file is ascii-encoded data, Y--PDS file is binary data
 *	   MAX_READ_FILES, // optional, default is 1000. Limit the number of files JVM read one time to avoid OOM
 *	   IDLE_SEND_INTERVAL, // optional, default is 60, units-seconds. Interval time after last send in idle mode
 *	   WAITING_FOR_WRITE_ENDED, // optional, default is 10, units-seconds. The time of waiting for other ECS application writes PDS file ended
 *	   CHANGE_TO_IDLE, // optional, default is 120, units-seconds. No data to send for specified time, the app will change to idle mode
 *	   MAX_LINE_LENGTH, // optional, default is 4096. If the length of data line is more than specified length, not send to SFTP, but write to problem file
 *	   RETENTION_HOURS=144, //optional, default is 144, units-hours. The time of retaining the sent data files, default is 7 days
 * }    
 * @Date 2021-08-02 16:42:55
 * <AUTHOR>
 */
public class SFTPSendEcsApp extends ibm.disk.ecs.ecs2000.EcsApplication implements Runnable, FilenameFilter {
	private SFTPSendApp sendApp;
	private java.lang.String profileDir;
	protected transient java.beans.PropertyChangeSupport propertyChange;
	private ibm.disk.extensions.ColoredString fieldMessage = new ibm.disk.extensions.ColoredString();
	
	/**
	 * SFTPSendEcsApp constructor comment.
	 * @param ecs2000 ibm.disk.ecs.ecs2000.Ecs2000
	 * @param name java.lang.String
	 */
	public SFTPSendEcsApp(ibm.disk.ecs.ecs2000.Ecs2000 ecs2000, String name) {
		super(ecs2000, name);
	}
	
	/**
	 * SFTPSendEcsApp constructor comment.
	 * @param ecs2000 ibm.disk.ecs.ecs2000.Ecs2000
	 * @param name java.lang.String
	 * @param parameters java.lang.String
	 */
	public SFTPSendEcsApp(ibm.disk.ecs.ecs2000.Ecs2000 ecs2000, String name, String parameters) {
		super(ecs2000, name, parameters);
	}
	
	public void initApplication() {
		setVersion("[ECS][SZ_GO][Common][SFTPSend]01.00.00.01");
		if (getStartParameters() == null) {
			showError("PARAMETERS= not set for MODELKEY=" + getModelKey() + " in ecs.ini");
	   		return;
		}
		
		StringTokenizer st = new StringTokenizer(getStartParameters());
		String blockName = null;
		if (st.hasMoreTokens()) {
			blockName = st.nextToken();
		}
		if(blockName == null) {
			showError("PARAMETERS= not set for MODELKEY=" + getModelKey() + " in ecs.ini");
	   		return;
		}
		
		sendApp = new SFTPSendApp();
		String profileString = ibm.disk.ecs.ecs2000.Ecs2000.ECSPROFILEPATH + blockName + ".ini";
		profileDir = profileString;
		try {
//			sendApp.initialize(ibm.disk.ecs.ecs2000.Ecs2000.ECSPROFILE,blockName);
			sendApp.initialize(profileString,blockName);
		
		} catch (SFTPSendException e) {
			javax.swing.JOptionPane.showMessageDialog(getEcs2000(),"Failed to start SFTPSendView, " + e.getMessage(),"SFTPSendView",javax.swing.JOptionPane.ERROR_MESSAGE); 
			return;		
		}
		

		MonitorSftpSendRunnable runnable = new MonitorSftpSendRunnable(profileString, this);
		Thread monitorThread = new Thread(runnable);
		monitorThread.setName("MonitorSftpSendThread");
		monitorThread.start();
		
		
		writeEcsClassToPdsFile(getEcs2000().getEcsApplications());
	}
	
	
	/**
	 * Write the information of started ECS apps to PDS file
	 * Author: Enhua
	 * Date: 2021-08-10 16:25:55
	 * @param ecsApplications
	 */
	private void writeEcsClassToPdsFile(Vector<EcsApplication> ecsApplications) {
		sleep(5000);
		UniquelyNamedFile pdsfile = new UniquelyNamedFile(getSendApp().getPdsDirName(), 'Z');
		
		Enumeration<EcsApplication> e = ecsApplications.elements();
		while(e.hasMoreElements()) {
			EcsApplication app = e.nextElement();
			try{
				String pdsRecord = getCompileTimeStamp(app.getClass(),app.getVersion());
				boolean isAppended = pdsfile.appendToFile(pdsRecord);
				if(isAppended == false) {
					String errorMsg = "Pds can't be write. Contact IT!!!";
					JOptionPane.showMessageDialog(null, errorMsg);	
					sendApp.logAndView(LogStream.ERROR_MESSAGE, errorMsg);
					SendMailForFailedToWritePds send = 
							new SendMailForFailedToWritePds(profileDir, SendMailForFailedToWritePds.BLOCK_NAME);				
					send.sendWarningMail(pdsRecord);					
					while(true) {
						JOptionPane.showMessageDialog(null, errorMsg);											
					}
				}
			}catch(Exception ex){
				sendApp.getLog().writeExceptionStack(ex);
				sendApp.logAndView(LogStream.ERROR_MESSAGE, "Error getting EcsApplication information for " + app.getModelKey());
				int count = 0;
				while(true) {
					JOptionPane.showMessageDialog(null, "Pds can't be write. Contact IT!!! " + count++);										
				}
			}
		}
		pdsfile.closeFile();
	}
	
	/**
	 * Create PDS record
	 * Author: Enhua
	 * Date: 2021-08-10 16:21:43
	 * @param cls
	 * @return
	 * @throws IOException
	 */
	public String getCompileTimeStamp(Class cls,String version) throws IOException {
	   ClassLoader loader = cls.getClassLoader();
	   String filename = cls.getName().replace('.', '/') + ".class";
	   // Get the corresponding class file as a Resource.
	   URL resource=(loader!=null) ?
	                loader.getResource(filename) :
	                ClassLoader.getSystemResource(filename);
	   URLConnection connection = resource.openConnection();
	   // Note, we are using Connection.getLastModified not File.lastModifed.
	   // This will then work both or members of jars or standalone class files.
	   long time = connection.getLastModified();
	   String pdsRecord=null;
	   String hostname = java.net.InetAddress.getLocalHost().getHostName();
	   String temp = resource.getFile();
	   if(temp.indexOf('!') != -1){
		   temp = temp.substring(0,temp.indexOf('!'));
	   }
	   pdsRecord = StringMethods.getISOTimeStamp() + " ECSCLASSES PROD 5 *|" + hostname + "|" + cls.getName() + "|" + temp + "|" + ((time != 0L)?StringMethods.getISOTimeStamp(time) : "n") + "|" + version + "\n";
	   
	   return pdsRecord;
	}
	
	public void showError(String errMsg) {
		javax.swing.JOptionPane.showMessageDialog(null, errMsg, "SFTPSendEcsApp", javax.swing.JOptionPane.ERROR_MESSAGE);
	}


	@Override
	public boolean accept(File dir, String name) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public void run() {
		// TODO Auto-generated method stub
		
	}

	@Override
	public void exitApplication() {
		sendApp.exitApplication();
	}

	public SFTPSendApp getSendApp() {
		return sendApp;
	}

	public void setSendApp(SFTPSendApp sendApp) {
		SFTPSendApp oldSendApp = this.sendApp;
		this.sendApp = sendApp;
		firePropertyChange("sendApp", oldSendApp, sendApp);
	}
	
	/**
	 * Accessor for the propertyChange field.
	 */
	protected java.beans.PropertyChangeSupport getPropertyChange() {
		if (propertyChange == null) {
			propertyChange = new java.beans.PropertyChangeSupport(this);
		};
		return propertyChange;
	}	
	
	/**
	 * The removePropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void removePropertyChangeListener(java.beans.PropertyChangeListener listener) {
		getPropertyChange().removePropertyChangeListener(listener);
	}
	
	/**
	 * The removePropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void removePropertyChangeListener(java.lang.String propertyName, java.beans.PropertyChangeListener listener) {
		getPropertyChange().removePropertyChangeListener(propertyName, listener);
	}	

	/**
	 * The hasListeners method was generated to support the propertyChange field.
	 */
	public synchronized boolean hasListeners(java.lang.String propertyName) {
		return getPropertyChange().hasListeners(propertyName);
	}

	/**
	 * The addPropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void addPropertyChangeListener(java.beans.PropertyChangeListener listener) {
		getPropertyChange().addPropertyChangeListener(listener);
	}
	
	/**
	 * The addPropertyChangeListener method was generated to support the propertyChange field.
	 */
	public synchronized void addPropertyChangeListener(java.lang.String propertyName, java.beans.PropertyChangeListener listener) {
		getPropertyChange().addPropertyChangeListener(propertyName, listener);
	}

	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.beans.PropertyChangeEvent evt) {
		getPropertyChange().firePropertyChange(evt);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.lang.String propertyName, int oldValue, int newValue) {
		getPropertyChange().firePropertyChange(propertyName, oldValue, newValue);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.lang.String propertyName, java.lang.Object oldValue, java.lang.Object newValue) {
		getPropertyChange().firePropertyChange(propertyName, oldValue, newValue);
	}
	
	/**
	 * The firePropertyChange method was generated to support the propertyChange field.
	 */
	public void firePropertyChange(java.lang.String propertyName, boolean oldValue, boolean newValue) {
		getPropertyChange().firePropertyChange(propertyName, oldValue, newValue);
	}
}
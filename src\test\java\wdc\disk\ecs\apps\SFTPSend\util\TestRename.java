package wdc.disk.ecs.apps.SFTPSend.util;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;

import org.junit.jupiter.api.Test;


public class TestRename {
	@Test
	public void testRename() {
		File file = new File("C:\\Users\\<USER>\\Desktop\\Rename.txt");
		FileWriter fWriter;
		try {
			fWriter = new FileWriter(file);
			fWriter.append("1234\n");
//			boolean renameTo = file.renameTo(new File("C:\\Users\\<USER>\\Desktop\\Rename2.txt"));
			boolean delete = file.delete();
			fWriter.flush();
			fWriter.close();
			System.out.println("Rename to:" + delete);
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		
	}
}

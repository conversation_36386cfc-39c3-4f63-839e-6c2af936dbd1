# Queue Configuration Guide

## 1. Objective

This guide explains how to configure new message queues for the MQUpload application. This involves two main steps:

1.  Adding the queue definition to the `test-queue-config.csv` file.
2.  Configuring the consumer to listen to the new queue in the `MQ_Upload.yml` file.

## 2. Process Flow

### Step 1: Add Queue to `test-queue-config.csv`

1.  Open the `test-queue-config.csv` file located at `src/test/resources/test-queue-config.csv`.
2.  Append a new line to this file for the new queue. Do not remove the existing content. The format of the CSV is as follows:

    ```csv
    Name,Type,Exchange,ExchangeType,RoutingKey,Durable,Exclusive,AutoDelete,QuorumSize
    ```

3.  For example, to add a new queue for `GLDRSLT` with `mqMessageId` "2", you would add the following line to the end of the file:

    ```csv
    2,quorum,ecs.direct,direct,2,TRUE,FALSE,FALSE,3
    ```

### Step 2: Add Queue to `MQ_Upload.yml`

1.  Open the `MQ_Upload.yml` file located at `src/test/resources/MQ_Upload.yml`.
2.  Navigate to the `consumer.queues` section.
3.  Add a new entry for the new queue at the end of the list. The configuration for each queue should include the following parameters:

    *   `name`: The name of the queue (should match the `Name` in the CSV file).
    *   `description`: A brief description of the queue's purpose.
    *   `enabled`: Set to `true` to enable the consumer for this queue.
    *   `retry_count`: The number of times to retry processing a message in case of an error.
    *   `retry_delay`: The delay in milliseconds between retries.
    *   `processing_interval`: The interval in milliseconds between processing cycles.
    *   `prefetch_count`: The number of messages to prefetch from the queue.

4.  For example, to add the configuration for the `GLDRSLT` queue, you would add the following to the end of the `consumer.queues` list:

    ```yaml
    - name: "2"
      description: "Queue for GLDRSLT processing"
      enabled: true
      retry_count: 3
      retry_delay: 1000
      processing_interval: 1000
      prefetch_count: 100
    ```
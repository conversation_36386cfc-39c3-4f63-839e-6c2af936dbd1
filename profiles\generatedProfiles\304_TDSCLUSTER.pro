* Table: DB2SYS.TDS_CLUSTER (ASCII format)
*| ColumnName       | DBColType |DBColLen|  SysType        |BinLength|ColNo|NULLS |StartIdx  |ValueIdx| UniIdx | 
|  DTINSERT         | TIMESTAMP | 10     | System.DateTime |  0      | 0   | N    |  0       |  0     |        |
|  MEAS_TIME        | TIMESTAMP | 10     | System.DateTime |  0      | 1   | N    |  0       |  1     | Y      |
|  SPINDLE          | INTEGER   | 4      | System.Int32    |  0      | 2   | N    |  0       |  2     | Y      |
|  CHANNEL_ID       | VARCHAR   | 10     | System.String   |  0      | 3   | Y    |  0       |  3     | Y      |
|  CLUSTER_ID       | SMALLINT  | 2      | System.Int16    |  0      | 4   | Y    |  0       |  4     | Y      |
|  CLUSTER_TYPE     | VARCHAR   | 20     | System.String   |  0      | 5   | Y    |  0       |  5     |        |
|  CLUSTER_SIZE     | VARCHAR   | 20     | System.String   |  0      | 6   | Y    |  0       |  6     |        |
|  SURFACE          | CHARACTER | 1      | System.String   |  0      | 7   | Y    |  0       |  7     |        |
|  SUB_TYPE         | VARCHAR   | 25     | System.String   |  0      | 8   | Y    |  0       |  8     |        |
|  RLEN_UM          | REAL      | 4      | System.Double   |  0      | 9   | Y    |  0       |  9     |        |
|  CLEN_UM          | REAL      | 4      | System.Double   |  0      | 10  | Y    |  0       |  10    |        |
|  MID_RADIUS_MM    | REAL      | 4      | System.Double   |  0      | 11  | Y    |  0       |  11    |        |
|  MID_ANGLE_RAD    | REAL      | 4      | System.Double   |  0      | 12  | Y    |  0       |  12    |        |
|  RADIUS           | REAL      | 4      | System.Double   |  0      | 13  | Y    |  0       |  13    |        |
|  SURFACE_TDS      | VARCHAR   | 10     | System.String   |  0      | 14  | Y    |  0       |  14    |        |
|  KIND             | VARCHAR   | 20     | System.String   |  0      | 15  | Y    |  0       |  15    |        |
|  ANGLE            | REAL      | 4      | System.Double   |  0      | 16  | Y    |  0       |  16    |        |
|  WIDTH            | REAL      | 4      | System.Double   |  0      | 17  | Y    |  0       |  17    |        |
|  LENGTH           | REAL      | 4      | System.Double   |  0      | 18  | Y    |  0       |  18    |        |
|  MAX              | INTEGER   | 4      | System.Int32    |  0      | 19  | Y    |  0       |  19    |        |
|  MIN              | INTEGER   | 4      | System.Int32    |  0      | 20  | Y    |  0       |  20    |        |
|  A_MAX            | INTEGER   | 4      | System.Int32    |  0      | 21  | Y    |  0       |  21    |        |
|  RMS              | INTEGER   | 4      | System.Int32    |  0      | 22  | Y    |  0       |  22    |        |
|  ADVD             | REAL      | 4      | System.Double   |  0      | 23  | Y    |  0       |  23    |        |
|  TOTALAREA        | INTEGER   | 4      | System.Int32    |  0      | 24  | Y    |  0       |  24    |        |
|  ORI_CHANNEL      | VARCHAR   | 10     | System.String   |  0      | 25  | Y    |  0       |  25    |        |
|  MAXAMP           | REAL      | 4      | System.Double   |  0      | 26  | Y    |  0       |  26    |        |
|  MINAMP           | REAL      | 4      | System.Double   |  0      | 27  | Y    |  0       |  27    |        |

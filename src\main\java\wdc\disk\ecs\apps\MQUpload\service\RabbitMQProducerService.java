
package wdc.disk.ecs.apps.MQUpload.service;

import java.io.IOException;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeoutException;

import com.rabbitmq.client.Address;
import com.rabbitmq.client.Channel;
import com.rabbitmq.client.Connection;
import com.rabbitmq.client.ConnectionFactory;

import ibm.disk.utility.LogStream;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQProperties;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMQMetadataLoader;
import wdc.disk.ecs.apps.MQUpload.model.QueueMetadataEntry;
import wdc.disk.ecs.apps.MQUpload.model.RabbitMessage;

/**
 * Service class for handling RabbitMQ operations including connection management
 * and message publishing.
 */
public class RabbitMQProducerService {
	private static final int MAX_RETRY_ATTEMPTS = 3;
	private static final long RETRY_DELAY_MS = 1000; // 1 second delay between retries
	
	
    private final LogStream log;
    private final RabbitMQProperties rabbitMQProperties;
    private final ConnectionFactory connectionFactory;
    private Connection connection;
    private Channel channel;
    private final String queueConfigPath;
    


    public RabbitMQProducerService(RabbitMQProperties rabbitMQProperties, LogStream log) {
        this(rabbitMQProperties, log, new ConnectionFactory());
    }

    public RabbitMQProducerService(RabbitMQProperties rabbitMQProperties, LogStream log, 
            ConnectionFactory connectionFactory) {
        this.rabbitMQProperties = rabbitMQProperties;
        this.log = log;
        this.connectionFactory = connectionFactory;
        this.queueConfigPath = rabbitMQProperties.getRabbitMQQueueConfigPath();
        initialize();
    }

    private void initialize() {
        try {
            // Print out connection info
            log.write(LogStream.INFORMATION_MESSAGE, "Initializing RabbitMQ connection with the following parameters:");
            log.write(LogStream.INFORMATION_MESSAGE, "Hosts: " + String.join(", ", rabbitMQProperties.getRabbitMQHosts()));
            log.write(LogStream.INFORMATION_MESSAGE, "Port: " + rabbitMQProperties.getRabbitMQPort());
            log.write(LogStream.INFORMATION_MESSAGE, "Username: " + rabbitMQProperties.getRabbitMQUsername());
            log.write(LogStream.INFORMATION_MESSAGE, "Exchange: " + rabbitMQProperties.getRabbitMQExchange());
            // Create Address array from hosts
            Address[] addresses = new Address[rabbitMQProperties.getRabbitMQHosts().length];
            for (int i = 0; i < rabbitMQProperties.getRabbitMQHosts().length; i++) {
                addresses[i] = new Address(rabbitMQProperties.getRabbitMQHosts()[i], rabbitMQProperties.getRabbitMQPort());
            }

            // Shuffle addresses to distribute load across different starting points
            List<Address> addressList = Arrays.asList(addresses);
            Collections.shuffle(addressList);
            addresses = addressList.toArray(new Address[0]);

            // Setup connection
            //connectionFactory.setHost(rabbitMQProperties.getRabbitMQHosts());
         
            connectionFactory.setPort(rabbitMQProperties.getRabbitMQPort());
            connectionFactory.setUsername(rabbitMQProperties.getRabbitMQUsername());
            connectionFactory.setPassword(rabbitMQProperties.getRabbitMQPassword());
            connection = connectionFactory.newConnection(addresses);
            channel = connection.createChannel();
            
            // Initialize queues from config file
            RabbitMQMetadataLoader queueConfig = RabbitMQMetadataLoader.loadFromCsv(queueConfigPath);
            for (QueueMetadataEntry entry : queueConfig.getEntries()) {
                // Store in routing map using message type/id as key
                routingMap.put(entry.getRoutingKey(), entry);
                log.write(LogStream.INFORMATION_MESSAGE, "Routing Key add to map: " + entry.getRoutingKey());
            }
            //print out all RoutingKey

            log.write(LogStream.INFORMATION_MESSAGE, "Successfully connected to RabbitMQ");
        } catch (Exception e) {
            log.write(LogStream.ERROR_MESSAGE, "Failed to initialize RabbitMQ connection: " + e.getMessage());
            log.writeExceptionStack(e);
            throw new RuntimeException("Failed to initialize RabbitMQ connection", e);
        }
    }

    // Add this field to store routing configurations
    private final Map<String, QueueMetadataEntry> routingMap = new HashMap<>();

    // private void initializeQueuesFromConfig() throws IOException {
    //     RabbitMQMetadataLoader queueConfig = RabbitMQMetadataLoader.loadFromCsv(queueConfigPath);

    //      for (QueueMetadataEntry entry : queueConfig.getEntries()) {
    //         // Store in routing map using message type/id as key
    //         routingMap.put(entry.getRoutingKey(), entry);
    //      }
        
    //     for (QueueMetadataEntry entry : queueConfig.getEntries()) {
            
            
    //         // Declare exchange
    //         channel.exchangeDeclare(
    //             entry.getExchange(),
    //             entry.getExchangeType(),
    //             entry.isDurable()
    //         );
            
    //         // Prepare queue arguments
    //         Map<String, Object> arguments = new HashMap<>();
    //         if ("quorum".equals(entry.getType())) {
    //             arguments.put("x-queue-type", "quorum");  // Add this line
    //             if (entry.getQuorumSize() > 0) {
    //                 arguments.put("x-quorum-initial-group-size", entry.getQuorumSize());
    //             }
    //         }
            
    //         // Declare queue
    //         channel.queueDeclare(
    //             entry.getName(),
    //             entry.isDurable(),
    //             entry.isExclusive(),
    //             entry.isAutoDelete(),
    //             arguments
    //         );
            
    //         // Bind queue to exchange
    //         channel.queueBind(
    //             entry.getName(),
    //             entry.getExchange(),
    //             entry.getRoutingKey()
    //         );
            
    //         log.write(LogStream.INFORMATION_MESSAGE, 
    //             "Declared queue: " + entry.getName() + " with exchange: " + entry.getExchange());
    //     }
    // }

    /**
     * Uploads multiple messages to RabbitMQ with retry .
     *
     * @param messages List of RabbitMessage containing routing keys and data
     * @throws IOException if upload fails after all retry attempts
     */
    public void uploadMessage(List<RabbitMessage> messages) throws IOException {
        int attempts = 0;
        IOException lastException = null;

        while (attempts < MAX_RETRY_ATTEMPTS) {
            try {
                if (channel == null || !channel.isOpen()) {
                    log.write(LogStream.WARNING_MESSAGE, "Channel is closed, attempting to reconnect...");
                    initialize();
                }

                for (RabbitMessage message : messages) {
                    // Get routing configuration from map
                    QueueMetadataEntry routingConfig = routingMap.get(message.getRoutingKey());
                    if (routingConfig == null) {
                        throw new IOException("No routing configuration found for message routing key: " + message.getRoutingKey());
                    }

                    channel.basicPublish(
                        routingConfig.getExchange(),
                        routingConfig.getRoutingKey(),
                        null,
                        message.getData()
                    );
                    
                    log.write(LogStream.INFORMATION_MESSAGE, 
                        String.format("Successfully uploaded message with routing key: %s", 
                        routingConfig.getRoutingKey()));
                }
                return;
                
            } catch (IOException e) {
                lastException = e;
                attempts++;
                log.write(LogStream.WARNING_MESSAGE, 
                    String.format("Upload attempt %d/%d failed: %s", 
                    attempts, MAX_RETRY_ATTEMPTS, e.getMessage()));
                
                if (attempts < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new IOException("Upload interrupted during retry delay", ie);
                    }
                }
            }
        }

        log.write(LogStream.ERROR_MESSAGE, 
            String.format("Failed to upload messages after %d attempts", MAX_RETRY_ATTEMPTS));
        throw lastException;
    }

    /**
     * Closes the RabbitMQ connection and channel.
     */
    public void close() {
        try {
            if (channel != null && channel.isOpen()) {
                channel.close();
            }
            if (connection != null && connection.isOpen()) {
                connection.close();
            }
            log.write(LogStream.INFORMATION_MESSAGE, "RabbitMQ connection closed");
        } catch (IOException | TimeoutException e) {
            log.write(LogStream.ERROR_MESSAGE, "Error closing RabbitMQ connection: " + e.getMessage());
        }
    }

    
     
}

#!/bin/bash

# Set application variables
APP_HOME=/opt/MQUpload
CONFIG_PROFILE=${APP_HOME}/config/MQ_Upload.yml
LOG_FILE=${APP_HOME}/logs/mqconsumer.log
CLASSPATH=${APP_HOME}/target/SFTPSend-1.0-SNAPSHOT.jar:/opt/ecs/debug/*

# Create necessary directories if they don't exist
mkdir -p "${APP_HOME}/logs"
mkdir -p "${APP_HOME}/config"

# Run application
echo "Starting RabbitMQConsumer application..."
java -Dconfig.block=MQUpload_test -Drabbitmq.block=RabbitMQ_Configurations -Dconsumer.block=Consumer_test \
     -cp "${CLASSPATH}" wdc.disk.ecs.apps.MQUpload.consumer.RabbitMQConsumer "${CONFIG_PROFILE}" "${LOG_FILE}"
package wdc.disk.ecs.apps.MQUpload.model.generatedModels;



/**
 * Generated POJO for TDSCLUSDATA
 */
public class TDSCLUSDATA  {
    private java.time.LocalDateTime dtinsert;
    private java.time.LocalDateTime measTime;
    private Integer spindle;
    private String channelId;
    private Short clusterId;
    private Double radiusMm;
    private Double angleRad;

    // Default constructor
    public TDSCLUSDATA() {
    }

    public java.time.LocalDateTime getDtinsert() {
        return dtinsert;
    }

    public void setDtinsert(java.time.LocalDateTime dtinsert) {
        this.dtinsert = dtinsert;
    }

    public java.time.LocalDateTime getMeasTime() {
        return measTime;
    }

    public void setMeasTime(java.time.LocalDateTime measTime) {
        this.measTime = measTime;
    }

    public Integer getSpindle() {
        return spindle;
    }

    public void setSpindle(Integer spindle) {
        this.spindle = spindle;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public Short getClusterId() {
        return clusterId;
    }

    public void setClusterId(Short clusterId) {
        this.clusterId = clusterId;
    }

    public Double getRadiusMm() {
        return radiusMm;
    }

    public void setRadiusMm(Double radiusMm) {
        this.radiusMm = radiusMm;
    }

    public Double getAngleRad() {
        return angleRad;
    }

    public void setAngleRad(Double angleRad) {
        this.angleRad = angleRad;
    }

}



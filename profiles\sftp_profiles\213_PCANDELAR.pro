*| ColumnName            |  DBColumnType  |  DBColumnLength  |  SystemType            |  BinaryLength  |  ColumnNo  |  NULLS  |  StartIndex  |  ValueIndex  |  
|  DTINSERT              |  Timestamp   |  4      |  System.DateTime       |  4      |  0      |  Y      |  0      |  0      |  
|  DT_ENTRY              |  Timestamp   |  4      |  System.DateTime       |  4      |  1      |         |  0      |  1      |  
|  LOT                   |  Char        |  10     |  System.String         |  10     |  2      |         |  0      |  2      |  
|  CASSETTE              |  Char        |  20     |  System.String         |  20     |  3      |         |  0      |  3      |  
|  LPROCRES              |  Char        |  10     |  System.String         |  10     |  4      |         |  0      |  4      |  
|  RAIL                  |  Char        |  1      |  System.String         |  1      |  5      |         |  0      |  5      |  
|  RESOURCE              |  Char        |  10     |  System.String         |  10     |  6      |         |  0      |  6      |  
|  PRODUCT               |  Char        |  6      |  System.String         |  6      |  7      |         |  0      |  7      |  
|  XID                   |  Char        |  10     |  System.String         |  10     |  8      |         |  0      |  8      |  
|  DISKSEQ               |  Char        |  2      |  System.String         |  2      |  9      |         |  0      |  9      |  
|  SIDE                  |  Char        |  1      |  System.String         |  1      |  10     |         |  0      |  10     |  
|  MEAS_TIME             |  Timestamp   |  4      |  System.DateTime       |  4      |  11     |         |  0      |  11     |  
|  STATUS                |  Char        |  10     |  System.String         |  10     |  12     |         |  0      |  12     |  
|  TEXTURI               |  Char        |  200    |  System.String         |  200    |  13     |         |  0      |  13     |  

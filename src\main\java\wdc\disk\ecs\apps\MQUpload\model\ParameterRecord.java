package wdc.disk.ecs.apps.MQUpload.model;

/**
 * Represents a configuration parameter record with key-value pair
 */
public class ParameterRecord {
    private final String key;
    private final String value;

    public ParameterRecord(String key, String value) {
        this.key = key;
        this.value = value;
    }

    /**
     * Get the parameter key
     * @return parameter key
     */
    public String getKey() {
        return key;
    }

    /**
     * Get the parameter value
     * @return parameter value
     */
    public String getValue() {
        return value;
    }

    @Override
    public String toString() {
        return key + "=" + value;
    }
}
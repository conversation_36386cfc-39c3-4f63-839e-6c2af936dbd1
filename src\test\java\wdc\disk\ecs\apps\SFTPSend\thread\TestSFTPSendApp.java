package wdc.disk.ecs.apps.SFTPSend.thread;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import java.util.Vector;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;
import ibm.disk.profiles.DiskProfile;
import ibm.disk.profiles.ProfileException;
import wdc.disk.ecs.apps.SFTPSend.SFTPSendApp;
import wdc.disk.ecs.apps.SFTPSend.profile.DBMsgIDRecord;

public class TestSFTPSendApp {

    private SFTPSendApp sftpSendApp;

    @Mock
    private DiskProfile dbMsgIDProfile;  // Mock the profile

    private String dbMsgIDProfileName = "C:\\Users\\<USER>\\Downloads\\pdsMsgId.pro"; // test profile name

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this); // Initialize Mockito mocks
        sftpSendApp = new SFTPSendApp();
        sftpSendApp.setDbMsgIDProfile(dbMsgIDProfile);
        sftpSendApp.setDbMsgIDProfileName(dbMsgIDProfileName);
    }

    @Test
    void getDBMsgID_validData_returnsDBMsgID() throws ProfileException {
        // Arrange
        String data = "2021-07-31-15.01.19.000000 LID PROD 9 * /B883C526236/**********/PCDJ  /DOUBLE        /BY48Z     /LID4001   /0.4/1.1/1.3";
        DBMsgIDRecord mockRecord = new DBMsgIDRecord(data);
        mockRecord.setDBMsgID("EXPECTED_DB_MSG_ID");
        mockRecord.setTableName("EXPECTED_TABLE_NAME");

        when(dbMsgIDProfile.getRecord(eq(0), any(Vector.class))).thenReturn(mockRecord);

        // Act
        String dbMsgID = sftpSendApp.getDBMsgID(data);

        // Assert
        assertEquals("EXPECTED_DB_MSG_ID", dbMsgID);
        verify(dbMsgIDProfile, times(1)).getRecord(eq(0), any(Vector.class));
    }

    @Test
    void getDBMsgID_nullData_returnsEmptyString() {
        // Arrange
        String data = null;

        // Act
        String dbMsgID = sftpSendApp.getDBMsgID(data);

        // Assert
        assertEquals("", dbMsgID);
    }

     @Test
    void getDBMsgID_lessThanThreeTokens_returnsNull() {
        // Arrange
        String data = "2021-07-31-15.01.19.000000 LID";

        // Act
        String dbMsgID = sftpSendApp.getDBMsgID(data);

        // Assert
        assertNull(dbMsgID);
    }


    @Test
    void getDBMsgID_profileException_returnsEmptyString() throws ProfileException {
        // Arrange
        String data = "2021-07-31-15.01.19.000000 LID PROD 9 * /B883C526236/**********/PCDJ  /DOUBLE        /BY48Z     /LID4001   /0.4/1.1/1.3";
        when(dbMsgIDProfile.getRecord(eq(0), any(Vector.class))).thenThrow(new ProfileException("Simulated Profile Exception"));

        // Act
        String dbMsgID = sftpSendApp.getDBMsgID(data);

        // Assert
        assertEquals("", dbMsgID); // Expect empty string due to exception handling.
    }

     @Test
    void getDBMsgID_noMatchingRecord_returnsEmptyString() throws ProfileException {
        // Arrange
        String data = "2021-07-31-15.01.19.000000 LID PROD 9 * /B883C526236/**********/PCDJ  /DOUBLE        /BY48Z     /LID4001   /0.4/1.1/1.3";
        when(dbMsgIDProfile.getRecord(eq(0), any(Vector.class))).thenReturn(null);

        // Act
        String dbMsgID = sftpSendApp.getDBMsgID(data);

        // Assert
        assertEquals("", dbMsgID); // Expect empty string.  The method logs an error, but doesn't throw an exception.
    }

}
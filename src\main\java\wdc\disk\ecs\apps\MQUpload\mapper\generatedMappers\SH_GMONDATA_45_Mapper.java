package wdc.disk.ecs.apps.MQUpload.mapper.generatedMappers;

import wdc.disk.ecs.apps.MQUpload.model.generatedModels.SH_GMONDATA_45;
import org.apache.ibatis.annotations.*;

/**
 * Generated Mapper Interface for SH_GMONDATA_45
 */
public interface SH_GMONDATA_45_Mapper {
    
    @Insert({
        "MERGE INTO DB2SYS.SH_GMONDATA AS TARGET",
        "USING (VALUES (",
        "    #{spindle},",
        "    #{diskid},",
        "    #{batchtime},",
        "    #{surface},",
        "    #{rule},",
        "    #{glideheadid},",
        "    #{hcf},",
        "    #{method},",
        "    #{amp},",
        "    #{dev},",
        "    #{glidehits},",
        "    #{bumphcf},",
        "    #{qcode},",
        "    #{recsent},",
        "    #{dtinsert},",
        "    #{correlid},",
        "    #{armIdent}",
        ")) AS SOURCE (",
        "    <PERSON>IND<PERSON>,",
        "    DISKID,",
        "    BATCHTIME,",
        "    SURFACE,",
        "    RULE,",
        "    GLIDEHEADID,",
        "    HCF,",
        "    METHOD,",
        "    AMP,",
        "    DEV,",
        "    GLIDEHITS,",
        "    BUMPHCF,",
        "    QCODE,",
        "    RECSENT,",
        "    DTINSERT,",
        "    CORRELID,",
        "    ARM_IDENT",
        ")",
        "ON TARGET.SPINDLE = SOURCE.SPINDLE AND TARGET.DISKID = SOURCE.DISKID AND TARGET.BATCHTIME = SOURCE.BATCHTIME AND TARGET.SURFACE = SOURCE.SURFACE AND TARGET.RULE = SOURCE.RULE AND TARGET.GLIDEHEADID = SOURCE.GLIDEHEADID",
        "WHEN MATCHED THEN",
        "  UPDATE SET",
        "    TARGET.HCF = SOURCE.HCF,",
        "    TARGET.METHOD = SOURCE.METHOD,",
        "    TARGET.AMP = SOURCE.AMP,",
        "    TARGET.DEV = SOURCE.DEV,",
        "    TARGET.GLIDEHITS = SOURCE.GLIDEHITS,",
        "    TARGET.BUMPHCF = SOURCE.BUMPHCF,",
        "    TARGET.QCODE = SOURCE.QCODE,",
        "    TARGET.RECSENT = SOURCE.RECSENT,",
        "    TARGET.DTINSERT = SOURCE.DTINSERT,",
        "    TARGET.CORRELID = SOURCE.CORRELID,",
        "    TARGET.ARM_IDENT = SOURCE.ARM_IDENT",
        "WHEN NOT MATCHED THEN",
        "  INSERT (",
        "    SPINDLE,",
        "    DISKID,",
        "    BATCHTIME,",
        "    SURFACE,",
        "    RULE,",
        "    GLIDEHEADID,",
        "    HCF,",
        "    METHOD,",
        "    AMP,",
        "    DEV,",
        "    GLIDEHITS,",
        "    BUMPHCF,",
        "    QCODE,",
        "    RECSENT,",
        "    DTINSERT,",
        "    CORRELID,",
        "    ARM_IDENT",
        "  )",
        "  VALUES (",
        "    SOURCE.SPINDLE,",
        "    SOURCE.DISKID,",
        "    SOURCE.BATCHTIME,",
        "    SOURCE.SURFACE,",
        "    SOURCE.RULE,",
        "    SOURCE.GLIDEHEADID,",
        "    SOURCE.HCF,",
        "    SOURCE.METHOD,",
        "    SOURCE.AMP,",
        "    SOURCE.DEV,",
        "    SOURCE.GLIDEHITS,",
        "    SOURCE.BUMPHCF,",
        "    SOURCE.QCODE,",
        "    SOURCE.RECSENT,",
        "    SOURCE.DTINSERT,",
        "    SOURCE.CORRELID,",
        "    SOURCE.ARM_IDENT",
        "  )"
    })
    void insertOrUpdate(SH_GMONDATA_45 record);
}











# Default environment-related paths (based on DEV profile)
mq_storage_root: 'C:\opt\ecs\storage\mq'
app_root: 'C:\Development\TFS_SourceCode\ECS\SZ_GO\Common\Dev\SFTPSend_Hao'

# Producer Configuration (shared across all environments)
producer:
  pds_dir:
    base: mq_storage_root
    relative: ''
  binary_format: true
  # File processor configurations
  max_files_in_sending: 10
  waiting_for_write_ended: 100
  db_msg_id_profile:
    base: app_root
    relative: 'profiles\pdsMsgId.pro'
  monitor_interval: 10
  housekeeping:
      enabled: false
      interval_hours: 24
      retention_days: 30
      directories:
        - sent
        - problem

# Default RabbitMQ configuration (based on DEV profile)
rabbitmq:
  host: "csf-md-rabitmq1.ad.shared"
  port: 5672
  username: "mqupload"
  password: "upload66"
  exchange: "ecs.direct"
  queue_config_path:
    base: app_root
    relative: 'config\test-queue-config.csv'

table_schema:
  message_mappings_path:
    base: app_root
    relative: 'config\message_mappings.json'
  profile_base_path:
    base: app_root
    relative: 'profiles\generatedProfiles'